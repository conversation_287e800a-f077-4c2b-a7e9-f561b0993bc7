<template>
  <button
    v-if="!readonly"
    class="thumbnail-toggle-btn"
    @click="toggleThumbnail"
    :title="showThumbnail ? '隐藏缩略图' : '显示缩略图'"
  >
    <span class="toggle-icon">{{ showThumbnail ? '◱' : '◰' }}</span>
  </button>
</template>

<script lang="ts">
import { defineComponent } from 'vue'

export default defineComponent({
  name: 'CanvasThumbnailControl',
  props: {
    readonly: {
      type: Boolean,
      default: false
    },
    showThumbnail: {
      type: Boolean,
      default: true
    }
  },
  setup(props, { emit }) {
    const toggleThumbnail = () => {
      emit('toggle-thumbnail')
    }

    return {
      toggleThumbnail
    }
  }
})
</script>

<style lang="scss" scoped>
.thumbnail-toggle-btn {
  position: absolute;
  bottom: 20px;
  right: 250px;
  width: 30px;
  height: 30px;
  background-color: rgba(255, 255, 255, 0.95);
  color: #333;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 16px;
  z-index: 100;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 3px 12px rgba(0, 0, 0, 0.15), 0 0 1px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;

  .toggle-icon {
    color: rgba(0, 0, 0, 0.6);
  }

  &:hover {
    background-color: rgba(255, 255, 255, 1);
    box-shadow: 0 4px 14px rgba(0, 0, 0, 0.2), 0 0 1px rgba(0, 0, 0, 0.1);
  }
}
</style>
