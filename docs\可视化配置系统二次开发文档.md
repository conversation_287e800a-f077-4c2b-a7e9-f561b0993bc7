# 可视化配置系统二次开发文档

## 1. 系统概述

可视化配置系统是一个基于Vue 3的前端应用，用于创建和管理节点关系图，支持节点参数配置、节点连接计算关系、自动布局等功能。系统提供了直观的可视化界面，使用户能够轻松创建、编辑和管理复杂的节点关系网络，特别适用于业务指标分析、数据流程建模和决策支持系统。

## 2. 技术架构

### 2.1 前端框架
- **Vue 3.5.13**: 采用组合式API（Composition API）开发，提供更好的代码组织和类型推断
- **TypeScript**: 提供静态类型检查，增强代码可维护性
- **Vite 6.3.4**: 现代前端构建工具，提供快速的开发体验和高效的构建过程

### 2.2 状态管理
- **Pinia 3.0.2**: Vue官方推荐的状态管理库，替代Vuex，提供更简洁的API和更好的TypeScript支持

### 2.3 路由
- **Vue Router 4.5.1**: Vue官方路由管理器，支持历史模式导航

### 2.4 样式
- **SCSS/SASS 1.87.0**: CSS预处理器，提供变量、嵌套、混合等高级功能

### 2.5 工具库
- **UUID 11.1.0**: 用于生成唯一标识符，确保节点和连线的唯一性

## 3. 项目结构

```
visual-config-system/
├── public/                 # 静态资源
├── src/                    # 源代码
│   ├── assets/             # 资源文件（图片、字体等）
│   ├── components/         # 组件
│   │   ├── AttributionPopup/   # 归因分析弹窗组件
│   │   ├── Canvas/         # 画布组件
│   │   ├── CanvasThumbnail/ # 画布缩略图组件
│   │   ├── ConfigPanel/    # 配置面板组件
│   │   ├── Edge/           # 连线组件
│   │   ├── IndicatorSelector/ # 指标选择器组件
│   │   ├── Node/           # 节点组件
│   │   └── RelationTypeSelector/ # 关系类型选择器组件
│   ├── stores/             # Pinia状态管理
│   │   ├── canvas.ts       # 画布状态
│   │   ├── edge.ts         # 连线状态
│   │   └── node.ts         # 节点状态
│   ├── types/              # TypeScript类型定义
│   ├── views/              # 页面视图
│   │   ├── Editor.vue      # 编辑器页面
│   │   └── Preview.vue     # 预览页面
│   ├── App.vue             # 根组件
│   ├── main.ts             # 入口文件
│   ├── router.ts           # 路由配置
│   └── style.css           # 全局样式
├── index.html              # HTML模板
├── tsconfig.json           # TypeScript配置
├── vite.config.ts          # Vite配置
└── package.json            # 项目依赖和脚本
```

## 4. 核心数据模型

### 4.1 节点（Node）

```typescript
interface Node {
  id: string;              // 节点唯一标识符
  type: string;            // 节点类型（kpi, input, output, process）
  position: {              // 节点位置
    x: number;
    y: number;
  };
  size?: {                 // 节点尺寸
    width: number;
    height: number;
  };
  data: {                  // 节点数据
    label: string;         // 节点标签
    value: string;         // 节点值
    change: string;        // 变化趋势
    period: string;        // 统计周期
    color?: string;        // 节点颜色
    borderStyle?: string;  // 边框样式
    borderWidth?: number;  // 边框宽度
    borderRadius?: number; // 边框圆角
    textColor?: string;    // 文本颜色
    backgroundColor?: string; // 背景颜色
    hasAttribution?: boolean; // 是否有归因分析
    attributionData?: AttributionItem[]; // 归因分析数据
    [key: string]: any;    // 其他自定义属性
  };
}
```

### 4.2 连线（Edge）

```typescript
interface Edge {
  id: string;              // 连线唯一标识符
  source: string;          // 源节点ID
  target: string;          // 目标节点ID
  label?: string;          // 连线标签
  type?: string;           // 连线类型（add, subtract, multiply, divide, related）
  color?: string;          // 连线颜色
  lineStyle?: string;      // 线条样式
  [key: string]: any;      // 其他自定义属性
}
```

### 4.3 归因项（AttributionItem）

```typescript
interface AttributionItem {
  id: string;              // 归因项唯一标识符
  name: string;            // 归因项名称
  value: number;           // 归因项值
  percentage: number;      // 归因项百分比
  contribution: number;    // 归因项贡献度
}
```

## 5. 核心模块与组件

### 5.1 画布模块（Canvas）

画布模块是系统的核心，负责节点和连线的渲染、交互和布局。

#### 5.1.1 主要功能
- 节点渲染与交互
- 连线渲染与交互
- 画布缩放与平移
- 自动布局算法
- 拖拽操作处理
- 连线创建与管理
- 缩略图显示

#### 5.1.2 关键API

```typescript
// 选择节点
function selectNode(id: string): void

// 移动节点
function moveNode(id: string, position: Position): void

// 开始连接节点
function startConnecting(sourceId: string): void

// 完成节点连接
function finishConnecting(targetId: string): void

// 应用自动布局
function applyAutoLayout(force: boolean = false): void

// 缩放画布
function zoomCanvas(delta: number, center: Position): void

// 平移画布
function panCanvas(deltaX: number, deltaY: number): void
```

### 5.2 节点模块（Node）

节点模块负责单个节点的渲染、交互和数据管理。

#### 5.2.1 主要功能
- 节点渲染
- 节点拖拽
- 节点连接
- 节点样式配置
- 归因分析

#### 5.2.2 关键API

```typescript
// 节点点击事件
function onNodeClick(): void

// 节点拖拽事件
function onNodeMouseDown(event: MouseEvent): void

// 显示归因分析弹窗
function showAttributionPopup(): void

// 选择计算关系
function selectRelation(relation: Relation): void
```

### 5.3 连线模块（Edge）

连线模块负责节点间连线的渲染、交互和数据管理。

#### 5.3.1 主要功能
- 连线渲染
- 连线交互
- 计算关系显示
- 连线样式配置

#### 5.3.2 关键API

```typescript
// 获取连线路径
function getConnectorPath(): string

// 获取目标路径
function getTargetPath(): string

// 计算符号点击事件
function onSymbolClick(event: MouseEvent): void

// 连线点击事件
function onArrowClick(): void
```

### 5.4 配置面板模块（ConfigPanel）

配置面板模块负责节点和连线属性的配置和编辑。

#### 5.4.1 主要功能
- 节点基础属性配置
- 节点样式配置
- 节点数据配置
- 节点高级配置
- 连线属性配置
- 归因分析配置

#### 5.4.2 关键API

```typescript
// 更新节点标签
function updateNodeLabel(): void

// 更新节点类型
function updateNodeType(): void

// 更新节点颜色
function updateNodeColor(color: string): void

// 更新连线标签
function updateEdgeLabel(): void

// 更新连线类型
function updateEdgeType(): void

// 删除节点
function removeNode(): void

// 删除连线
function removeEdge(): void
```

### 5.5 画布缩略图模块（CanvasThumbnail）

画布缩略图模块负责显示画布的缩略视图，帮助用户了解当前视口在整个画布中的位置。

#### 5.5.1 主要功能
- 画布缩略视图显示
- 视口位置指示
- 快速导航

#### 5.5.2 关键API

```typescript
// 计算缩略图缩放比例
function thumbnailScale(): number

// 获取连线路径
function getEdgePath(edge: Edge): string

// 获取节点颜色
function getNodeColor(node: Node): string
```

### 5.6 归因分析弹窗模块（AttributionPopup）

归因分析弹窗模块负责显示节点的归因分析数据。

#### 5.6.1 主要功能
- 归因数据显示
- 归因比例可视化

#### 5.6.2 关键API

```typescript
// 关闭弹窗
function close(): void
```

### 5.7 关系类型选择器模块（RelationTypeSelector）

关系类型选择器模块负责节点间关系类型的选择和管理。

#### 5.7.1 主要功能
- 关系类型选择
- 关系图标显示

#### 5.7.2 关键API

```typescript
// 选择关系类型
function selectRelation(relation: Relation): void
```

### 5.8 指标选择器模块（IndicatorSelector）

指标选择器模块负责指标的搜索、选择和管理。

#### 5.8.1 主要功能
- 指标搜索
- 指标选择
- 指标分类管理

#### 5.8.2 关键API

```typescript
// 过滤指标
function filteredIndicators(): Indicator[]

// 选择指标
function selectIndicator(indicator: Indicator): void
```

## 6. 状态管理

系统使用Pinia进行状态管理，主要包括以下三个Store：

### 6.1 节点状态（NodeStore）

```typescript
// 节点状态
const nodeStore = defineStore('node', {
  state: () => ({
    nodes: [],                // 所有节点
    selectedNodeId: null      // 当前选中的节点ID
  }),

  getters: {
    // 根据ID获取节点
    getNodeById: (state) => (id) => state.nodes.find(node => node.id === id) || null,
    // 获取当前选中的节点
    selectedNode: (state) => state.nodes.find(node => node.id === state.selectedNodeId) || null
  },

  actions: {
    addNode(node) { /* 添加节点 */ },
    updateNode(id, updates) { /* 更新节点 */ },
    moveNode(id, position) { /* 移动节点 */ },
    removeNode(id) { /* 删除节点 */ },
    selectNode(id) { /* 选择节点 */ },
    clearNodes() { /* 清空所有节点 */ },
    addNodes(nodes) { /* 批量添加节点 */ }
  }
})
```

### 6.2 连线状态（EdgeStore）

```typescript
// 连线状态
const edgeStore = defineStore('edge', {
  state: () => ({
    edges: [],                // 所有连线
    selectedEdgeId: null      // 当前选中的连线ID
  }),

  getters: {
    // 根据ID获取连线
    getEdgeById: (state) => (id) => state.edges.find(edge => edge.id === id) || null,
    // 获取指定源节点的所有连线
    getEdgesBySource: (state) => (sourceId) => state.edges.filter(edge => edge.source === sourceId),
    // 获取指定目标节点的所有连线
    getEdgesByTarget: (state) => (targetId) => state.edges.filter(edge => edge.target === targetId),
    // 获取当前选中的连线
    selectedEdge: (state) => state.edges.find(edge => edge.id === state.selectedEdgeId) || null
  },

  actions: {
    addEdge(edge) { /* 添加连线 */ },
    updateEdge(id, updates) { /* 更新连线 */ },
    removeEdge(id) { /* 删除连线 */ },
    selectEdge(id) { /* 选择连线 */ },
    removeEdgesConnectedToNode(nodeId) { /* 删除与指定节点相关的所有连线 */ }
  }
})
```

### 6.3 画布状态（CanvasStore）

```typescript
// 画布状态
const canvasStore = defineStore('canvas', {
  state: () => ({
    scale: 1,                 // 画布缩放比例
    position: { x: 0, y: 0 }, // 画布位置
    dimensions: {             // 画布尺寸
      width: 5000,
      height: 5000
    }
  }),

  actions: {
    setScale(scale) { /* 设置缩放比例 */ },
    setPosition(position) { /* 设置位置 */ },
    setDimensions(dimensions) { /* 设置尺寸 */ },
    zoom(delta, center) { /* 缩放画布 */ },
    pan(deltaX, deltaY) { /* 平移画布 */ }
  }
})
```

## 7. 自动布局算法

系统实现了一套复杂的自动布局算法，确保节点在画布中的合理分布。

### 7.1 布局原则

1. **单子节点对齐**: 当节点只有一个子节点时，子节点与父节点水平中心对齐
2. **多子节点对称分布**: 当节点有多个子节点时，子节点对称分布，与父节点形成局部对称效果
3. **避免节点重叠**: 新增节点时，确保不与现有节点重叠
4. **水平间距固定**: 子节点与父节点之间保持固定的水平间距
5. **垂直间距层级区分**: 父节点与叔叔节点（同级节点）之间的垂直间距大于子节点之间的垂直间距

### 7.2 算法实现

```typescript
// 应用自动布局
function applyAutoLayout(force = false) {
  // 如果自动布局被禁用且不是强制执行，则直接返回
  if (!autoLayoutEnabled.value && !force) return

  // 强制执行时，重新启用自动布局
  if (force) {
    autoLayoutEnabled.value = true
  }

  // 构建节点关系树
  const { nodeMap, rootNodes } = buildNodeTree()

  // 全局协调布局
  coordinateGlobalLayout(nodeMap, rootNodes)
}

// 构建节点关系树
function buildNodeTree() {
  const nodeMap = new Map()

  // 初始化节点映射
  nodes.value.forEach(node => {
    nodeMap.set(node.id, {
      node,
      children: [],
      parent: null,
      depth: 0,
      siblingIndex: 0,
      hasSingleChild: false,
      totalSiblings: 0,
      parentGroup: '',
      siblingNodes: []
    })
  })

  // 构建父子关系
  edges.value.forEach(edge => {
    const sourceInfo = nodeMap.get(edge.source)
    const targetInfo = nodeMap.get(edge.target)

    if (sourceInfo && targetInfo) {
      sourceInfo.children.push(edge.target)
      targetInfo.parent = edge.source
    }
  })

  // 找出根节点
  const rootNodes = []
  nodeMap.forEach((info, nodeId) => {
    if (!info.parent) {
      rootNodes.push(nodeId)
    }
  })

  return { nodeMap, rootNodes }
}
```

## 8. 系统功能与交互

### 8.1 编辑模式

编辑模式是系统的主要工作模式，提供完整的节点和连线编辑功能。

#### 8.1.1 节点操作
- **创建节点**: 点击画布空白区域创建新节点
- **选择节点**: 点击节点选中，显示配置面板
- **移动节点**: 拖拽节点改变位置
- **删除节点**: 在配置面板中点击删除按钮
- **配置节点**: 在右侧配置面板中编辑节点属性

#### 8.1.2 连线操作
- **创建连线**: 从源节点的出口拖拽到目标节点的入口
- **选择连线**: 点击连线选中，显示配置面板
- **配置连线**: 在右侧配置面板中编辑连线属性
- **删除连线**: 在配置面板中点击删除按钮
- **修改计算关系**: 点击连线上的计算符号，选择关系类型

#### 8.1.3 画布操作
- **缩放画布**: 鼠标滚轮缩放
- **平移画布**: 拖拽画布背景
- **自动布局**: 点击工具栏中的自动布局按钮
- **查看缩略图**: 右下角显示画布缩略图，可快速导航

### 8.2 预览模式

预览模式用于查看和分析已配置的节点关系图，不允许编辑操作。

#### 8.2.1 查看功能
- **浏览节点**: 查看节点数据和关系
- **缩放平移**: 调整视图
- **归因分析**: 点击节点查看归因分析弹窗

## 9. 计算关系类型

系统支持多种节点间的计算关系类型：

### 9.1 相加关系（add）
- 符号：`+`
- 描述：目标节点值是源节点值的相加结果

### 9.2 相减关系（subtract）
- 符号：`-`
- 描述：目标节点值是源节点值的相减结果

### 9.3 相乘关系（multiply）
- 符号：`×`
- 描述：目标节点值是源节点值的相乘结果

### 9.4 相除关系（divide）
- 符号：`÷`
- 描述：目标节点值是源节点值的相除结果

### 9.5 相关关系（related）
- 符号：`C`
- 描述：目标节点与源节点存在相关性，但没有具体的计算关系

## 10. 二次开发指南

### 10.1 环境搭建

1. **安装依赖**
   ```bash
   # 使用pnpm（推荐）
   pnpm install

   # 或使用npm
   npm install
   ```

2. **启动开发服务器**
   ```bash
   # 使用pnpm
   pnpm dev

   # 或使用npm
   npm run dev
   ```

### 10.2 扩展节点类型

1. 在`src/types/index.ts`中扩展节点类型定义
   ```typescript
   // 添加新的节点类型
   export type NodeType = 'kpi' | 'input' | 'output' | 'process' | 'custom';
   ```

2. 在`src/components/Node/Node.vue`中添加新节点类型的样式和行为
   ```typescript
   // 根据节点类型应用不同的样式
   const nodeStyle = computed(() => {
     const style: any = { ... };

     if (props.node.type === 'custom') {
       style.backgroundColor = '#f0f5ff';
       style.borderLeftColor = '#722ed1';
     }

     return style;
   });
   ```

3. 在`src/components/ConfigPanel/ConfigPanel.vue`中添加新节点类型的配置选项
   ```typescript
   // 节点类型选项
   const nodeTypeOptions = [
     { value: 'kpi', label: 'KPI指标' },
     { value: 'input', label: '输入指标' },
     { value: 'output', label: '输出指标' },
     { value: 'process', label: '处理指标' },
     { value: 'custom', label: '自定义指标' } // 添加新的节点类型选项
   ];
   ```

### 10.3 扩展关系类型

1. 在`src/components/RelationTypeSelector/RelationTypeSelector.vue`中添加新的关系类型
   ```typescript
   const relationTypes = [
     { id: 'add', name: '相加关系', icon: '+' },
     { id: 'subtract', name: '相减关系', icon: '-' },
     { id: 'multiply', name: '相乘关系', icon: '×' },
     { id: 'divide', name: '相除关系', icon: '÷' },
     { id: 'related', name: '相关关系', icon: 'C' },
     { id: 'custom', name: '自定义关系', icon: '?' } // 添加新的关系类型
   ];
   ```

2. 在`src/components/Edge/Edge.vue`中添加新关系类型的渲染逻辑
   ```typescript
   // 根据关系类型设置不同的样式
   const edgeStyle = computed(() => {
     const style: any = { ... };

     if (props.edge.type === 'custom') {
       style.stroke = '#722ed1';
       style.strokeDasharray = '5,2';
     }

     return style;
   });
   ```

### 10.4 自定义样式

1. 修改全局样式变量（`src/style.css`）
   ```css
   :root {
     --primary-color: #1890ff;
     --secondary-color: #722ed1;
     --success-color: #52c41a;
     --warning-color: #faad14;
     --error-color: #f5222d;
     --border-radius: 4px;
     --node-width: 240px;
     --node-height: 90px;
   }
   ```

2. 添加新的颜色选项（`src/components/ConfigPanel/ConfigPanel.vue`）
   ```typescript
   const colorOptions = [
     { value: '#1890ff', label: '蓝色' },
     { value: '#52c41a', label: '绿色' },
     { value: '#faad14', label: '黄色' },
     { value: '#f5222d', label: '红色' },
     { value: '#722ed1', label: '紫色' }, // 添加新的颜色选项
     { value: '#eb2f96', label: '粉色' }  // 添加新的颜色选项
   ];
   ```

## 11. 性能优化

系统实现了多项性能优化措施，确保在处理大量节点和连线时保持流畅的用户体验：

### 11.1 计算属性缓存
- 使用Vue的计算属性缓存复杂计算结果
- 只在依赖变化时重新计算

### 11.2 事件节流与防抖
- 对频繁触发的事件（如鼠标移动、滚轮缩放）应用节流和防抖技术
- 减少不必要的计算和渲染

### 11.3 布局算法优化
- 使用增量更新而非全量重新计算
- 只在必要时应用自动布局
- 拖拽节点时暂时禁用自动布局

## 12. 部署与维护

### 12.1 构建生产版本
```bash
# 使用pnpm
pnpm build

# 或使用npm
npm run build
```

### 12.2 部署方式

#### 12.2.1 传统Web服务器（Nginx/Apache）
1. 构建项目：`pnpm build`
2. 将`dist`目录中的文件上传到Web服务器
3. 配置服务器路由，将所有请求重定向到index.html

#### 12.2.2 Docker容器
1. 创建Dockerfile：
   ```dockerfile
   FROM nginx:alpine
   COPY dist/ /usr/share/nginx/html/
   COPY nginx.conf /etc/nginx/conf.d/default.conf
   EXPOSE 80
   CMD ["nginx", "-g", "daemon off;"]
   ```

2. 构建并运行Docker容器：
   ```bash
   docker build -t visual-config-system .
   docker run -d -p 80:80 visual-config-system
   ```

## 13. API参考

系统提供了一套完整的API，用于与系统进行交互、获取数据和控制系统行为。

### 13.1 节点管理API

#### 获取所有节点
```typescript
function getNodes(): Node[]
```
描述：获取画布中的所有节点。

#### 获取指定节点
```typescript
function getNodeById(id: string): Node | null
```
描述：根据ID获取指定节点。

#### 添加节点
```typescript
function addNode(node: Node): string
```
描述：添加新节点到画布，返回新节点的ID。

#### 更新节点
```typescript
function updateNode(id: string, updates: Partial<Node>): void
```
描述：更新指定节点的属性。

#### 移动节点
```typescript
function moveNode(id: string, position: Position): void
```
描述：移动指定节点到新位置。

#### 删除节点
```typescript
function removeNode(id: string): void
```
描述：删除指定节点。

### 13.2 连线管理API

#### 获取所有连线
```typescript
function getEdges(): Edge[]
```
描述：获取画布中的所有连线。

#### 获取指定连线
```typescript
function getEdgeById(id: string): Edge | null
```
描述：根据ID获取指定连线。

#### 添加连线
```typescript
function addEdge(edge: Edge): string
```
描述：添加新连线到画布，返回新连线的ID。

#### 更新连线
```typescript
function updateEdge(id: string, updates: Partial<Edge>): void
```
描述：更新指定连线的属性。

#### 删除连线
```typescript
function removeEdge(id: string): void
```
描述：删除指定连线。

### 13.3 画布操作API

#### 缩放画布
```typescript
function zoomCanvas(delta: number, center: Position): void
```
描述：缩放画布，delta为缩放量，center为缩放中心点。

#### 平移画布
```typescript
function panCanvas(deltaX: number, deltaY: number): void
```
描述：平移画布，deltaX和deltaY为水平和垂直平移量。

#### 应用自动布局
```typescript
function applyAutoLayout(forceLayout: boolean = false): void
```
描述：应用自动布局算法，调整节点位置。

### 13.4 事件监听API

#### 节点选择事件
```typescript
function onNodeSelect(callback: (nodeId: string | null) => void): () => void
```
描述：监听节点选择事件，返回取消监听的函数。

#### 节点更新事件
```typescript
function onNodeUpdate(callback: (nodeId: string, updates: Partial<Node>) => void): () => void
```
描述：监听节点更新事件，返回取消监听的函数。

#### 连线选择事件
```typescript
function onEdgeSelect(callback: (edgeId: string | null) => void): () => void
```
描述：监听连线选择事件，返回取消监听的函数。

#### 画布缩放事件
```typescript
function onCanvasZoom(callback: (scale: number) => void): () => void
```
描述：监听画布缩放事件，返回取消监听的函数。

## 14. 常见问题与解决方案

### 14.1 节点重叠问题
**问题**：在添加多个节点后，节点可能会出现重叠。
**解决方案**：调用`applyAutoLayout(true)`强制应用自动布局算法，确保节点合理分布。

### 14.2 连线显示异常
**问题**：连线路径显示异常或不可见。
**解决方案**：检查源节点和目标节点的位置关系，确保它们之间有足够的距离。可以调整`Edge.vue`中的路径计算逻辑。

### 14.3 性能问题
**问题**：当节点和连线数量较多时，系统性能下降。
**解决方案**：
- 实现虚拟滚动，只渲染可见区域的节点和连线
- 优化自动布局算法，减少不必要的计算
- 使用Web Worker处理复杂计算，避免阻塞主线程

### 14.4 样式自定义问题
**问题**：需要自定义节点和连线的样式。
**解决方案**：
- 修改`Node.vue`和`Edge.vue`中的样式定义
- 在`ConfigPanel.vue`中添加更多样式配置选项
- 使用CSS变量实现全局样式定制

## 15. 未来扩展方向

系统设计考虑了未来扩展的可能性，主要扩展方向包括：

1. **更多节点类型**：支持更多类型的节点，如图表节点、表格节点等
2. **更多关系类型**：支持更复杂的节点关系类型
3. **数据导入导出**：支持从外部系统导入数据，以及导出配置结果
4. **协作功能**：支持多用户同时编辑同一配置
5. **历史记录与回滚**：支持操作历史记录和回滚功能
6. **移动端适配**：优化移动设备上的用户体验
7. **国际化支持**：添加多语言支持
8. **主题定制**：支持深色模式和自定义主题

## 16. 结论

可视化配置系统是一个功能强大、易于使用的节点关系图编辑工具，通过Vue 3和TypeScript的结合，提供了良好的开发体验和用户体验。系统的核心自动布局算法确保了节点在画布中的合理分布，为用户提供了直观、高效的配置体验。

本文档详细介绍了系统的技术架构、核心组件、关键算法和API接口，旨在帮助开发者理解系统设计和实现细节，便于二次开发和功能扩展。通过遵循本文档提供的指南，开发者可以轻松地扩展系统功能，满足特定业务需求。