<template>
  <div class="app-container">
    <main class="app-content">
      <router-view />
    </main>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue'

export default defineComponent({
  name: 'App'
})
</script>

<style lang="scss">
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html, body {
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  font-size: 14px;
  color: #333;
  background-color: #f5f7fa;
}

#app {
  height: 100vh;
}

.app-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.app-content {
  flex: 1;
  overflow: hidden;
}

button {
  cursor: pointer;
  border: none;
  outline: none;
  background-color: transparent;
  
  &.btn-primary {
    background-color: #1890ff;
    color: white;
    padding: 8px 16px;
    border-radius: 4px;
    font-weight: 500;
    
    &:hover {
      background-color: #40a9ff;
    }
  }
  
  &.btn-default {
    background-color: #fff;
    color: #666;
    padding: 8px 16px;
    border-radius: 4px;
    border: 1px solid #d9d9d9;
    
    &:hover {
      color: #40a9ff;
      border-color: #40a9ff;
    }
  }
}

.header-bar {
  height: 56px;
  background-color: #fff;
  border-bottom: 1px solid #e8e8e8;
  display: flex;
  align-items: center;
  padding: 0 16px;
  justify-content: space-between;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  
  .left-section {
    display: flex;
    align-items: center;
    
    .back-btn {
      display: flex;
      align-items: center;
      color: #666;
      margin-right: 16px;
      
      &:hover {
        color: #1890ff;
      }
    }
    
    .title {
      font-size: 16px;
      font-weight: 500;
      color: #333;
      margin-right: 8px;
    }
    
    .edit-icon {
      color: #999;
      cursor: pointer;
      
      &:hover {
        color: #1890ff;
      }
    }
  }
  
  .right-section {
    display: flex;
    align-items: center;
    
    button {
      margin-left: 8px;
    }
  }
}

.toolbar {
  height: 48px;
  background-color: #fff;
  border-bottom: 1px solid #e8e8e8;
  display: flex;
  align-items: center;
  padding: 0 16px;
  
  .date-picker {
    display: flex;
    align-items: center;
    margin-right: 16px;
    
    .label {
      color: #666;
      margin-right: 8px;
    }
    
    .date-input {
      border: 1px solid #d9d9d9;
      border-radius: 4px;
      padding: 4px 8px;
      width: 120px;
    }
  }
}

.node {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  border-left: 4px solid #1890ff;
  
  &.selected {
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.3);
  }
  
  .node-header {
    padding: 8px 12px;
    font-size: 12px;
    color: #666;
    border-bottom: 1px solid #f0f0f0;
  }
  
  .node-content {
    padding: 12px;
    
    .value {
      font-size: 20px;
      font-weight: 500;
      margin-bottom: 4px;
    }
    
    .change {
      font-size: 12px;
      
      &.positive {
        color: #52c41a;
      }
      
      &.negative {
        color: #f5222d;
      }
      
      .period {
        color: #999;
        margin-left: 4px;
      }
    }
  }
}

.config-panel {
  background-color: #fff;
  border-left: 1px solid #e8e8e8;
  
  .panel-header {
    padding: 16px;
    border-bottom: 1px solid #f0f0f0;
    font-weight: 500;
  }
  
  .panel-content {
    padding: 16px;
    
    .form-group {
      margin-bottom: 16px;
      
      .label {
        display: block;
        margin-bottom: 8px;
        color: #666;
      }
      
      .input, .select {
        width: 100%;
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        padding: 8px;
        
        &:focus {
          border-color: #40a9ff;
          outline: none;
          box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
      }
    }
  }
}
</style>
