// 位置类型
export interface Position {
  x: number
  y: number
}

// 尺寸类型
export interface Size {
  width: number
  height: number
}

// 节点数据类型
export interface NodeData {
  label?: string
  value?: string
  change?: string
  period?: string
  color?: string
  calculationPeriod?: string
  compareIndicator?: boolean
  borderStyle?: string
  borderWidth?: number
  borderRadius?: number
  textColor?: string
  backgroundColor?: string
  fontSize?: number
  fontWeight?: string
  boxShadow?: string
  dataSource?: string
  formula?: string
  // 归因分析相关属性
  hasAttribution?: boolean
  attributionData?: AttributionItem[]
  [key: string]: any
}

// 归因项类型
export interface AttributionItem {
  id: string
  name: string
  value: number
  percentage: number
  contribution: number
}

// 节点类型
export interface Node {
  id: string
  type: string
  position: Position
  size?: Size
  data: NodeData
  isDragging?: boolean
}

// 连线类型
export interface Edge {
  id: string
  source: string
  target: string
  label?: string
  type?: string
  color?: string
  lineStyle?: string
  [key: string]: any
}

// 节点状态类型
export interface NodeState {
  nodes: Node[]
  selectedNodeId: string | null
}

// 连线状态类型
export interface EdgeState {
  edges: Edge[]
  selectedEdgeId: string | null
}

// 画布状态类型
export interface CanvasState {
  position: Position
  scale: number
  dimensions: {
    width: number
    height: number
  }
}

// 指标类型
export interface Indicator {
  id: string
  label: string
  value: string
  change: string
  period: string
  color?: string
  type?: string
}

// 关系类型
export interface Relation {
  id: string
  name: string
  icon: string
}