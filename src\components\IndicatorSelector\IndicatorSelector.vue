<template>
  <div class="indicator-selector">
    <div class="selector-header">
      <div class="search-box">
        <input 
          type="text" 
          class="search-input" 
          placeholder="搜索指标..." 
          v-model="searchText"
        />
        <span class="search-icon">🔍</span>
      </div>
    </div>
    
    <div class="selector-content">
      <div class="category-title">全部指标 ({{ indicators.length }})</div>
      <div class="indicator-list">
        <div 
          v-for="indicator in filteredIndicators" 
          :key="indicator.id"
          class="indicator-item"
          @click="selectIndicator(indicator)"
        >
          <span class="indicator-dot"></span>
          <span class="indicator-name">{{ indicator.label }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed } from 'vue'

export default defineComponent({
  name: 'IndicatorSelector',
  props: {
    sourceNodeId: {
      type: String,
      required: true
    },
    relationType: {
      type: Object,
      required: true
    }
  },
  emits: ['select'],
  setup(props, { emit }) {
    const searchText = ref('')
    
    // 模拟指标数据
    const indicators = [
      { id: 'sales', label: '零售价销售额', value: '49,194.473916', change: '+2.71%', period: '对比上周', color: '#1890ff' },
      { id: 'discount', label: '优惠金额', value: '4,935', change: '+4.82%', period: '对比上周', color: '#52c41a' },
      { id: 'net_sales', label: '净销售额', value: '7,169.215255', change: '+2.88%', period: '对比上周', color: '#fa8c16' },
      { id: 'cost', label: '成本', value: '4,103.173492', change: '+0.27%', period: '对比上周', color: '#722ed1' },
      { id: 'profit', label: '毛利', value: '41,727.718241', change: '-0.15%', period: '对比上周', color: '#1890ff' },
      { id: 'profit_rate', label: '毛利率', value: '4,156.677496', change: '+0.16%', period: '对比上周', color: '#52c41a' }
    ]
    
    // 过滤指标
    const filteredIndicators = computed(() => {
      if (!searchText.value) return indicators
      
      return indicators.filter(indicator => 
        indicator.label.toLowerCase().includes(searchText.value.toLowerCase())
      )
    })
    
    // 选择指标
    const selectIndicator = (indicator: any) => {
      emit('select', {
        sourceNodeId: props.sourceNodeId,
        relationType: props.relationType,
        indicator
      })
    }
    
    return {
      searchText,
      indicators,
      filteredIndicators,
      selectIndicator
    }
  }
})
</script>

<style lang="scss" scoped>
.indicator-selector {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  
  .selector-header {
    padding: 12px;
    border-bottom: 1px solid #f0f0f0;
    
    .search-box {
      position: relative;
      
      .search-input {
        width: 100%;
        padding: 8px 12px 8px 32px;
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        font-size: 14px;
        
        &:focus {
          outline: none;
          border-color: #1890ff;
          box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
      }
      
      .search-icon {
        position: absolute;
        left: 10px;
        top: 50%;
        transform: translateY(-50%);
        color: #999;
        font-size: 14px;
      }
    }
  }
  
  .selector-content {
    flex: 1;
    overflow-y: auto;
    padding: 0 12px;
    
    .category-title {
      padding: 12px 0;
      font-size: 14px;
      font-weight: 500;
      color: #333;
    }
    
    .indicator-list {
      .indicator-item {
        display: flex;
        align-items: center;
        padding: 10px 12px;
        border-radius: 4px;
        cursor: pointer;
        
        &:hover {
          background-color: #f5f5f5;
        }
        
        .indicator-dot {
          width: 8px;
          height: 8px;
          border-radius: 50%;
          background-color: #1890ff;
          margin-right: 12px;
        }
        
        .indicator-name {
          font-size: 14px;
          color: #333;
        }
      }
    }
  }
}
</style>