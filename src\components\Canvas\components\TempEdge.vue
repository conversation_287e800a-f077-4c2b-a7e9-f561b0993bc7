<template>
  <svg class="temp-edge">
    <path
      :d="path"
      stroke="#1890ff"
      stroke-width="1.5"
      fill="none"
      stroke-dasharray="5,5"
    />
  </svg>
</template>

<script lang="ts">
import { defineComponent } from 'vue'

export default defineComponent({
  name: 'TempEdge',
  props: {
    path: {
      type: String,
      required: true
    }
  }
})
</script>

<style lang="scss" scoped>
.temp-edge {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}
</style>
