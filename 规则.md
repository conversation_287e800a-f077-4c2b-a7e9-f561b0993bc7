自动布局计算逻辑，当次新增节点时，需要全局考量，要求所有节点遵循下面7个原则：

原则1、如果节点没有子节点，这个节点新增子节点时，子节点默认和父节点水平方向中心对齐，后续父节点位移时，子节点也始终和父节点保持水平方向中心对齐；

原则2、所有子节点与父节点之间的左右间距是默认固定的，任意节点之间是不重叠的。

原则3、终端节点定义：如果某个父节点下的所有子节点，都没有下级子节点，那么这些子节点是终端节点。
终端节点要求：同一个父节点下的所有终端子节点之间，纵向均匀排列，间距固定，左右对齐。

原则4、对称效果：所有终端子节点与他们同一个父节点之间，在布局上总是形成水平轴线方向局部对称效果；所有父节点与祖父节点也是需要形成对称效果，以此类推，从所有的终端节点，延申到父节点，祖父节点，都要遵循子节点与父节点的对称效果。参考截图所示：![alt text](image.png)

原则5、在遵守原则1-4的前提下，每次新增子节点时，需要全局计算，增大该子节点的父节点与所有叔节点之间的间距，在该父节点新增子节点时，由于叔父节点间距增大，该父节点的同级叔叔节点，处于父节点位置上方的叔叔节点及其子孙节点整体往上移，下方的叔叔节点及其子孙节点整体下移。由于间距增大导致的某个节点位移时，该节点下的所有子节点、孙节点，保持原有的相对位置关系，跟随父节点一起发生整体位移。

原则6、将原则5的间距变化和位移，进一步传导至祖父节点乃至根节点。

补充细节：

7、终端子节点之间的间距是是固定值。
