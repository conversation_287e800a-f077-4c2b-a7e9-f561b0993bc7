<template>
  <svg class="edge" :class="{ selected, highlighted, readonly }">
    <!-- Calculation Symbol Group -->
    <g
      class="relation-symbol"
      :transform="`translate(${symbolPosition.x - 15}, ${symbolPosition.y - 15})`"
      @click="onSymbolClick"
    >
      <rect
        width="30"
        height="30"
        rx="4"
        fill="white"
        :stroke="getSymbolStrokeColor()"
        :stroke-width="selected || highlighted ? 1.5 : 1"
      />
      <text
        x="15"
        y="15"
        text-anchor="middle"
        dominant-baseline="middle"
        font-size="16"
        font-weight="bold"
        :fill="getSymbolTextColor()"
      >
        {{ edge.label || 'C' }}
      </text>
    </g>

    <!-- Arrow Group (Connector + Target Path) -->
    <g class="arrow-group" @click.stop="onArrowClick">
       <!-- Path from symbol to connector end (solid) -->
      <path
        :d="getConnectorPath()"
        :stroke="getArrowStrokeColor()"
        :stroke-width="selected || highlighted ? 2 : 1.5"
        fill="none"
        class="arrow-path connector-part"
      />
      <!-- Path from connector end to target (dashed curve) -->
      <path
        :d="getTargetPath()"
        :stroke="getArrowStrokeColor()"
        :stroke-width="selected || highlighted ? 2 : 1.5"
        fill="none"
        :stroke-dasharray="highlighted ? '5,3' : '3,3'"
        class="arrow-path target-part"
        :marker-end="getArrowMarker()"
      />
       <!-- Hitbox for the entire arrow section -->
      <path
        :d="getArrowHitboxPath()"
        stroke="transparent"
        stroke-width="12"
        fill="none"
        class="arrow-hitbox"
        @mouseenter="isHoveringArrow = true"
        @mouseleave="isHoveringArrow = false"
      />
    </g>

    <!-- Arrowhead Definitions -->
    <defs>
      <!-- Default Arrowhead -->
      <marker
        id="arrowhead-default"
        viewBox="0 0 10 10"
        refX="8"
        refY="5"
        markerWidth="6"
        markerHeight="6"
        orient="auto-start-reverse"
      >
        <path d="M 0 0 L 10 5 L 0 10 z" fill="#c1c7d0" />
      </marker>
      <!-- Active/Hover Arrowhead -->
      <marker
        id="arrowhead-active"
        viewBox="0 0 10 10"
        refX="8"
        refY="5"
        markerWidth="8"
        markerHeight="8"
        orient="auto-start-reverse"
      >
        <path d="M 0 0 L 10 5 L 0 10 z" fill="#1890ff" />
      </marker>
      <!-- Highlighted Arrowhead -->
      <marker
        id="arrowhead-highlighted"
        viewBox="0 0 10 10"
        refX="8"
        refY="5"
        markerWidth="8"
        markerHeight="8"
        orient="auto-start-reverse"
      >
        <path d="M 0 0 L 10 5 L 0 10 z" fill="#52c41a" />
      </marker>
    </defs>
  </svg>
</template>

<script lang="ts">
import { defineComponent, computed, ref } from 'vue'
import type { Edge, Node, Position } from '../../types'

export default defineComponent({
  name: 'Edge',
  props: {
    edge: {
      type: Object as () => Edge,
      required: true
    },
    sourceNode: {
      type: Object as () => Node | null,
      required: true
    },
    targetNode: {
      type: Object as () => Node | null,
      required: true
    },
    selected: {
      type: Boolean,
      default: false
    },
    readonly: {
      type: Boolean,
      default: false
    },
    highlighted: {
      type: Boolean,
      default: false
    }
  },
  emits: ['select', 'symbol-click', 'connector-click'],
  setup(props, { emit }) {
    const isHoveringArrow = ref(false)

    // Calculate connection points
    const sourcePoint = computed(() => {
      if (!props.sourceNode) return { x: 0, y: 0 }
      return {
        x: props.sourceNode.position.x + (props.sourceNode.size?.width || 240),
        y: props.sourceNode.position.y + (props.sourceNode.size?.height || 90) / 2
      }
    })

    const targetPoint = computed(() => {
      if (!props.targetNode) return { x: 0, y: 0 }
      return {
        x: props.targetNode.position.x,
        y: props.targetNode.position.y + (props.targetNode.size?.height || 90) / 2
      }
    })

    // Calculate symbol position - 调整源节点到计算符号的距离
    const symbolPosition = computed(() => {
      if (!props.sourceNode) return { x: 0, y: 0 }
      return {
        x: sourcePoint.value.x + 60, // 适当增加距离，从50增加到60，使符号与源节点之间的实线更长
        y: sourcePoint.value.y
      }
    })

    // Calculate connector end point - 调整计算符号到连接点的距离
    const connectorEndPoint = computed(() => {
       if (!props.sourceNode) return { x: 0, y: 0 }
      return {
        x: symbolPosition.value.x + 60, // 适当增加距离，从50增加到60，使连接点与符号之间的实线更长
        y: symbolPosition.value.y
      }
    })



    // Path from source node to symbol end and then to connector end point
    const getConnectorPath = () => {
      if (!props.sourceNode) return ''
      // 从父节点到计算符号的实线
      const sourceStart = sourcePoint.value
      const symbolStart = { x: symbolPosition.value.x - 15, y: symbolPosition.value.y }
      const symbolEnd = { x: symbolPosition.value.x + 15, y: symbolPosition.value.y }
      const connectorEnd = connectorEndPoint.value
      // 绘制从父节点到计算符号，再到连接点的实线
      return `M ${sourceStart.x} ${sourceStart.y} L ${symbolStart.x} ${symbolStart.y} M ${symbolEnd.x} ${symbolEnd.y} L ${connectorEnd.x} ${connectorEnd.y}`
    }

    // Curved path from connector end point to target node
    const getTargetPath = () => {
      // 如果是临时连线（没有实际的目标节点），只返回一个短的水平线段
      if (props.edge.isTemporary) {
        const start = connectorEndPoint.value
        // 创建一个短的水平线段，长度为30px
        return `M ${start.x} ${start.y} L ${start.x + 30} ${start.y}`
      }

      if (!props.targetNode) return ''
      const start = connectorEndPoint.value
      const end = targetPoint.value

      // 确保虚线始终从实线尾端右侧开始
      // 检查目标节点是否在源节点左侧
      if (end.x <= start.x) {
        // 如果目标节点在源节点左侧，强制将目标连接点移到源节点右侧
        // 这样可以确保虚线始终从实线尾端右侧开始
        const adjustedEnd = {
          x: start.x + 30, // 减少强制目标点的距离，从50px减少到30px，使虚线更短
          y: end.y
        }

        // 使用直线连接到调整后的点，然后使用曲线连接到实际目标点
        const midPoint = {
          x: adjustedEnd.x,
          y: adjustedEnd.y
        }

        // 使用三段曲线：从起点到中间点的直线，从中间点到目标的曲线
        // 适当调整控制点的水平偏移和垂直弯曲，使曲线更自然
        const midY = (midPoint.y + end.y) / 2;
        return `M ${start.x} ${start.y} L ${midPoint.x} ${midPoint.y} Q ${midPoint.x + 30} ${midPoint.y}, ${midPoint.x + 30} ${midY} T ${end.x} ${end.y}`
      }

      // 正常情况：目标节点在源节点右侧
      const dx = Math.abs(end.x - start.x)
      const dy = end.y - start.y

      // 检查源节点和目标节点是否在同一水平线上（允许小误差）
      const sameLevel = Math.abs(dy) < 5

      // 如果源节点和目标节点在同一水平线上，使用直线或简单曲线
      if (sameLevel) {
        // 如果水平距离极小，使用直线
        if (dx < 30) {
          return `M ${start.x} ${start.y} L ${end.x} ${end.y}`
        }

        // 使用简单的贝塞尔曲线，适当弯曲，使虚线有自然的弯曲形状
        const offsetX = Math.min(dx * 0.4, 60) // 降低水平偏移系数，使曲线弯曲度更自然

        const controlPoint1 = {
          x: start.x + offsetX,
          y: start.y
        }

        const controlPoint2 = {
          x: end.x - offsetX,
          y: end.y
        }

        return `M ${start.x} ${start.y} C ${controlPoint1.x} ${controlPoint1.y}, ${controlPoint2.x} ${controlPoint2.y}, ${end.x} ${end.y}`
      }

      // 不在同一水平线上的情况
      // 计算合适的控制点，确保曲线自然且不会产生奇怪的弯曲

      // 基础水平偏移量 - 调整水平偏移，使虚线曲线有适当的弯曲度
      let offsetX = Math.min(dx * 0.4, 70) // 降低水平偏移系数，使曲线弯曲度更自然

      // 确保最小水平偏移
      offsetX = Math.max(offsetX, 50) // 降低最小水平偏移，使曲线不会过度弯曲

      // 垂直曲线因子 - 控制曲线的垂直弯曲程度
      let verticalCurveFactor = 0.4 // 降低垂直弯曲程度，使曲线形状更自然

      // 根据垂直距离调整参数
      if (Math.abs(dy) > 200) {
        // 垂直距离大时，适当增加水平偏移，但降低垂直弯曲度
        offsetX = Math.min(dx * 0.4, 80)
        verticalCurveFactor = 0.3 // 降低垂直弯曲度，使曲线更自然
      } else if (Math.abs(dy) < 50) {
        // 垂直距离小时，降低垂直弯曲度
        verticalCurveFactor = 0.2 // 降低垂直弯曲度，使曲线更自然
      }

      // 创建控制点
      const controlPoint1 = {
        x: start.x + offsetX,
        y: start.y + (dy * verticalCurveFactor)
      }

      const controlPoint2 = {
        x: end.x - offsetX,
        y: end.y - (dy * verticalCurveFactor)
      }

      // 使用三次贝塞尔曲线创建平滑路径
      return `M ${start.x} ${start.y} C ${controlPoint1.x} ${controlPoint1.y}, ${controlPoint2.x} ${controlPoint2.y}, ${end.x} ${end.y}`
    }

     // Combined path for the arrow hitbox
    const getArrowHitboxPath = () => {
      // Combine connector and target paths for a single hitbox
      const connector = getConnectorPath()
      const target = getTargetPath().replace(/^M[\s\d.-]+/, 'L') // Replace M with L for continuous path
      return `${connector} ${target}`
    }

    // Emit event when calculation symbol is clicked
    const onSymbolClick = (event: MouseEvent) => {
      // 如果是只读模式，不处理点击事件
      if (props.readonly) return

      // 阻止事件冒泡
      event.stopPropagation()

      // 传递边的ID、计算符号的精确位置和原始DOM事件
      emit('symbol-click', props.edge.id, {
        x: symbolPosition.value.x,
        y: symbolPosition.value.y,
        domEvent: event,
        symbolSize: 30 // 符号的大小，用于精确定位弹窗
      })
    }

    // Emit select event when the arrow part is clicked
    const onArrowClick = () => {
      // 如果是只读模式，不处理点击事件
      if (props.readonly) return

      // 首先发出 select 事件选中边
      emit('select', props.edge.id)

      // 然后发出 connector-click 事件，用于创建子节点
      // 计算连接器位置作为参考点
      const position: Position = {
        x: connectorEndPoint.value.x,
        y: connectorEndPoint.value.y
      }
      emit('connector-click', props.edge.id, position)
    }

    // 获取符号边框颜色
    const getSymbolStrokeColor = () => {
      if (props.highlighted) return '#52c41a' // 高亮状态使用绿色
      if (props.selected) return '#1890ff' // 选中状态使用蓝色
      return '#e8e8e8' // 默认状态使用浅灰色
    }

    // 获取符号文本颜色
    const getSymbolTextColor = () => {
      if (props.highlighted) return '#52c41a' // 高亮状态使用绿色
      if (props.selected) return '#1890ff' // 选中状态使用蓝色
      return '#666' // 默认状态使用深灰色
    }

    // 获取箭头线条颜色
    const getArrowStrokeColor = () => {
      if (props.highlighted) return '#52c41a' // 高亮状态使用绿色
      if (props.selected || isHoveringArrow.value) return '#1890ff' // 选中或悬停状态使用蓝色
      return '#c1c7d0' // 默认状态使用浅灰色
    }

    // 获取箭头标记
    const getArrowMarker = () => {
      if (props.highlighted) return 'url(#arrowhead-highlighted)' // 高亮状态使用绿色箭头
      if (props.selected || isHoveringArrow.value) return 'url(#arrowhead-active)' // 选中或悬停状态使用蓝色箭头
      return 'url(#arrowhead-default)' // 默认状态使用灰色箭头
    }

    return {
      symbolPosition,
      connectorEndPoint,
      isHoveringArrow,
      getConnectorPath,
      getTargetPath,
      getArrowHitboxPath,
      getSymbolStrokeColor,
      getSymbolTextColor,
      getArrowStrokeColor,
      getArrowMarker,
      onSymbolClick,
      onArrowClick
    }
  }
})
</script>

<style lang="scss" scoped>
.edge {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;

  .relation-symbol {
    pointer-events: all;
    cursor: pointer;
    transition: transform 0.1s ease-out;

    rect {
      transition: stroke 0.2s ease, fill 0.2s ease;
    }
    text {
      transition: fill 0.2s ease;
      user-select: none;
    }
  }

  .arrow-group {
     pointer-events: none; // Group itself doesn't capture events
  }

  .arrow-path {
    transition: stroke 0.2s ease;
  }

  .arrow-hitbox {
    pointer-events: all; // Hitbox captures events for the arrow group
    cursor: pointer;
  }

  &.selected {
    z-index: 10;

    .arrow-path { // Highlight path when edge is selected
       stroke: #1890ff;
    }

    .relation-symbol {
      filter: drop-shadow(0 0 3px rgba(24, 144, 255, 0.3));
    }
  }

  &.highlighted {
    z-index: 15; // 高于选中状态

    .arrow-path {
      stroke: #52c41a;
      animation: dash 1s linear infinite;
    }

    .relation-symbol {
      filter: drop-shadow(0 0 3px rgba(82, 196, 26, 0.3));
      animation: pulse 1.5s ease-in-out infinite;
    }
  }

  @keyframes dash {
    to {
      stroke-dashoffset: -16;
    }
  }

  @keyframes pulse {
    0% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.1);
    }
    100% {
      transform: scale(1);
    }
  }
}
</style>