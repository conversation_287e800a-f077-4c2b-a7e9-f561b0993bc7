<template>
  <div
    ref="nodeRef"
    class="node"
    :class="{
      selected: selected,
      [`node-type-${node.type}`]: true,
      'hovered': isHovered,
      'dragging': isDragging
    }"
    :style="getNodeStyle()"
    @mousedown="onNodeMouseDown"
    @click.stop="onNodeClick"
    @contextmenu.prevent.stop="onNodeRightClick"
    @mouseenter="isHovered = true"
    @mouseleave="isHovered = false"
  >
    <div class="node-header" :style="{
        color: node.data.textColor || '#666',
        fontSize: node.data.fontSize ? `${node.data.fontSize - 2}px` : '12px'
      }">
      {{ node.data.label }}
    </div>
    <div class="node-content">
      <div class="value" :style="{
        color: node.data.textColor || '#333',
        fontSize: node.data.fontSize ? `${node.data.fontSize}px` : '20px',
        fontWeight: node.data.fontWeight || '500'
      }">{{ node.data.value }}</div>
      <div
        class="change"
        :class="{
          'positive': node.data.change && node.data.change.includes('+'),
          'negative': node.data.change && node.data.change.includes('-')
        }"
      >
        {{ node.data.change }}
        <span class="period">{{ node.data.period }}</span>
      </div>

      <!-- 归因分析指示器 - 仅在预览模式且有归因数据时显示 -->
      <div
        v-if="readonly && node.data.hasAttribution"
        class="attribution-indicator"
        @click.stop="showAttributionPopup"
      >
        <span class="indicator-icon">📊</span>
        <span class="indicator-text">查看归因分析</span>
      </div>
    </div>

    <!-- 右侧操作按钮 - 选中或悬停时显示 -->
    <div
      v-if="(selected || isHovered) && !readonly"
      class="node-actions"
    >
      <div class="action-wrapper">
        <button
          class="action-btn add-relation-btn"
          @click.stop="handleAddRelationBtnClick"
        >
          <span>+</span>
        </button>

        <!-- 计算关系菜单 -->
        <div
          v-if="showRelationMenu"
          class="relation-menu"
          @click.stop
        >
        <button
          v-for="relation in relationTypes"
          :key="relation.id"
          class="relation-item"
          @click.stop="selectRelation(relation)"
        >
          <span class="relation-icon">{{ relation.icon }}</span>
          <span class="relation-name">{{ relation.name }}</span>
        </button>
      </div>
      </div>
    </div>

    <!-- 连接点 -->
    <div
      v-if="!readonly"
      class="node-port node-port-out"
      @mousedown.stop="onPortMouseDown"
    ></div>

    <div
      v-if="!readonly"
      class="node-port node-port-in"
      @mouseup.stop="onPortMouseUp"
    ></div>
  </div>

  <!-- 归因分析弹窗 -->
  <teleport to="body">
    <AttributionPopup
      v-if="showAttribution"
      :visible="showAttribution"
      :title="node.data.label + ' 归因分析'"
      :attribution-data="node.data.attributionData"
      @close="showAttribution = false"
    />
  </teleport>

  <!-- 右键菜单 -->
  <ContextMenu
    :visible="showContextMenu"
    :position="contextMenuPosition"
    @copy="handleCopyNode"
    @edit="handleEditNode"
    @delete="handleDeleteNode"
    @add-relation="handleAddRelation"
  />
</template>

<script lang="ts">
import { defineComponent, computed, ref, watch, onUnmounted } from 'vue'
import type { Node, Position } from '../../types'
import AttributionPopup from '../AttributionPopup/AttributionPopup.vue'
import ContextMenu from '../ContextMenu/ContextMenu.vue'

export default defineComponent({
  name: 'Node',
  components: {
    AttributionPopup,
    ContextMenu
  },
  props: {
    node: {
      type: Object as () => Node,
      required: true
    },
    selected: {
      type: Boolean,
      default: false
    },
    readonly: {
      type: Boolean,
      default: false
    }
  },
  emits: ['select', 'move', 'connect-start', 'connect-end', 'add-relation', 'show-attribution', 'drag-end', 'drag-start', 'copy-node', 'delete-node'],
  setup(props, { emit }) {
    // 拖拽状态
    const isDragging = ref(false)
    const dragOffset = ref<Position>({ x: 0, y: 0 })

    // 悬停状态
    const isHovered = ref(false)

    // 归因分析弹窗状态
    const showAttribution = ref(false)

    // 计算关系菜单
    const showRelationMenu = ref(false)

    // 右键菜单状态
    const showContextMenu = ref(false)
    const contextMenuPosition = ref<Position>({ x: 0, y: 0 })
    const relationTypes = [
      { id: 'add', name: '相加关系', icon: '+' },
      { id: 'subtract', name: '相减关系', icon: '-' },
      { id: 'multiply', name: '相乘关系', icon: '×' },
      { id: 'divide', name: '相除关系', icon: '÷' },
      { id: 'related', name: '相关关系', icon: 'C' }
    ]

    // 获取节点样式
    const getNodeStyle = () => {
      // 确保borderLeftStyle是有效的CSS值
      let borderStyle: 'solid' | 'dashed' | 'dotted' = 'solid'
      if (props.node.data.borderStyle === 'solid' ||
          props.node.data.borderStyle === 'dashed' ||
          props.node.data.borderStyle === 'dotted') {
        borderStyle = props.node.data.borderStyle as 'solid' | 'dashed' | 'dotted'
      }

      // 根据拖拽状态设置不同的样式
      const boxShadowValue = isDragging.value
        ? `0 8px 20px rgba(0, 0, 0, 0.15), 0 0 0 2px rgba(24, 144, 255, 0.3)`
        : (props.node.data.boxShadow || '0 2px 8px rgba(0, 0, 0, 0.08)')

      return {
        left: `${props.node.position.x}px`,
        top: `${props.node.position.y}px`,
        width: `${props.node.size?.width || 240}px`,
        borderLeftColor: props.node.data.color || '#1890ff',
        borderLeftWidth: props.node.data.borderWidth ? `${props.node.data.borderWidth}px` : '4px',
        borderRadius: props.node.data.borderRadius ? `${props.node.data.borderRadius}px` : '4px',
        backgroundColor: props.node.data.backgroundColor || 'white',
        boxShadow: boxShadowValue,
        borderLeftStyle: borderStyle,
        // 不在这里设置transform，而是通过applyTransform函数动态设置
        opacity: isDragging.value ? '0.95' : '1',
        zIndex: isDragging.value ? 1000 : 'auto'
      }
    }

    // 计算节点中心位置
    const nodeCenter = computed(() => {
      return {
        x: props.node.position.x + (props.node.size?.width || 240) / 2,
        y: props.node.position.y + (props.node.size?.height || 90) / 2
      }
    })

    // 节点点击事件
    const onNodeClick = () => {
      if (props.readonly) {
        // 在预览模式下，如果节点有归因数据，点击节点显示归因分析
        if (props.node.data.hasAttribution) {
          showAttributionPopup()
        }
        return
      }
      emit('select', props.node.id)
    }

    // 显示归因分析弹窗
    const showAttributionPopup = () => {
      if (!props.node.data.hasAttribution) return

      // 确保在预览模式下弹窗能够正确显示
      showAttribution.value = true

      // 发送事件到父组件
      emit('show-attribution', props.node.id)

      // 确保弹窗在下一个事件循环中显示（解决可能的渲染时序问题）
      setTimeout(() => {
        if (!showAttribution.value) {
          showAttribution.value = true
        }
      }, 0)
    }

    // 节点拖拽事件
    const onNodeMouseDown = (event: MouseEvent) => {
      if (props.readonly) return

      // 检查是否点击了操作按钮或关系菜单
      const target = event.target as HTMLElement;
      if (
        target.closest('.action-btn') ||
        target.closest('.relation-menu') ||
        target.closest('.node-port')
      ) {
        // 如果点击的是操作按钮或关系菜单，不启动拖拽
        return;
      }

      // 防止事件冒泡
      event.stopPropagation()

      // 获取节点DOM引用
      nodeRef.value = event.currentTarget as HTMLElement;

      // 选中节点
      emit('select', props.node.id)

      // 开始拖拽
      isDragging.value = true

      // 计算鼠标点击位置与节点左上角的偏移
      dragOffset.value = {
        x: event.clientX - props.node.position.x,
        y: event.clientY - props.node.position.y
      }

      // 重置transform值
      transformX = 0;
      transformY = 0;

      // 发送拖拽开始事件
      emit('drag-start', props.node.id)

      // 添加全局鼠标事件监听
      document.addEventListener('mousemove', onMouseMove)
      document.addEventListener('mouseup', onMouseUp)

      // 添加鼠标样式
      document.body.style.cursor = 'grabbing'

      // 阻止默认行为，防止文本选择
      event.preventDefault()
    }

    // 使用requestAnimationFrame优化拖拽性能
    let animationFrameId: number | null = null
    let lastMouseEvent: MouseEvent | null = null
    let lastUpdateTime = 0
    const THROTTLE_THRESHOLD = 1000 / 120; // 120fps的更新频率

    // 使用Transform而不是直接修改left/top属性
    let transformX = 0;
    let transformY = 0;

    // 节点DOM引用
    const nodeRef = ref<HTMLElement | null>(null);

    // 直接应用transform，避免重排
    const applyTransform = (x: number, y: number) => {
      if (!nodeRef.value) return;

      // 使用transform: translate3d触发GPU加速
      // 只应用位移，不应用缩放，避免影响内部元素的交互
      nodeRef.value.style.transform = `translate3d(${x}px, ${y}px, 0)`;

      // 如果是拖拽状态，添加一个小的缩放效果
      if (isDragging.value) {
        nodeRef.value.style.transform += ' scale(1.02)';
      }
    };

    const onMouseMove = (event: MouseEvent) => {
      if (!isDragging.value) return

      // 存储最新的鼠标事件
      lastMouseEvent = event

      // 如果已经有动画帧请求，则不再创建新的
      if (animationFrameId === null) {
        animationFrameId = requestAnimationFrame(updateNodePosition)
      }

      // 直接应用视觉反馈，不等待下一帧
      // 这会使拖拽感觉更加即时
      const currentTime = performance.now();
      if (currentTime - lastUpdateTime > THROTTLE_THRESHOLD) {
        const newX = event.clientX - dragOffset.value.x - props.node.position.x;
        const newY = event.clientY - dragOffset.value.y - props.node.position.y;

        transformX = newX;
        transformY = newY;

        applyTransform(newX, newY);
        lastUpdateTime = currentTime;
      }
    }

    const updateNodePosition = () => {
      // 重置动画帧ID
      animationFrameId = null

      // 如果没有鼠标事件或不在拖拽状态，则返回
      if (!lastMouseEvent || !isDragging.value) return

      // 计算新位置
      const newPosition = {
        x: lastMouseEvent.clientX - dragOffset.value.x,
        y: lastMouseEvent.clientY - dragOffset.value.y
      }

      // 更新节点位置 - 这会触发实际的数据更新
      emit('move', props.node.id, newPosition)

      // 如果还有未处理的鼠标事件，继续请求动画帧
      if (lastMouseEvent) {
        animationFrameId = requestAnimationFrame(updateNodePosition)
      }
    }

    const onMouseUp = () => {
      if (!isDragging.value) return

      // 结束拖拽
      isDragging.value = false

      // 重置transform
      if (nodeRef.value) {
        nodeRef.value.style.transform = '';
        transformX = 0;
        transformY = 0;
      }

      // 发送拖拽结束事件
      emit('drag-end', props.node.id)

      // 移除全局鼠标事件监听
      document.removeEventListener('mousemove', onMouseMove)
      document.removeEventListener('mouseup', onMouseUp)

      // 恢复鼠标样式
      document.body.style.cursor = ''

      // 取消任何待处理的动画帧
      if (animationFrameId !== null) {
        cancelAnimationFrame(animationFrameId)
        animationFrameId = null
      }

      // 清除最后的鼠标事件
      lastMouseEvent = null

      // 重置时间戳
      lastUpdateTime = 0
    }

    // 连接点事件
    const onPortMouseDown = (event: MouseEvent) => {
      if (props.readonly) return

      // 防止事件冒泡
      event.stopPropagation()

      // 开始连线
      emit('connect-start', props.node.id, {
        x: nodeCenter.value.x,
        y: nodeCenter.value.y
      })
    }

    const onPortMouseUp = () => {
      if (props.readonly) return

      // 结束连线
      emit('connect-end', props.node.id)
    }

    // 选择计算关系
    const selectRelation = (relation: { id: string, name: string, icon: string }) => {
      emit('add-relation', props.node.id, relation)
      showRelationMenu.value = false
    }

    // 当节点不再被选中时，隐藏关系菜单
    watch(() => props.selected, (newValue) => {
      if (!newValue) {
        showRelationMenu.value = false
      }
    })

    // 节点右键点击事件
    const onNodeRightClick = (event: MouseEvent) => {
      if (props.readonly) return

      // 选中节点
      emit('select', props.node.id)

      // 设置右键菜单位置
      contextMenuPosition.value = {
        x: event.clientX,
        y: event.clientY
      }

      // 显示右键菜单
      showContextMenu.value = true

      // 添加全局点击事件监听，用于关闭右键菜单
      document.addEventListener('click', closeContextMenu)
    }

    // 处理复制节点
    const handleCopyNode = () => {
      // 关闭右键菜单
      showContextMenu.value = false

      // 发送复制节点事件到父组件
      emit('copy-node', props.node.id)
    }

    // 处理编辑节点
    const handleEditNode = () => {
      // 关闭右键菜单
      showContextMenu.value = false

      // 选中节点，让配置面板显示
      emit('select', props.node.id)
    }

    // 处理删除节点
    const handleDeleteNode = () => {
      // 关闭右键菜单
      showContextMenu.value = false

      // 发送删除节点事件到父组件
      emit('delete-node', props.node.id)
    }

    // 处理添加关系（从右键菜单）
    const handleAddRelation = () => {
      // 关闭右键菜单
      showContextMenu.value = false

      // 显示关系菜单
      showRelationMenu.value = true
    }

    // 处理加号按钮点击
    const handleAddRelationBtnClick = () => {
      // 确保节点被选中
      emit('select', props.node.id)

      // 切换关系菜单显示状态
      showRelationMenu.value = !showRelationMenu.value

      // 如果菜单显示，添加点击外部关闭菜单的事件
      if (showRelationMenu.value) {
        // 使用setTimeout确保事件在当前点击事件处理完后才添加
        setTimeout(() => {
          // 移除之前可能存在的事件监听器，避免重复
          document.removeEventListener('click', closeRelationMenuOnce)
          // 添加一次性事件监听器
          document.addEventListener('click', closeRelationMenuOnce)
        }, 0)
      }
    }

    // 点击文档时关闭关系菜单（全局监听，用于处理鼠标悬停时的情况）
    const closeRelationMenu = (event: MouseEvent) => {
      // 只有当节点未被选中时，才自动关闭菜单
      if (showRelationMenu.value && !props.selected) {
        const target = event.target as HTMLElement
        if (!target.closest('.relation-menu') && !target.closest('.add-relation-btn')) {
          showRelationMenu.value = false
        }
      }
    }

    // 一次性关闭关系菜单的函数（用于点击加号按钮后）
    const closeRelationMenuOnce = (event: MouseEvent) => {
      const target = event.target as HTMLElement
      if (!target.closest('.relation-menu') && !target.closest('.add-relation-btn')) {
        showRelationMenu.value = false
        // 移除事件监听器，避免重复触发
        document.removeEventListener('click', closeRelationMenuOnce)
      }
    }

    // 确保在组件卸载时移除事件监听器
    onUnmounted(() => {
      if (typeof window !== 'undefined') {
        window.removeEventListener('click', closeRelationMenu)
        document.removeEventListener('click', closeRelationMenuOnce)
      }
    })

    // 点击文档时关闭右键菜单
    const closeContextMenu = () => {
      showContextMenu.value = false
      document.removeEventListener('click', closeContextMenu)
    }

    // 添加全局点击事件监听
    if (typeof window !== 'undefined') {
      window.addEventListener('click', closeRelationMenu)
    }

    return {
      nodeCenter,
      getNodeStyle,
      isHovered,
      isDragging,
      showRelationMenu,
      relationTypes,
      showAttribution,
      showContextMenu,
      contextMenuPosition,
      nodeRef, // 添加节点DOM引用
      onNodeClick,
      onNodeMouseDown,
      onNodeRightClick,
      onPortMouseDown,
      onPortMouseUp,
      selectRelation,
      showAttributionPopup,
      handleCopyNode,
      handleEditNode,
      handleDeleteNode,
      handleAddRelation,
      handleAddRelationBtnClick // 添加新的处理函数
    }
  }
})
</script>

<style lang="scss" scoped>
.node {
  position: absolute;
  user-select: none;
  cursor: move;
  display: flex;
  flex-direction: column;
  overflow: visible; /* 修改为visible，使菜单可以溢出 */
  height: auto;
  min-height: 90px;
  /* 只对非拖拽状态应用过渡效果 */
  transition: box-shadow 0.2s cubic-bezier(0.34, 1.56, 0.64, 1),
              opacity 0.15s ease,
              border-color 0.2s ease,
              background-color 0.2s ease;
  will-change: transform, box-shadow, opacity; /* 优化性能 */
  backface-visibility: hidden; /* 减少闪烁 */
  transform-origin: center center;
  /* 启用GPU加速，但不设置具体的transform值，避免与JS设置的transform冲突 */
  /* 提高层级，确保拖拽时在其他元素上方 */
  position: absolute;

  &.selected {
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.3);
  }

  &.hovered {
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);

    &::after {
      content: '';
      position: absolute;
      top: -2px;
      left: -2px;
      right: -2px;
      bottom: -2px;
      border: 1px dashed #1890ff;
      border-radius: 4px;
      pointer-events: none;
      animation: borderPulse 1.5s infinite ease-in-out;
    }
  }

  &.dragging {
    cursor: grabbing;
    /* 拖拽时禁用过渡效果，使移动更加即时 */
    transition: none;
    /* 不再设置pointer-events: none，以允许点击添加关系按钮 */
    /* 添加拖拽时的视觉效果 */
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15), 0 0 0 2px rgba(24, 144, 255, 0.4) !important;
    /* 确保在最上层 */
    z-index: 9999 !important;
    /* 添加微小的缩放效果 */
    transform-origin: center center;
  }

  @keyframes borderPulse {
    0% {
      border-color: rgba(24, 144, 255, 0.4);
    }
    50% {
      border-color: rgba(24, 144, 255, 0.8);
    }
    100% {
      border-color: rgba(24, 144, 255, 0.4);
    }
  }

  .node-header {
    padding: 8px 12px;
    font-size: 12px;
    color: #666;
    border-bottom: 1px solid #f0f0f0;
  }

  .node-content {
    padding: 12px;
    flex: 1;

    .value {
      font-size: 20px;
      font-weight: 500;
      margin-bottom: 4px;
      color: #333;
    }

    .change {
      font-size: 12px;

      &.positive {
        color: #52c41a;
      }

      &.negative {
        color: #f5222d;
      }

      .period {
        color: #999;
        margin-left: 4px;
      }
    }

    .attribution-indicator {
      display: flex;
      align-items: center;
      margin-top: 8px;
      padding: 4px 8px;
      background-color: #f0f7ff;
      border-radius: 4px;
      cursor: pointer;
      transition: background-color 0.2s ease;

      &:hover {
        background-color: #e6f7ff;
      }

      .indicator-icon {
        margin-right: 4px;
        font-size: 14px;
      }

      .indicator-text {
        font-size: 12px;
        color: #1890ff;
      }
    }
  }

  .node-actions {
    position: absolute;
    right: -16px;
    top: 50%;
    transform: translateY(-50%);
    z-index: 10000; /* 提高z-index，确保在拖拽节点之上 */

    .action-wrapper {
      position: relative;
      display: inline-block;
    }

    .action-btn {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background-color: #fff;
      border: 1px solid #e8e8e8;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      transition: all 0.2s ease;

      &:hover {
        border-color: #1890ff;
        color: #1890ff;
        transform: scale(1.1);
      }

      span {
        font-size: 18px;
        line-height: 1;
      }
    }

    .relation-menu {
      position: absolute;
      top: 50%;
      left: 100%; /* 将菜单放在按钮右侧 */
      transform: translateY(-50%); /* 垂直居中对齐 */
      background-color: #fff;
      border-radius: 4px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      z-index: 10000; /* 提高z-index，确保在拖拽节点之上 */
      width: 120px;
      margin-left: 8px; /* 添加左侧间距，使菜单不紧贴按钮 */

      .relation-item {
        display: flex;
        align-items: center;
        width: 100%;
        padding: 8px 12px;
        border: none;
        background: none;
        text-align: left;
        cursor: pointer;

        &:hover {
          background-color: #f5f5f5;
        }

        .relation-icon {
          width: 24px;
          height: 24px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 8px;
          font-weight: bold;
        }

        .relation-name {
          font-size: 14px;
        }
      }
    }
  }

  .node-port {
    position: absolute;
    width: 10px;
    height: 10px;
    background-color: #1890ff;
    border: 2px solid white;
    border-radius: 50%;
    box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease;

    &:hover {
      transform: scale(1.2);
    }

    &.node-port-out {
      right: -5px;
      top: 50%;
      transform: translateY(-50%);
      cursor: crosshair;
    }

    &.node-port-in {
      left: -5px;
      top: 50%;
      transform: translateY(-50%);
      cursor: crosshair;
    }
  }
}

// 不同类型节点的样式
.node-type-kpi {
  border-left-color: #1890ff;
}

.node-type-input {
  border-left-color: #52c41a;
}

.node-type-output {
  border-left-color: #fa8c16;
}

.node-type-process {
  border-left-color: #722ed1;
}
</style>