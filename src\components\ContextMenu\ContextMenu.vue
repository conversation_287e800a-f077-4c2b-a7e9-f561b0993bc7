<template>
  <teleport to="body">
    <div
      v-if="visible"
      class="context-menu"
      :style="{
        left: `${position.x}px`,
        top: `${position.y}px`
      }"
      @click.stop
    >
      <div class="menu-item" @click="$emit('copy')">
        <span class="menu-icon">📋</span>
        <span class="menu-text">复制节点</span>
      </div>
      <div class="menu-item" @click="$emit('edit')">
        <span class="menu-icon">✏️</span>
        <span class="menu-text">编辑节点</span>
      </div>
      <div class="menu-item" @click="$emit('add-relation')">
        <span class="menu-icon">🔗</span>
        <span class="menu-text">添加关系</span>
      </div>
      <div class="menu-item danger" @click="$emit('delete')">
        <span class="menu-icon">🗑️</span>
        <span class="menu-text">删除节点</span>
      </div>
    </div>
  </teleport>
</template>

<script lang="ts">
import { defineComponent } from 'vue'
import type { Position } from '../../types'

export default defineComponent({
  name: 'ContextMenu',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    position: {
      type: Object as () => Position,
      required: true
    }
  },
  emits: ['copy', 'edit', 'delete', 'add-relation']
})
</script>

<style lang="scss" scoped>
.context-menu {
  position: fixed;
  z-index: 1000;
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
  min-width: 160px;
  padding: 5px 0;
  
  .menu-item {
    padding: 8px 16px;
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: background-color 0.2s;
    
    &:hover {
      background-color: #f5f5f5;
    }
    
    &.danger {
      color: #f5222d;
      
      &:hover {
        background-color: #fff1f0;
      }
    }
    
    .menu-icon {
      margin-right: 8px;
      font-size: 16px;
    }
    
    .menu-text {
      font-size: 14px;
    }
  }
}
</style>
