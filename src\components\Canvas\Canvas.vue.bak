<template>
  <div
    ref="canvasRef"
    class="canvas"
    @mousedown="onMouseDown"
    @mousemove="onMouseMove"
    @mouseup="onMouseUp"
    @wheel="onWheel"
    @contextmenu="onCanvasContextMenu"
  >
    <!-- 空画布背景提示 -->
    <div
      v-if="!readonly && nodes.length === 0"
      class="empty-canvas-background-hint"
    >
      <div class="hint-text">右键点击画布任意位置也可添加节点</div>
    </div>

    <div
      class="canvas-content"
      :style="{
        transform: `translate(${canvasPosition.x}px, ${canvasPosition.y}px) scale(${canvasScale})`,
        width: `${canvasDimensions.width}px`,
        height: `${canvasDimensions.height}px`
      }"
    >
      <!-- 渲染所有连线 -->
      <Edge
        v-for="edge in edges"
        :key="edge.id"
        :edge="edge"
        :source-node="getNodeById(edge.source)"
        :target-node="getNodeById(edge.target)"
        :selected="edge.id === selectedEdgeId"
        :readonly="readonly"
        :highlighted="draggedNodeId && (edge.source === draggedNodeId || edge.target === draggedNodeId)"
        @select="selectEdge"
        @symbol-click="onSymbolClick"
        @connector-click="onConnectorClick"
      />

      <!-- 渲染所有节点 -->
      <Node
        v-for="node in nodes"
        :key="node.id"
        :node="node"
        :selected="node.id === selectedNodeId"
        :readonly="readonly"
        @select="selectNode"
        @move="moveNode"
        @connect-start="startConnecting"
        @connect-end="finishConnecting"
        @add-relation="handleAddRelation"
        @show-attribution="handleShowAttribution"
        @drag-start="handleDragStart"
        @drag-end="handleDragEnd"
        @copy-node="handleCopyNode"
        @delete-node="handleDeleteNode"
      />

      <!-- 绘制连线过程中的临时线 -->
      <svg class="temp-edge" v-if="isConnecting">
        <path
          :d="getTempEdgePath()"
          stroke="#1890ff"
          stroke-width="1.5"
          fill="none"
          stroke-dasharray="5,5"
        />
      </svg>

    </div>

    <!-- 空画布节点提示 -->
    <div
      v-if="!readonly && nodes.length === 0"
      class="empty-canvas-node-prompt"
      @click="createEmptyNode"
      @contextmenu.prevent="onEmptyCanvasRightClick"
    >
      <div class="node-header">
        <div class="header-text">新建指标</div>
      </div>
      <div class="node-content">
        <div class="add-icon-container">
          <div class="add-icon">+</div>
        </div>
        <div class="prompt-text">点击此处添加节点</div>
      </div>
    </div>

    <!-- 实时缩略图 -->
    <CanvasThumbnail
      v-if="!readonly && showThumbnail"
      :nodes="nodesWithDragState"
      :edges="edges"
      :canvas-position="canvasPosition"
      :canvas-scale="canvasScale"
      :canvas-dimensions="canvasDimensions"
      :container-size="containerSize"
      :selected-node-id="selectedNodeId"
    />

    <!-- 缩略图切换按钮 -->
    <button
      v-if="!readonly"
      class="thumbnail-toggle-btn"
      @click="showThumbnail = !showThumbnail"
      :title="showThumbnail ? '隐藏缩略图' : '显示缩略图'"
    >
      <span class="toggle-icon">{{ showThumbnail ? '◱' : '◰' }}</span>
    </button>

    <!-- 空画布右键菜单 -->
    <EmptyCanvasContextMenu
      :visible="showEmptyCanvasMenu"
      :position="emptyCanvasMenuPosition"
      @add-node="handleAddNodeFromMenu"
      @paste="handlePasteFromMenu"
      @auto-layout="handleAutoLayoutFromMenu"
    />
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed, watch, onMounted } from 'vue'
import { useCanvasStore } from '../../stores/canvas'
import { useNodeStore } from '../../stores/node'
import { useEdgeStore } from '../../stores/edge'
import type { Position, Node as NodeType, Size } from '../../types'
import Node from '../Node/Node.vue'
import Edge from '../Edge/Edge.vue'
import CanvasThumbnail from '../CanvasThumbnail/CanvasThumbnail.vue'
import EmptyCanvasContextMenu from '../ContextMenu/EmptyCanvasContextMenu.vue'

// 声明全局变量
declare global {
  interface Window {
    layoutDebounceTimer: number | undefined
  }
}

export default defineComponent({
  name: 'Canvas',
  components: {
    Node,
    Edge,
    CanvasThumbnail,
    EmptyCanvasContextMenu
  },
  props: {
    readonly: {
      type: Boolean,
      default: false
    }
  },
  emits: ['add-relation', 'create-node', 'connector-click', 'node-removed', 'show-attribution', 'auto-layout', 'close-relation-selector', 'create-empty-node'],
  setup(props, { emit }) {
    const canvasRef = ref<HTMLElement | null>(null)
    const canvasStore = useCanvasStore()
    const nodeStore = useNodeStore()
    const edgeStore = useEdgeStore()

    // 画布状态
    const canvasScale = computed(() => canvasStore.scale)
    const canvasPosition = computed(() => canvasStore.position)
    const canvasDimensions = computed(() => canvasStore.dimensions)

    // 缩略图状态
    const showThumbnail = ref(true)
    const containerSize = ref<Size>({ width: 0, height: 0 })

    // 获取容器尺寸
    onMounted(() => {
      if (canvasRef.value) {
        containerSize.value = {
          width: canvasRef.value.clientWidth,
          height: canvasRef.value.clientHeight
        }

        // 监听窗口大小变化
        window.addEventListener('resize', updateContainerSize)
      }
    })

    // 更新容器尺寸
    const updateContainerSize = () => {
      if (canvasRef.value) {
        containerSize.value = {
          width: canvasRef.value.clientWidth,
          height: canvasRef.value.clientHeight
        }
      }
    }

    // 节点状态
    const nodes = computed(() => nodeStore.nodes)
    const selectedNodeId = computed(() => nodeStore.selectedNodeId)
    const getNodeById = (id: string) => nodeStore.getNodeById(id)

    // 连线状态
    const edges = computed(() => edgeStore.edges)
    const selectedEdgeId = computed(() => edgeStore.selectedEdgeId)

    // 连线操作状态
    const isConnecting = ref(false)
    const connectingSourceId = ref<string | null>(null)
    const connectingPosition = ref<Position>({ x: 0, y: 0 })

    // 拖拽画布状态
    const isDraggingCanvas = ref(false)
    const lastMousePosition = ref<Position>({ x: 0, y: 0 })

    // 画布拖拽优化
    let canvasAnimationFrameId: number | null = null
    let lastCanvasMouseEvent: MouseEvent | null = null
    let lastCanvasUpdateTime = 0
    const CANVAS_THROTTLE_THRESHOLD = 1000 / 120 // 120fps

    // 自动布局控制标志
    const autoLayoutEnabled = ref(true)

    // 当前被拖动的节点ID
    const draggedNodeId = ref<string | null>(null)

    // 为节点添加拖动状态
    const nodesWithDragState = computed(() => {
      return nodes.value.map(node => ({
        ...node,
        isDragging: node.id === draggedNodeId.value
      }))
    })

    // 监听节点删除事件
    watch(() => nodeStore.nodes.length, (newCount, oldCount) => {
      if (newCount < oldCount) {
        // 找出被删除的节点ID
        const oldNodeIds = new Set(nodeStore.nodes.map(node => node.id))
        const deletedNodeId = Array.from(oldNodeIds).find(id => !oldNodeIds.has(id))

        if (deletedNodeId) {
          // 通知父组件节点被删除
          emit('node-removed', deletedNodeId)
        }
      }
    })

    // 创建空节点
    const createEmptyNode = (event?: MouseEvent) => {
      if (props.readonly) return

      // 计算节点位置 - 使用画布中心位置
      const position: Position = {
        x: (canvasDimensions.value.width / 2 - canvasPosition.value.x) / canvasScale.value,
        y: (canvasDimensions.value.height / 2 - canvasPosition.value.y) / canvasScale.value
      }

      // 发送创建空节点事件
      emit('create-empty-node', position)

      // 关闭右键菜单（如果打开）
      if (showEmptyCanvasMenu.value) {
        closeEmptyCanvasMenu()
      }
    }

    // 空画布右键菜单状态
    const showEmptyCanvasMenu = ref(false)
    const emptyCanvasMenuPosition = ref<Position>({ x: 0, y: 0 })
    const rightClickPosition = ref<Position>({ x: 0, y: 0 })

    // 空画布右键点击事件
    const onEmptyCanvasRightClick = (event: MouseEvent) => {
      if (props.readonly) return

      // 显示右键菜单
      if (canvasRef.value) {
        // 保存鼠标在画布中的位置（用于创建节点）
        const canvasRect = canvasRef.value.getBoundingClientRect()
        rightClickPosition.value = {
          x: (event.clientX - canvasRect.left - canvasPosition.value.x) / canvasScale.value,
          y: (event.clientY - canvasRect.top - canvasPosition.value.y) / canvasScale.value
        }

        // 设置菜单在屏幕上的位置
        emptyCanvasMenuPosition.value = {
          x: event.clientX,
          y: event.clientY
        }

        // 显示菜单
        showEmptyCanvasMenu.value = true

        // 添加点击事件监听器，用于关闭菜单
        document.addEventListener('click', closeEmptyCanvasMenu)
      }
    }

    // 关闭空画布右键菜单
    const closeEmptyCanvasMenu = () => {
      showEmptyCanvasMenu.value = false
      document.removeEventListener('click', closeEmptyCanvasMenu)
    }

    // 处理从菜单添加节点
    const handleAddNodeFromMenu = () => {
      // 发送创建空节点事件，使用右键点击位置
      emit('create-empty-node', rightClickPosition.value)
      closeEmptyCanvasMenu()
    }

    // 处理从菜单粘贴
    const handlePasteFromMenu = () => {
      // 这里可以实现粘贴功能，或者发送事件到父组件
      console.log('Paste at position:', rightClickPosition.value)
      closeEmptyCanvasMenu()
    }

    // 处理从菜单自动布局
    const handleAutoLayoutFromMenu = () => {
      // 调用自动布局函数
      applyAutoLayout(true)
      closeEmptyCanvasMenu()
    }

    // 优化节点间距并防止重叠 - 新增函数
    const optimizeNodeSpacing = () => {
      if (nodes.value.length <= 1) return false; // 如果只有0或1个节点，不需要处理
      
      // 计算节点的有效区域，包括安全间距
      const getNodeBounds = (node, safetyMargin = 20) => {
        return {
          left: node.position.x - safetyMargin,
          right: node.position.x + (node.size?.width || 240) + safetyMargin,
          top: node.position.y - safetyMargin,
          bottom: node.position.y + (node.size?.height || 90) + safetyMargin
        };
      };
      
      // 检测两个节点是否重叠
      const checkOverlap = (node1, node2) => {
        const bounds1 = getNodeBounds(node1);
        const bounds2 = getNodeBounds(node2);
        
        return !(bounds1.right < bounds2.left ||
                 bounds1.left > bounds2.right ||
                 bounds1.bottom < bounds2.top ||
                 bounds1.top > bounds2.bottom);
      };
      
      // 检查节点是否在同一水平线上（X坐标接近）
      const isInSameColumn = (node1, node2) => {
        return Math.abs(node1.position.x - node2.position.x) < (node1.size?.width || 240) / 2;
      };
      
      // 按Y坐标排序所有节点，确保从上到下处理
      const sortedNodes = [...nodes.value].sort((a, b) => a.position.y - b.position.y);
      let nodesAdjusted = false;
      
      // 多次迭代，确保所有重叠都被解决
      for (let iteration = 0; iteration < 3; iteration++) {
        let adjustmentsInThisIteration = 0;
        
        for (let i = 0; i < sortedNodes.length; i++) {
          for (let j = i + 1; j < sortedNodes.length; j++) {
            const nodeA = sortedNodes[i];
            const nodeB = sortedNodes[j];
            
            // 检查节点是否重叠
            if (checkOverlap(nodeA, nodeB)) {
              if (isInSameColumn(nodeA, nodeB)) {
                // 垂直方向上的重叠 - 向下移动节点B
                const minVerticalGap = 150; // 使用较大的垂直间距
                const newY = nodeA.position.y + (nodeA.size?.height || 90) + minVerticalGap;
                
                // 移动节点B
                nodeStore.moveNode(nodeB.id, {
                  x: nodeB.position.x,
                  y: newY
                });
                
                // 更新排序后数组中的节点位置
                nodeB.position.y = newY;
                adjustmentsInThisIteration++;
                nodesAdjusted = true;
              } else {
                // 水平方向上的重叠 - 向右移动节点B
                const minHorizontalGap = 80; // 水平方向的最小间距
                const newX = nodeA.position.x + (nodeA.size?.width || 240) + minHorizontalGap;
                
                // 移动节点B
                nodeStore.moveNode(nodeB.id, {
                  x: newX,
                  y: nodeB.position.y
                });
                
                // 更新排序后数组中的节点位置
                nodeB.position.x = newX;
                adjustmentsInThisIteration++;
                nodesAdjusted = true;
              }
            }
          }
        }
        
        // 如果本次迭代没有调整任何节点，退出循环
        if (adjustmentsInThisIteration === 0) break;
        
        // 重新排序节点，以便下一次迭代使用最新的位置
        sortedNodes.sort((a, b) => a.position.y - b.position.y);
      }
      
      // 确保所有父子关系的节点位置正确
      // 子节点应该在父节点的右侧
      edges.value.forEach(edge => {
        const sourceNode = getNodeById(edge.source);
        const targetNode = getNodeById(edge.target);
        
        if (!sourceNode || !targetNode) return;
        
        // 如果目标节点（子节点）在源节点（父节点）左侧，将其移到右侧
        if (targetNode.position.x <= sourceNode.position.x + (sourceNode.size?.width || 240)) {
          const minHorizontalGap = 100; // 父子节点之间的最小水平间距
          const newX = sourceNode.position.x + (sourceNode.size?.width || 240) + minHorizontalGap;
          
          nodeStore.moveNode(targetNode.id, {
            x: newX,
            y: targetNode.position.y
          });
          
          nodesAdjusted = true;
        }
      });
      
      return nodesAdjusted; // 返回是否有节点被调整
    }

    // 鼠标事件处理
    const onMouseDown = (event: MouseEvent) => {
      // 如果点击的是画布背景（而不是节点），则开始拖拽画布
      if (event.target === canvasRef.value || event.target === canvasRef.value?.firstElementChild) {
        // 发送关闭弹窗的事件
        if (!props.readonly) {
          emit('close-relation-selector')
        }

        isDraggingCanvas.value = true
        lastMousePosition.value = { x: event.clientX, y: event.clientY }

        // 重置画布拖拽优化变量
        lastCanvasMouseEvent = null
        lastCanvasUpdateTime = 0

        // 添加鼠标样式
        document.body.style.cursor = 'grabbing'

        // 在非预览模式下，取消选中的节点和连线
        if (!props.readonly) {
          nodeStore.selectNode(null)
          edgeStore.selectEdge(null)
        }

        // 阻止默认行为
        event.preventDefault()
      }
    }

    // 为整个画布添加右键菜单
    const onCanvasContextMenu = (event: MouseEvent) => {
      // 如果是只读模式或者点击的不是画布背景，则不处理
      if (props.readonly) return

      // 阻止默认的右键菜单
      event.preventDefault()

      // 如果画布为空，显示空画布右键菜单
      if (nodes.value.length === 0) {
        onEmptyCanvasRightClick(event)
      }
    }

    const onMouseMove = (event: MouseEvent) => {
      if (isDraggingCanvas.value) {
        // 存储最新的鼠标事件
        lastCanvasMouseEvent = event

        // 使用requestAnimationFrame优化画布拖拽
        if (canvasAnimationFrameId === null) {
          canvasAnimationFrameId = requestAnimationFrame(updateCanvasPosition)
        }

        // 直接应用视觉反馈，不等待下一帧
        const currentTime = performance.now();
        if (currentTime - lastCanvasUpdateTime > CANVAS_THROTTLE_THRESHOLD) {
          // 计算鼠标移动的距离
          const deltaX = event.clientX - lastMousePosition.value.x
          const deltaY = event.clientY - lastMousePosition.value.y

          // 更新画布位置
          canvasStore.pan(deltaX, deltaY)

          // 更新最后的鼠标位置
          lastMousePosition.value = { x: event.clientX, y: event.clientY }

          lastCanvasUpdateTime = currentTime;
        }
      }

      // 只在非预览模式下处理连线
      if (!props.readonly && isConnecting.value) {
        // 更新临时连线的终点位置
        const canvasRect = canvasRef.value?.getBoundingClientRect()
        if (canvasRect) {
          connectingPosition.value = {
            x: (event.clientX - canvasRect.left - canvasPosition.value.x) / canvasScale.value,
            y: (event.clientY - canvasRect.top - canvasPosition.value.y) / canvasScale.value
          }
        }
      }
    }

    // 使用requestAnimationFrame优化画布拖拽
    const updateCanvasPosition = () => {
      // 重置动画帧ID
      canvasAnimationFrameId = null

      // 如果没有鼠标事件或不在拖拽状态，则返回
      if (!lastCanvasMouseEvent || !isDraggingCanvas.value) return

      // 计算鼠标移动的距离
      const deltaX = lastCanvasMouseEvent.clientX - lastMousePosition.value.x
      const deltaY = lastCanvasMouseEvent.clientY - lastMousePosition.value.y

      // 更新画布位置
      canvasStore.pan(deltaX, deltaY)

      // 更新最后的鼠标位置
      lastMousePosition.value = {
        x: lastCanvasMouseEvent.clientX,
        y: lastCanvasMouseEvent.clientY
      }

      // 如果还有未处理的鼠标事件，继续请求动画帧
      if (lastCanvasMouseEvent && isDraggingCanvas.value) {
        canvasAnimationFrameId = requestAnimationFrame(updateCanvasPosition)
      }
    }

    const onMouseUp = () => {
      if (isDraggingCanvas.value) {
        isDraggingCanvas.value = false

        // 取消任何待处理的动画帧
        if (canvasAnimationFrameId !== null) {
          cancelAnimationFrame(canvasAnimationFrameId)
          canvasAnimationFrameId = null
        }

        // 清除最后的鼠标事件
        lastCanvasMouseEvent = null

        // 重置时间戳
        lastCanvasUpdateTime = 0

        // 恢复鼠标样式
        document.body.style.cursor = ''
      }
    }

    const onWheel = (event: WheelEvent) => {
      event.preventDefault()

      if (!canvasRef.value) return

      // 获取鼠标相对于画布的位置
      const canvasRect = canvasRef.value.getBoundingClientRect()
      const center = {
        x: event.clientX - canvasRect.left,
        y: event.clientY - canvasRect.top
      }

      // 缩放画布 - 在预览模式和编辑模式下都允许
      canvasStore.zoom(-event.deltaY, center)
    }

    // 节点操作
    const selectNode = (id: string) => {
      if (props.readonly) return

      nodeStore.selectNode(id)
      edgeStore.selectEdge(null)
    }

    const moveNode = (id: string, position: Position) => {
      if (props.readonly) return

      // 当用户手动拖动节点时，禁用自动布局
      autoLayoutEnabled.value = false

      // 记录当前被拖动的节点ID，避免其他节点被自动调整
      draggedNodeId.value = id

      // 更新节点位置
      nodeStore.moveNode(id, position)
    }

    // 处理添加关系
    const handleAddRelation = (nodeId: string, relation: any) => {
      if (props.readonly) return

      // 向父组件发送事件，显示指标选择器
      emit('add-relation', nodeId, relation)
    }

    // 处理显示归因分析
    const handleShowAttribution = (nodeId: string) => {
      // 转发归因分析事件到父组件
      emit('show-attribution', nodeId)
    }

    // 处理节点拖拽开始事件
    const handleDragStart = (nodeId: string) => {
      // 设置当前拖拽节点ID
      draggedNodeId.value = nodeId

      // 获取当前节点
      const node = getNodeById(nodeId)
      if (!node) return

      // 添加拖拽开始的视觉反馈
      // 可以在这里添加一些额外的视觉效果，如高亮相关连线等

      // 禁用自动布局
      autoLayoutEnabled.value = false

      // 如果有子节点，预先计算它们的位置约束
      const { nodeMap } = buildNodeTree()
      const nodeInfo = nodeMap.get(nodeId)

      // 存储子节点信息，以便在拖拽过程中使用
      if (nodeInfo && nodeInfo.children.length > 0) {
        // 这里可以预先计算一些约束条件
      }
    }

    // 处理节点拖拽结束事件
    const handleDragEnd = (nodeId: string) => {
      // 重置拖拽节点ID
      draggedNodeId.value = null

      // 保持自动布局禁用状态，确保节点位置不会被自动调整
      autoLayoutEnabled.value = false

      // 获取当前节点
      const node = getNodeById(nodeId)
      if (!node) return

      // 构建节点关系树
      const { nodeMap } = buildNodeTree()
      const nodeInfo = nodeMap.get(nodeId)

      // 如果节点有子节点，确保子节点位置合理
      if (nodeInfo && nodeInfo.children.length > 0) {
        // 获取所有子节点
        const childNodes = nodeInfo.children.map(childId => getNodeById(childId)).filter(Boolean)

        // 使用requestAnimationFrame平滑调整子节点位置
        requestAnimationFrame(() => {
          // 确保所有子节点在父节点右侧且有足够距离
          childNodes.forEach(childNode => {
            if (!childNode) return

            // 计算计算符号的位置
            const symbolX = node.position.x + NODE_WIDTH + MIN_HORIZONTAL_GAP
            const connectorEndX = symbolX + SYMBOL_WIDTH + CONNECTOR_LENGTH
            const minChildX = connectorEndX + 20  // 适当减少最小距离，使子节点更靠近父节点，形成更好的曲线效果

            // 如果子节点位置不合理，平滑调整其位置
            if (childNode.position.x < minChildX) {
              // 使用CSS过渡效果平滑移动子节点
              nodeStore.moveNode(childNode.id, {
                x: minChildX,
                y: childNode.position.y
              })
            }
          })
        })
      }

      // 添加拖拽结束的视觉反馈
      // 可以在这里添加一些额外的视觉效果，如短暂的高亮等
    }

    // 处理复制节点
    const handleCopyNode = (nodeId: string) => {
      if (props.readonly) return

      // 获取要复制的节点
      const sourceNode = getNodeById(nodeId)
      if (!sourceNode) return

      // 创建新节点，复制原节点的数据
      const newNode = {
        id: '',
        type: sourceNode.type,
        position: {
          x: sourceNode.position.x + 20, // 稍微偏移一点，避免完全重叠
          y: sourceNode.position.y + 20
        },
        size: sourceNode.size,
        data: { ...sourceNode.data, label: sourceNode.data.label + ' (复制)' }
      }

      // 添加新节点
      const newNodeId = nodeStore.addNode(newNode)

      // 选中新节点
      selectNode(newNodeId)
    }

    // 处理删除节点
    const handleDeleteNode = (nodeId: string) => {
      if (props.readonly) return

      // 删除节点
      nodeStore.removeNode(nodeId)

      // 删除与该节点相关的所有连线
      edgeStore.removeEdgesConnectedToNode(nodeId)

      // 取消选中
      nodeStore.selectNode(null)
    }

    // 节点布局常量 - 全局变量，避免重复声明
    const NODE_HEIGHT = 90
    const NODE_WIDTH = 240
    const SYMBOL_WIDTH = 30
    const MIN_HORIZONTAL_GAP = 40  // 减少节点与计算符号之间的最小间距，确保适当的间距
    const CONNECTOR_LENGTH = 60    // 适当增加计算符号右侧的短直线长度，使虚线起点更靠右

    // 构建节点关系树 - 全局函数，避免重复声明
    const buildNodeTree = () => {
      const nodeMap = new Map()

      // 初始化节点映射
      nodes.value.forEach(node => {
        nodeMap.set(node.id, {
          node,
          children: [],
          parent: null,
          depth: 0,
          siblingIndex: 0,
          hasSingleChild: false,
          totalSiblings: 0,
          parentGroup: '',
          siblingNodes: [],
          isTerminalNode: true, // 默认所有节点都是终端节点，后续会更新
          childrenCount: 0,
          uncleNodes: [], // 存储叔叔节点（父节点的兄弟节点）
          cousinNodes: [], // 存储表节点（叔叔节点的子节点）
          requiredSpace: NODE_HEIGHT, // 节点需要的最小垂直空间
          hasNewChildren: false, // 标记是否有新增子节点
          lastChildrenCount: 0 // 上次记录的子节点数量
        })
      })

      // 构建父子关系
      edges.value.forEach(edge => {
        const sourceInfo = nodeMap.get(edge.source)
        const targetInfo = nodeMap.get(edge.target)

        if (sourceInfo && targetInfo) {
          sourceInfo.children.push(edge.target)
          sourceInfo.childrenCount = sourceInfo.children.length
          sourceInfo.isTerminalNode = false // 有子节点，不是终端节点
          targetInfo.parent = edge.source

          // 检测是否有新增子节点 - 关键部分：实现规则3的触发条件
          // 无论是第一个还是第二个子节点，都标记为有新增子节点，确保每次添加子节点都增加父叔节点间距
          sourceInfo.hasNewChildren = true

          // 保存当前子节点数量，用于下次比较
          sourceInfo.lastChildrenCount = sourceInfo.childrenCount
        }
      })

      // 找出根节点
      const rootNodes = []
      nodeMap.forEach((info, nodeId) => {
        if (!info.parent) {
          rootNodes.push(nodeId)
        }
      })

      // 计算节点深度
      const calculateNodeDepth = (nodeId, depth = 0) => {
        const nodeInfo = nodeMap.get(nodeId)
        if (!nodeInfo) return

        nodeInfo.depth = depth

        // 递归计算子节点深度
        nodeInfo.children.forEach(childId => {
          calculateNodeDepth(childId, depth + 1)
        })
      }

      // 从根节点开始计算深度
      rootNodes.forEach(rootId => {
        calculateNodeDepth(rootId)
      })

      // 计算叔叔节点和表节点关系
      nodeMap.forEach((info, nodeId) => {
        if (info.parent) {
          const parentInfo = nodeMap.get(info.parent)
          if (parentInfo && parentInfo.parent) {
            const grandparentInfo = nodeMap.get(parentInfo.parent)
            if (grandparentInfo) {
              // 获取所有叔叔节点（父节点的兄弟节点）
              info.uncleNodes = grandparentInfo.children.filter(id => id !== info.parent)

              // 获取所有表节点（叔叔节点的子节点）
              info.uncleNodes.forEach(uncleId => {
                const uncleInfo = nodeMap.get(uncleId)
                if (uncleInfo) {
                  info.cousinNodes.push(...uncleInfo.children)
                }
              })
            }
          }
        }
      })

      // 计算每个节点所需的空间
      const calculateRequiredSpace = (nodeId) => {
        const nodeInfo = nodeMap.get(nodeId)
        if (!nodeInfo) return NODE_HEIGHT

        // 如果是终端节点，只需要自身高度
        if (nodeInfo.isTerminalNode) {
          return NODE_HEIGHT
        }

        // 如果只有一个子节点
        if (nodeInfo.children.length === 1) {
          const childId = nodeInfo.children[0]
          const childSpace = calculateRequiredSpace(childId)
          return Math.max(NODE_HEIGHT, childSpace)
        }

        // 如果有多个子节点，计算所有子节点需要的总空间
        let totalChildrenSpace = 0

        // 检查是否所有子节点都是终端节点
        const allTerminalNodes = nodeInfo.children.every(childId => {
          const childInfo = nodeMap.get(childId)
          return childInfo && childInfo.isTerminalNode
        })

        // 使用适当的垂直间距
        const spacing = allTerminalNodes ? TERMINAL_NODE_SPACING : VERTICAL_SPACING

        // 计算子节点所需的总空间
        nodeInfo.children.forEach(childId => {
          const childSpace = calculateRequiredSpace(childId)
          totalChildrenSpace += childSpace
        })

        // 加上子节点之间的间距
        totalChildrenSpace += (nodeInfo.children.length - 1) * spacing

        // 为未来可能添加的子节点预留空间
        const requiredSpace = Math.max(NODE_HEIGHT, totalChildrenSpace * FUTURE_NODE_SPACE_FACTOR)

        // 更新节点所需空间
        nodeInfo.requiredSpace = requiredSpace

        return requiredSpace
      }

      // 从根节点开始计算所需空间
      rootNodes.forEach(rootId => {
        calculateRequiredSpace(rootId)
      })

      return { nodeMap, rootNodes }
    }

    // 连线操作
    const selectEdge = (id: string) => {
      if (props.readonly) return

      edgeStore.selectEdge(id)
      nodeStore.selectNode(null)
    }

    // 计算关系符号点击事件
    const onSymbolClick = (edgeId: string, symbolInfo?: any) => {
      if (props.readonly) return

      // 先发送关闭已有弹窗的事件
      emit('close-relation-selector')

      // 选中连线
      edgeStore.selectEdge(edgeId)
      nodeStore.selectNode(null)

      // 获取连线信息
      const edge = edgeStore.getEdgeById(edgeId)
      if (!edge) return

      // 计算画布缩放和平移后的实际位置
      let adjustedSymbolInfo = symbolInfo

      if (symbolInfo) {
        // 将画布坐标转换为屏幕坐标
        const canvasRect = canvasRef.value?.getBoundingClientRect()
        if (canvasRect) {
          // 计算符号在屏幕上的实际位置
          const screenX = symbolInfo.x * canvasScale.value + canvasPosition.value.x + canvasRect.left
          const screenY = symbolInfo.y * canvasScale.value + canvasPosition.value.y + canvasRect.top

          adjustedSymbolInfo = {
            ...symbolInfo,
            screenX,
            screenY,
            canvasScale: canvasScale.value,
            canvasPosition: canvasPosition.value,
            canvasRect: {
              left: canvasRect.left,
              top: canvasRect.top,
              width: canvasRect.width,
              height: canvasRect.height
            }
          }
        }
      }

      // 向父组件发送事件，显示关系类型选择器，同时传递符号的精确位置和画布信息
      emit('add-relation', edge.source, {
        id: edge.type || 'related',
        name: '修改关系',
        icon: edge.label || 'C',
        symbolInfo: adjustedSymbolInfo // 传递符号的精确位置和画布信息
      })
    }

    // 连接器点击事件
    const onConnectorClick = (edgeId: string, position: Position) => {
      if (props.readonly) return

      // 选中连线
      edgeStore.selectEdge(edgeId)
      nodeStore.selectNode(null)

      // 获取连线信息
      const edge = edgeStore.getEdgeById(edgeId)
      if (!edge) return

      // 向父组件发送事件，显示指标选择器
      emit('connector-click', edge.source, edge.type || 'related', position)
    }

    const startConnecting = (nodeId: string, position: Position) => {
      if (props.readonly) return

      isConnecting.value = true
      connectingSourceId.value = nodeId
      connectingPosition.value = position
    }

    const finishConnecting = (targetId: string) => {
      if (props.readonly || !isConnecting.value || !connectingSourceId.value) return

      // 不允许连接到自己
      if (connectingSourceId.value !== targetId) {
        edgeStore.addEdge({
          id: '',
          source: connectingSourceId.value,
          target: targetId,
          label: 'C'
        })
      }

      // 重置连线状态
      isConnecting.value = false
      connectingSourceId.value = null
    }

    // 获取临时连线的路径
    const getTempEdgePath = () => {
      if (!isConnecting.value || !connectingSourceId.value) return ''

      const sourceNode = getNodeById(connectingSourceId.value)
      if (!sourceNode) return ''

      // 源节点的右侧中点
      const startX = sourceNode.position.x + (sourceNode.size?.width || 240)
      const startY = sourceNode.position.y + (sourceNode.size?.height || 90) / 2

      // 计算控制点 - 使用与Edge组件相同的改进算法
      const dx = Math.abs(connectingPosition.value.x - startX)
      const dy = connectingPosition.value.y - startY

      // 检查源节点和目标位置是否在同一水平线上
      const sameLevel = Math.abs(dy) < 5

      // 如果源节点和目标位置在同一水平线上，使用更简单的曲线
      if (sameLevel) {
        // 使用简单的贝塞尔曲线，减少弯曲度
        const offsetX = dx * 0.3

        const controlPoint1 = {
          x: startX + offsetX,
          y: startY
        }

        const controlPoint2 = {
          x: connectingPosition.value.x - offsetX,
          y: connectingPosition.value.y
        }

        return `M ${startX} ${startY} C ${controlPoint1.x} ${controlPoint1.y}, ${controlPoint2.x} ${controlPoint2.y}, ${connectingPosition.value.x} ${connectingPosition.value.y}`
      }

      // 如果源节点和目标位置不在同一水平线上，使用更复杂的曲线
      let offsetX = dx * 0.4
      let verticalCurveFactor = 0.25

      // 如果水平距离很小，增加水平控制点距离，避免曲线过于陡峭
      if (dx < 100) {
        offsetX = 40 // 固定最小水平控制点距离
      }

      // 如果垂直距离很大，增加水平控制点距离，使曲线更平滑
      if (Math.abs(dy) > 150) {
        offsetX = Math.max(dx * 0.5, 60)
        verticalCurveFactor = 0.2
      }

      // 如果垂直距离很小，减小垂直控制点影响，避免曲线过于弯曲
      if (Math.abs(dy) < 50 && Math.abs(dy) >= 5) {
        verticalCurveFactor = 0.1
      }

      // 为不同垂直方向的连线使用不同的曲线形状
      let controlPoint1, controlPoint2

      if (dy < 0) {
        // 目标在上方，使用不同的控制点计算方式
        controlPoint1 = {
          x: startX + offsetX,
          y: startY + (dy * verticalCurveFactor)
        }

        controlPoint2 = {
          x: connectingPosition.value.x - offsetX,
          y: connectingPosition.value.y - (dy * verticalCurveFactor)
        }
      } else {
        // 目标在下方，使用标准控制点
        controlPoint1 = {
          x: startX + offsetX,
          y: startY + (dy * verticalCurveFactor)
        }

        controlPoint2 = {
          x: connectingPosition.value.x - offsetX,
          y: connectingPosition.value.y - (dy * verticalCurveFactor)
        }
      }

      return `M ${startX} ${startY} C ${controlPoint1.x} ${controlPoint1.y}, ${controlPoint2.x} ${controlPoint2.y}, ${connectingPosition.value.x} ${connectingPosition.value.y}`
    }

    // 使用前面定义的buildNodeTree函数

    // 使用前面定义的布局常量
    const FIXED_HORIZONTAL_SPACING = 400 // 子节点与父节点之间的固定水平间距 - 增加到400以确保各层级节点间距一致
    const VERTICAL_SPACING = 180 // 基础垂直间距
    const PARENT_UNCLE_VERTICAL_SPACING = 280 // 父节点与叔叔节点之间的垂直间距
    const ROOT_X = 100 // 根节点的起始X坐标
    const ROOT_Y = 150 // 根节点的起始Y坐标
    const HORIZONTAL_SPACING = FIXED_HORIZONTAL_SPACING // 水平间距 - 确保所有层级使用相同的水平间距
    const PARENT_CHILD_SPACING = FIXED_HORIZONTAL_SPACING // 父子节点间距 - 确保与其他层级间距一致
    const SIBLING_VERTICAL_SPACING = 220 // 兄弟节点之间的垂直间距
    const COUSIN_VERTICAL_SPACING = 150 // 表节点之间的垂直间距
    const MIN_NODE_DISTANCE = 30 // 节点之间的最小距离
    const MAX_ITERATIONS = 5 // 最大迭代次数
    const MIN_VERTICAL_GAP = 120 // 最小垂直间隙
    const VERTICAL_GAP = 160 // 标准垂直间隙

    // 布局常量 - 优化后的间距设置，平衡终端节点间距和父叔节点间距
    const TERMINAL_NODE_SPACING = 180 // 终端节点之间的垂直间距 - 增加到180，使终端节点间距更合理
    const CHILD_NODE_SPACING = 180 // 子节点之间的标准垂直间距 - 增加到180，与终端节点间距保持一致
    const FUTURE_NODE_SPACE_FACTOR = 1.2 // 为未来节点预留空间的因子 - 保持不变
    const PARENT_UNCLE_SPACING_INCREMENT = 60 // 每增加一个子节点，父节点与叔叔节点之间的间距增量 - 使用终端子节点间距的1/3
    const BASE_PARENT_UNCLE_SPACING = 150 // 父节点与叔叔节点之间的基础间距 - 减小到150，更合理
    const PROPAGATION_FACTOR = 1.2 // 向上传导的间距变化因子 - 减小到1.2，更合理
    const SYMMETRY_PRIORITY_FACTOR = 1.0 // 对称布局优先级因子 - 保持不变
    const MIN_PARENT_UNCLE_GAP = 180 // 父节点与叔叔节点之间的最小间距 - 减小到180，更合理
    const SIBLING_SPACING_MULTIPLIER = 1.0 // 兄弟节点间距乘数 - 保持不变
    const FORCE_PARENT_UNCLE_SPACING = true // 强制应用父节点与叔叔节点之间的间距调整 - 保持不变
    const GLOBAL_NODE_OVERLAP_CHECK = true // 启用全局节点重叠检测 - 保持不变
    const OVERLAP_SAFETY_MARGIN = 100 // 节点重叠安全边距 - 减小到100，更合理
    const ANCESTOR_PROPAGATION_DEPTH = 5 // 向上传导的祖先深度 - 减小到5，更合理
    // 使用已经定义的NODE_WIDTH和NODE_HEIGHT常量，避免重复定义
    // const NODE_WIDTH = 240 // 节点宽度 - 已在其他地方定义
    // const NODE_HEIGHT = 90 // 节点高度 - 已在其他地方定义
    const EXPONENTIAL_BASE = 3.0 // 指数增长的底数 - 增加到3.0，使间距增长更快
    const FIXED_SPACING_PER_CHILD = 1500 // 每个子节点固定增加的间距 - 新增常量

    // 基于ID集合的节点变化检测函数 - 内部版本，用于adjustParentUncleSpacing
    const detectNodeChangesInternal = (nodeMap: Map<string, any>) => {
      // 获取当前所有节点的父子关系映射
      const currentParentChildMap = new Map<string, Set<string>>();
      
      // 构建当前的父子关系映射
      nodeMap.forEach((info, nodeId) => {
        if (info.parent) {
          if (!currentParentChildMap.has(info.parent)) {
            currentParentChildMap.set(info.parent, new Set());
          }
          currentParentChildMap.get(info.parent)?.add(nodeId);
        }
      });
      
      // 与之前的父子关系映射比较，找出变化
      const changedParents = new Map<string, {addedChildren: string[], removedChildren: string[]}>();
      
      currentParentChildMap.forEach((childrenSet, parentId) => {
        // 获取之前的子节点集合
        const previousChildrenSet = previousState.value.previousParentChildMap.get(parentId) || new Set<string>();
        
        // 找出新增的子节点
        const addedChildren = Array.from(childrenSet).filter(childId => !previousChildrenSet.has(childId));
        
        // 找出删除的子节点
        const removedChildren = Array.from(previousChildrenSet).filter(childId => !childrenSet.has(childId));
        
        // 如果有变化，记录下来
        if (addedChildren.length > 0 || removedChildren.length > 0) {
          changedParents.set(parentId, { addedChildren, removedChildren });
        }
      });
      
      // 检查之前存在但现在不存在的父节点
      previousState.value.previousParentChildMap.forEach((_, parentId) => {
        if (!currentParentChildMap.has(parentId)) {
          // 这个父节点已经被删除，所有子节点都被视为已删除
          const previousChildren = Array.from(previousState.value.previousParentChildMap.get(parentId) || new Set<string>());
          if (previousChildren.length > 0) {
            changedParents.set(parentId, { addedChildren: [], removedChildren: previousChildren });
          }
        }
      });
      
      // 更新之前的父子关系映射，用于下次比较
      previousState.value.previousParentChildMap = new Map();
      currentParentChildMap.forEach((childrenSet, parentId) => {
        previousState.value.previousParentChildMap.set(parentId, new Set(childrenSet));
      });
      
      return changedParents;
    };

    // 节点间距优化函数已经在前面定义，这里不再重复定义
    
    // 自动布局应用函数 - 优化版本 v5
    const applyAutoLayout = (force = false) => {
      if (!autoLayoutEnabled.value && !force) return;
      
      // 添加全局优化标志 - 允许在每次布局应用时自动优化重叠节点
      const AUTO_OPTIMIZE_OVERLAPS = true;

      // 强制执行时，重新启用自动布局
      if (force) {
        autoLayoutEnabled.value = true
      }

      const nodeCount = nodes.value.length
      if (nodeCount === 0) return

      // 保存当前节点位置，用于计算相对位置变化
      const originalPositions = new Map(
        nodes.value.map(node => [node.id, { ...node.position }])
      )

      // 记录当前所有节点的初始位置，用于后续检查是否有位置变化
      const initialPositions = new Map(
        nodes.value.map(node => [node.id, { ...node.position }])
      )

      // 记录需要重新布局的节点集合
      const nodesToReposition = new Set<string>()

      // 记录已经处理过的节点，避免重复处理
      const processedNodes = new Set<string>()

      // 用于跟踪只有一个子节点的父节点
      let singleParentNodes = new Set<string>()

      // 构建增强的节点关系树 - 包含终端节点信息和节点关系
      const { nodeMap, rootNodes } = buildNodeTree()

      // 检查是否有最近添加的节点，如果有，强制调整其父节点与叔叔节点之间的间距
      if (previousState.value.lastAddedNodeId && previousState.value.lastAddedNodeParentId) {
        const lastAddedNodeId = previousState.value.lastAddedNodeId
        const lastAddedNodeParentId = previousState.value.lastAddedNodeParentId

        console.log(`自动布局中检测到最近添加的节点: ${lastAddedNodeId}，父节点: ${lastAddedNodeParentId}`)

        // 使用强制调整函数处理父节点与叔叔节点之间的间距
        if (lastAddedNodeParentId) {
          const moved = forceAdjustParentUncleSpacing(nodeMap, lastAddedNodeParentId)
          console.log('自动布局中强制调整父节点与叔叔节点间距完成，是否有节点被移动:', moved)
        }
      }

      // 使用顶部定义的布局常量

      // 预处理函数 - 专注于调整父节点和叔叔节点之间的间距
      const preAdjustParentUncleSpacing = (nodeMap: Map<string, any>, rootNodes: string[]) => {
        console.log('执行预处理：调整父节点与叔叔节点之间的间距')

        // 记录所有需要移动的节点及其新位置
        const nodesToMove = new Map<string, {x: number, y: number}>()

        // 按照深度从大到小排序节点，先处理深层节点，再处理浅层节点
        const nodesByDepth = new Map<number, string[]>()

        // 计算每个节点的深度并按深度分组
        nodeMap.forEach((info, nodeId) => {
          const depth = info.depth || 0
          if (!nodesByDepth.has(depth)) {
            nodesByDepth.set(depth, [])
          }
          nodesByDepth.get(depth)?.push(nodeId)
        })

        // 获取最大深度
        const maxDepth = Math.max(...Array.from(nodesByDepth.keys()))

        // 从最深层开始处理，向上传导间距变化
        for (let depth = maxDepth; depth >= 1; depth--) {
          const nodesAtDepth = nodesByDepth.get(depth) || []

          // 按照父节点分组处理同一深度的节点
          const nodesByParent = new Map<string, string[]>()

          // 收集所有有父节点的节点
          for (const nodeId of nodesAtDepth) {
            const nodeInfo = nodeMap.get(nodeId)
            if (!nodeInfo || !nodeInfo.parent) continue

            const parentId = nodeInfo.parent
            if (!nodesByParent.has(parentId)) {
              nodesByParent.set(parentId, [])
            }
            nodesByParent.get(parentId)?.push(nodeId)
          }

          // 处理每组兄弟节点的父节点
          for (const [parentId, childIds] of nodesByParent.entries()) {
            if (childIds.length === 0) continue

            const parentInfo = nodeMap.get(parentId)
            if (!parentInfo || !parentInfo.parent) continue

            const grandparentId = parentInfo.parent
            const grandparentInfo = nodeMap.get(grandparentId)
            if (!grandparentInfo) continue

            // 获取父节点的所有兄弟节点（叔叔节点）
            const uncleIds = grandparentInfo.children.filter(id => id !== parentId)
            if (uncleIds.length === 0) continue

            // 获取父节点
            const parentNode = getNodeById(parentId)
            if (!parentNode) continue

            // 获取所有叔叔节点
            const uncleNodes = uncleIds
              .map(id => getNodeById(id))
              .filter(Boolean)

            if (uncleNodes.length === 0) continue

            // 按Y坐标排序所有叔叔节点
            const sortedUncleNodes = [...uncleNodes].sort((a, b) => a.position.y - b.position.y)

            // 计算父节点需要的空间 - 关键部分：根据子节点数量计算所需空间
            const childrenCount = parentInfo.children.length

            // 对于任何有子节点的父节点，都视为有新增子节点，确保规则3的实现
            const hasNewChildren = true

            // 修改：使用固定的常数间距，避免指数增长导致的过大间距
            // 基础间距是固定的常数值，确保节点树之间的间距始终保持合理
            let baseSpacing = 120 // 固定的节点树间距常数
            
            // 如果有多个子节点，只增加少量额外间距，限制最大增量
            if (childrenCount > 1) {
              // 最多增加固定的额外间距，无论子节点数量多少
              baseSpacing += Math.min((childrenCount - 1) * 20, 40) // 每增加一个子节点增加20px间距，最多增加40px
            }
            
            console.log(`节点 ${parentId} 有${childrenCount}个子节点，应用固定基础间距 ${baseSpacing}px`)
            
            // 取消指数增长，使用固定间距
            const finalSpacing = baseSpacing

            console.log('父叔节点间距计算:', {
              parentId,
              childrenCount,
              hasNewChildren,
              baseSpacing,
              exponentialFactor,
              requiredSpacing,
              finalSpacing,
              forceSpacing: FORCE_PARENT_UNCLE_SPACING
            })

            // 找出父节点上方的所有叔叔节点
            const unclesAbove = sortedUncleNodes.filter(uncle => uncle.position.y < parentNode.position.y)

            // 找出父节点下方的所有叔叔节点
            const unclesBelow = sortedUncleNodes.filter(uncle => uncle.position.y > parentNode.position.y)

            // 处理父节点与上方叔叔节点的间距
            if (unclesAbove.length > 0) {
              // 获取最近的上方叔叔节点
              const closestUncleAbove = unclesAbove[unclesAbove.length - 1]

              // 计算当前间距
              const currentGap = parentNode.position.y - (closestUncleAbove.position.y + NODE_HEIGHT)

              // 如果间距不足，向上移动上方的叔叔节点及其所有子节点
              if (currentGap < finalSpacing) {
                const additionalSpace = finalSpacing - currentGap

                console.log(`增加父节点(${parentId})与上方叔叔节点(${closestUncleAbove.id})的间距:`, {
                  currentGap,
                  requiredSpacing,
                  finalSpacing,
                  additionalSpace
                })

                // 移动所有上方的叔叔节点，而不是移动父节点
                for (const uncle of unclesAbove) {
                  // 考虑节点可能已经被移动过
                  const currentY = nodesToMove.has(uncle.id)
                    ? nodesToMove.get(uncle.id)!.y
                    : uncle.position.y

                  // 向上移动叔叔节点（减小Y坐标）
                  nodesToMove.set(uncle.id, {
                    x: uncle.position.x,
                    y: currentY - additionalSpace
                  })

                  // 同时移动叔叔节点的所有子节点
                  const uncleInfo = nodeMap.get(uncle.id)
                  if (uncleInfo && uncleInfo.children.length > 0) {
                    uncleInfo.children.forEach(cousinId => {
                      const cousinNode = getNodeById(cousinId)
                      if (!cousinNode) return

                      // 考虑节点可能已经被移动过
                      const currentCousinY = nodesToMove.has(cousinId)
                        ? nodesToMove.get(cousinId)!.y
                        : cousinNode.position.y

                      nodesToMove.set(cousinId, {
                        x: cousinNode.position.x,
                        y: currentCousinY - additionalSpace
                      })
                    })
                  }
                }
              }
            }

            // 处理父节点与下方叔叔节点的间距
            if (unclesBelow.length > 0) {
              // 获取最近的下方叔叔节点
              const closestUncleBelow = unclesBelow[0]

              // 计算当前间距
              const currentGap = closestUncleBelow.position.y - (parentNode.position.y + NODE_HEIGHT)

              // 如果间距不足，向下移动下方的叔叔节点及其所有子节点
              if (currentGap < finalSpacing) {
                const additionalSpace = finalSpacing - currentGap

                console.log(`增加父节点(${parentId})与下方叔叔节点(${closestUncleBelow.id})的间距:`, {
                  currentGap,
                  requiredSpacing,
                  finalSpacing,
                  additionalSpace
                })

                // 移动所有下方的叔叔节点，而不是移动父节点
                for (const uncle of unclesBelow) {
                  // 考虑节点可能已经被移动过
                  const currentY = nodesToMove.has(uncle.id)
                    ? nodesToMove.get(uncle.id)!.y
                    : uncle.position.y

                  // 向下移动叔叔节点（增加Y坐标）
                  nodesToMove.set(uncle.id, {
                    x: uncle.position.x,
                    y: currentY + additionalSpace
                  })

                  // 同时移动叔叔节点的所有子节点
                  const uncleInfo = nodeMap.get(uncle.id)
                  if (uncleInfo && uncleInfo.children.length > 0) {
                    uncleInfo.children.forEach(cousinId => {
                      const cousinNode = getNodeById(cousinId)
                      if (!cousinNode) return

                      // 考虑节点可能已经被移动过
                      const currentCousinY = nodesToMove.has(cousinId)
                        ? nodesToMove.get(cousinId)!.y
                        : cousinNode.position.y

                      nodesToMove.set(cousinId, {
                        x: cousinNode.position.x,
                        y: currentCousinY + additionalSpace
                      })
                    })
                  }
                }
              }
            }
          }
        }

        // 执行所有节点移动操作
        nodesToMove.forEach((newPosition, nodeId) => {
          const node = getNodeById(nodeId)
          if (!node) return

          // 移动节点
          nodeStore.moveNode(nodeId, newPosition)

          // 同时移动该节点的所有子节点，保持相对位置不变
          const nodeInfo = nodeMap.get(nodeId)
          if (nodeInfo && nodeInfo.children.length > 0) {
            const deltaY = newPosition.y - node.position.y

            if (Math.abs(deltaY) > 1) { // 只有当移动距离足够大时才移动子节点
              nodeInfo.children.forEach(childId => {
                const childNode = getNodeById(childId)
                if (!childNode) return

                nodeStore.moveNode(childId, {
                  x: childNode.position.x,
                  y: childNode.position.y + deltaY
                })
              })
            }
          }
        })

        return nodesToMove.size > 0 // 返回是否有节点被移动
      }

      // 防止父节点下移和表节点穿插的特殊处理函数 - 优化实现规则3
      const preventParentShiftAndCousinsIntermixing = (nodeMap: Map<string, any>, rootNodes: string[]) => {
        // 按照父节点分组所有节点
        const nodesByParent = new Map<string, string[]>()

        // 收集所有有父节点的节点
        nodeMap.forEach((info, nodeId) => {
          if (info.parent) {
            if (!nodesByParent.has(info.parent)) {
              nodesByParent.set(info.parent, [])
            }
            nodesByParent.get(info.parent)?.push(nodeId)
          }
        })

        // 处理每个父节点的子节点
        nodesByParent.forEach((childIds, parentId) => {
          if (childIds.length === 0) return

          const parentNode = getNodeById(parentId)
          if (!parentNode) return

          const parentInfo = nodeMap.get(parentId)
          if (!parentInfo) return

          // 获取父节点的兄弟节点（叔叔节点）
          const uncleNodes: string[] = []
          if (parentInfo.parent) {
            const grandparentInfo = nodeMap.get(parentInfo.parent)
            if (grandparentInfo) {
              // 获取所有叔叔节点（父节点的兄弟节点）
              uncleNodes.push(...grandparentInfo.children.filter(id => id !== parentId))
            }
          }

          // 如果只有一个子节点，确保与父节点水平对齐 - 规则1
          if (childIds.length === 1) {
            const childId = childIds[0]
            const childNode = getNodeById(childId)
            if (!childNode) return

            // 计算父节点中心点X坐标
            const parentCenterX = parentNode.position.x + (parentNode.size?.width || NODE_WIDTH) / 2
            const childNodeWidth = childNode.size?.width || NODE_WIDTH

            // 计算子节点应该的X坐标，使其中心与父节点中心水平对齐
            const childX = parentCenterX - childNodeWidth / 2 + FIXED_HORIZONTAL_SPACING

            // 确保子节点与父节点水平对齐 - 规则1
            // 关键：使用父节点的Y坐标，确保水平对齐，即使父节点位置发生变化
            nodeStore.moveNode(childId, {
              x: childX,
              y: parentNode.position.y // 使用父节点的Y坐标，确保水平对齐
            })
          }
          // 如果有多个子节点，确保它们对称分布，并且不会与表节点穿插 - 规则2和规则3
          else if (childIds.length > 1) {
            // 获取所有子节点
            const childNodes = childIds.map(id => getNodeById(id)).filter(Boolean)

            // 检查是否所有子节点都是终端节点 - 规则2
            const allTerminalNodes = childIds.every(childId => {
              const childInfo = nodeMap.get(childId)
              return childInfo && childInfo.isTerminalNode
            })

            // 使用适当的垂直间距 - 保持子节点间距不变
            // 对于终端节点使用较小的间距，确保子节点间距不会过大
            const spacing = allTerminalNodes ? TERMINAL_NODE_SPACING : CHILD_NODE_SPACING

            // 记录日志，便于调试
            console.log('子节点间距计算:', {
              parentId,
              childrenCount: childNodes.length,
              allTerminalNodes,
              spacing
            })

            // 计算父节点中心点
            const parentCenterY = parentNode.position.y + NODE_HEIGHT / 2

            // 计算子节点的总高度
            const totalChildHeight = (childNodes.length - 1) * spacing + NODE_HEIGHT

            // 计算第一个子节点的Y坐标，使所有子节点对称分布在父节点两侧
            const firstChildY = parentCenterY - totalChildHeight / 2

            // 计算父节点中心点X坐标
            const parentCenterX = parentNode.position.x + (parentNode.size?.width || NODE_WIDTH) / 2
            const childNodeWidth = NODE_WIDTH

            // 计算子节点应该的X坐标，使其中心与父节点中心水平对齐
            const childX = parentCenterX - childNodeWidth / 2 + FIXED_HORIZONTAL_SPACING

            // 获取所有表节点（叔叔节点的子节点）
            const cousinNodes: {id: string, y: number, parentId: string}[] = []

            uncleNodes.forEach(uncleId => {
              const uncleInfo = nodeMap.get(uncleId)
              if (uncleInfo && uncleInfo.children.length > 0) {
                uncleInfo.children.forEach(cousinId => {
                  const cousinNode = getNodeById(cousinId)
                  if (cousinNode) {
                    cousinNodes.push({
                      id: cousinId,
                      y: cousinNode.position.y,
                      parentId: uncleId
                    })
                  }
                })
              }
            })

            // 按Y坐标排序子节点
            childNodes.sort((a, b) => a.position.y - b.position.y)

            // 重新布局所有子节点，确保它们不会与表节点穿插
            childNodes.forEach((childNode, index) => {
              // 计算子节点的新Y坐标
              let newY = firstChildY + index * spacing

              // 检查是否与表节点重叠或穿插
              let hasOverlap = true
              let adjustmentsMade = false

              while (hasOverlap) {
                hasOverlap = false

                // 检查是否与表节点重叠
                for (const cousin of cousinNodes) {
                  // 计算所需的最小间距 - 实现规则3：增大父节点与叔叔节点之间的间距
                  // 使用更激进的间距计算，确保有足够的空间
                  const minRequiredGap = NODE_HEIGHT * FUTURE_NODE_SPACE_FACTOR * 3.0 // 增加倍数从2.0到3.0，确保更大的间距

                  if (Math.abs(cousin.y - newY) < minRequiredGap) {
                    hasOverlap = true
                    adjustmentsMade = true

                    // 增加间距，确保有足够空间 - 使用更大的增量
                    const additionalGap = minRequiredGap - Math.abs(cousin.y - newY) + PARENT_UNCLE_SPACING_INCREMENT * 3 // 增加倍数从2到3，确保更大的间距
                    console.log(`检测到子节点与表节点重叠，增加间距:`, {
                      childId: childNode.id,
                      cousinId: cousin.id,
                      currentGap: Math.abs(cousin.y - newY),
                      minRequiredGap,
                      additionalGap
                    })

                    newY += additionalGap

                    // 向上传导间距变化 - 实现规则3的向上传导效果
                    // 使用更激进的传导效果，确保间距变化向上传导
                    if (parentInfo.parent) {
                      // 使用更大的传导因子，确保间距变化充分向上传导
                      const additionalSpace = PARENT_UNCLE_SPACING_INCREMENT * PROPAGATION_FACTOR * 3.0 // 增加倍数从2.0到3.0，确保更强的传导效果

                      console.log(`向上传导间距变化:`, {
                        parentId,
                        additionalSpace
                      })

                      // 获取叔叔节点
                      const uncleNode = getNodeById(cousin.parentId)
                      if (uncleNode) {
                        // 如果叔叔节点在父节点下方，向下移动叔叔节点
                        if (uncleNode.position.y > parentNode.position.y) {
                          console.log(`叔叔节点在下方，向下移动叔叔节点(${cousin.parentId})`)

                          nodeStore.moveNode(cousin.parentId, {
                            x: uncleNode.position.x,
                            y: uncleNode.position.y + additionalSpace
                          })

                          // 同时移动叔叔节点的所有子节点
                          const uncleInfo = nodeMap.get(cousin.parentId)
                          if (uncleInfo && uncleInfo.children.length > 0) {
                            console.log(`同时移动叔叔节点(${cousin.parentId})的所有子节点:`, uncleInfo.children)

                            uncleInfo.children.forEach(cousinId => {
                              const cousinNode = getNodeById(cousinId)
                              if (cousinNode) {
                                nodeStore.moveNode(cousinId, {
                                  x: cousinNode.position.x,
                                  y: cousinNode.position.y + additionalSpace
                                })
                              }
                            })
                          }
                        }
                        // 如果叔叔节点在父节点上方，向下移动父节点
                        else {
                          console.log(`叔叔节点在上方，向下移动父节点(${parentId})`)

                          nodeStore.moveNode(parentId, {
                            x: parentNode.position.x,
                            y: parentNode.position.y + additionalSpace
                          })

                          // 同时移动父节点的所有子节点
                          if (parentInfo.children.length > 0) {
                            console.log(`同时移动父节点(${parentId})的所有子节点:`, parentInfo.children)

                            parentInfo.children.forEach(childId => {
                              if (childId !== childNode.id) { // 避免移动当前正在处理的子节点
                                const siblingNode = getNodeById(childId)
                                if (siblingNode) {
                                  nodeStore.moveNode(childId, {
                                    x: siblingNode.position.x,
                                    y: siblingNode.position.y + additionalSpace
                                  })
                                }
                              }
                            })
                          }

                          // 更新当前子节点的Y坐标基准
                          newY += additionalSpace
                        }

                        // 递归向上传导间距变化
                        const grandparentId = parentInfo.parent
                        const grandparentInfo = nodeMap.get(grandparentId)
                        if (grandparentInfo && grandparentInfo.parent) {
                          // 递归调用强制调整函数，确保间距变化向上传导
                          forceAdjustParentUncleSpacing(nodeMap, grandparentId)
                        }
                      }
                    }

                    break
                  }
                }
              }

              // 移动子节点到新位置
              nodeStore.moveNode(childNode.id, {
                x: childX,
                y: newY
              })

              // 递归处理子节点的子节点
              const childInfo = nodeMap.get(childNode.id)
              if (childInfo && childInfo.children.length > 0) {
                // 如果子节点只有一个子节点，确保与子节点水平对齐
                if (childInfo.children.length === 1) {
                  const grandchildId = childInfo.children[0]
                  const grandchildNode = getNodeById(grandchildId)
                  if (grandchildNode) {
                    // 计算子节点中心点X坐标
                    const childCenterX = childNode.position.x + (childNode.size?.width || NODE_WIDTH) / 2
                    const grandchildWidth = grandchildNode.size?.width || NODE_WIDTH

                    // 计算孙子节点应该的X坐标，使其中心与子节点中心水平对齐
                    const grandchildX = childCenterX - grandchildWidth / 2 + FIXED_HORIZONTAL_SPACING

                    // 将孙子节点与子节点水平对齐
                    nodeStore.moveNode(grandchildId, {
                      x: grandchildX,
                      y: newY // 使用子节点的Y坐标，确保水平对齐
                    })
                  }
                }
                // 如果子节点有多个子节点，确保它们对称分布
                else if (childInfo.children.length > 1) {
                  const grandchildIds = childInfo.children
                  const grandchildNodes = grandchildIds.map(id => getNodeById(id)).filter(Boolean)

                  // 计算子节点中心点
                  const childCenterY = newY + NODE_HEIGHT / 2

                  // 计算孙子节点的总高度
                  const totalGrandchildHeight = (grandchildNodes.length - 1) * VERTICAL_SPACING + NODE_HEIGHT

                  // 计算第一个孙子节点的Y坐标，使所有孙子节点对称分布在子节点两侧
                  const firstGrandchildY = childCenterY - totalGrandchildHeight / 2

                  // 计算子节点中心点X坐标
                  const childCenterX = childNode.position.x + (childNode.size?.width || NODE_WIDTH) / 2
                  const grandchildWidth = NODE_WIDTH

                  // 计算孙子节点应该的X坐标，使其中心与子节点中心水平对齐
                  const grandchildX = childCenterX - grandchildWidth / 2 + FIXED_HORIZONTAL_SPACING

                  // 重新布局所有孙子节点
                  grandchildNodes.forEach((grandchildNode, idx) => {
                    const grandchildY = firstGrandchildY + idx * VERTICAL_SPACING

                    nodeStore.moveNode(grandchildNode.id, {
                      x: grandchildX,
                      y: grandchildY
                    })
                  })
                }
              }
            })
          }
        })

        // 最后检查所有节点，确保没有重叠
        const allNodes = nodes.value

        // 按Y坐标排序节点，确保从上到下处理
        const sortedNodes = [...allNodes].sort((a, b) => a.position.y - b.position.y)

        for (let i = 0; i < sortedNodes.length; i++) {
          for (let j = i + 1; j < sortedNodes.length; j++) {
            const nodeA = sortedNodes[i]
            const nodeB = sortedNodes[j]

            // 检查节点是否在同一列（X坐标接近）
            const sameColumn = Math.abs(nodeA.position.x - nodeB.position.x) < NODE_WIDTH / 2

            if (sameColumn) {
              // 检查垂直距离
              const verticalDistance = Math.abs(nodeA.position.y - nodeB.position.y)

              // 如果距离太近，调整位置
              if (verticalDistance < NODE_HEIGHT) {
                // 获取节点A和节点B的信息
                const nodeInfoA = nodeMap.get(nodeA.id)
                const nodeInfoB = nodeMap.get(nodeB.id)

                // 检查节点是否属于同一个父节点
                const sameParent = nodeInfoA && nodeInfoB && nodeInfoA.parent === nodeInfoB.parent

                if (sameParent) {
                  // 如果属于同一个父节点，按照原来的顺序调整位置
                  const parentInfo = nodeMap.get(nodeInfoA?.parent || '')
                  if (parentInfo) {
                    const indexA = parentInfo.children.indexOf(nodeA.id)
                    const indexB = parentInfo.children.indexOf(nodeB.id)

                    // 确保节点按照原来的顺序排列
                    if (indexA < indexB) {
                      // 向下移动节点B
                      nodeStore.moveNode(nodeB.id, {
                        x: nodeB.position.x,
                        y: nodeA.position.y + NODE_HEIGHT + VERTICAL_SPACING
                      })
                    } else {
                      // 向下移动节点A
                      nodeStore.moveNode(nodeA.id, {
                        x: nodeA.position.x,
                        y: nodeB.position.y + NODE_HEIGHT + VERTICAL_SPACING
                      })
                    }
                  }
                } else {
                  // 如果不属于同一个父节点，检查它们的父节点关系
                  const parentA = nodeInfoA?.parent ? getNodeById(nodeInfoA.parent) : null
                  const parentB = nodeInfoB?.parent ? getNodeById(nodeInfoB.parent) : null

                  if (parentA && parentB) {
                    // 检查父节点的垂直位置关系
                    if (parentA.position.y < parentB.position.y) {
                      // 父节点A在父节点B上方，向下移动节点B
                      nodeStore.moveNode(nodeB.id, {
                        x: nodeB.position.x,
                        y: nodeA.position.y + NODE_HEIGHT + VERTICAL_SPACING
                      })
                    } else {
                      // 父节点B在父节点A上方，向下移动节点A
                      nodeStore.moveNode(nodeA.id, {
                        x: nodeA.position.x,
                        y: nodeB.position.y + NODE_HEIGHT + VERTICAL_SPACING
                      })
                    }
                  } else {
                    // 如果无法确定父节点关系，默认向下移动节点B
                    nodeStore.moveNode(nodeB.id, {
                      x: nodeB.position.x,
                      y: nodeA.position.y + NODE_HEIGHT + VERTICAL_SPACING
                    })
                  }
                }
              }
            }
          }
        }
      }

      // 全局协调函数 - 处理节点位置变化的连锁反应
      const coordinateGlobalLayout = (nodeMap: Map<string, any>, rootNodes: string[]) => {
        // 记录需要重新布局的节点集合
        const nodesToReposition = new Set<string>()

        // 记录已经处理过的节点，避免重复处理
        const processedNodes = new Set<string>()

        // 记录节点的原始位置
        const originalPositions = new Map(
          nodes.value.map(node => [node.id, { ...node.position }])
        )

        // 迭代处理，直到所有节点都满足原则1-4，或达到最大迭代次数
        let iteration = 0
        let hasChanges = true

        // 第一步：处理单子节点与父节点的水平对齐问题
        const alignSingleChildNodes = () => {
          // 找出所有只有一个子节点的父节点
          const singleChildParents = new Set<string>()

          // 遍历所有节点，找出只有一个子节点的父节点
          nodeMap.forEach((info, nodeId) => {
            if (info.children.length === 1) {
              singleChildParents.add(nodeId)
            }
          })

          // 处理每个只有一个子节点的父节点
          singleChildParents.forEach(parentId => {
            const parentInfo = nodeMap.get(parentId)
            if (!parentInfo) return

            const parentNode = getNodeById(parentId)
            if (!parentNode) return

            const childId = parentInfo.children[0]
            const childNode = getNodeById(childId)
            if (!childNode) return

            // 计算父节点中心点X坐标
            const parentCenterX = parentNode.position.x + (parentNode.size?.width || NODE_WIDTH) / 2
            const childNodeWidth = childNode.size?.width || NODE_WIDTH

            // 计算子节点应该的X坐标，使其中心与父节点中心水平对齐
            const childX = parentCenterX - childNodeWidth / 2 + FIXED_HORIZONTAL_SPACING

            // 将子节点与父节点水平对齐 - 严格对齐 - 原则1
            nodeStore.moveNode(childId, {
              x: childX, // 使用计算后的X坐标，确保水平中心对齐
              y: parentNode.position.y // 使用父节点的Y坐标，确保水平对齐
            })

            // 标记为已处理
            processedNodes.add(childId)

            // 递归处理子节点的子节点
            const childInfo = nodeMap.get(childId)
            if (childInfo && childInfo.children.length === 1) {
              const grandchildId = childInfo.children[0]
              const grandchildNode = getNodeById(grandchildId)
              if (grandchildNode) {
                // 获取更新后的子节点位置
                const updatedChildNode = getNodeById(childId)
                if (updatedChildNode) {
                  // 计算子节点中心点X坐标
                  const childCenterX = updatedChildNode.position.x + (updatedChildNode.size?.width || NODE_WIDTH) / 2
                  const grandchildWidth = grandchildNode.size?.width || NODE_WIDTH

                  // 计算孙子节点应该的X坐标，使其中心与子节点中心水平对齐
                  const grandchildX = childCenterX - grandchildWidth / 2 + FIXED_HORIZONTAL_SPACING

                  // 将孙子节点与子节点水平对齐 - 原则1
                  nodeStore.moveNode(grandchildId, {
                    x: grandchildX, // 使用计算后的X坐标，确保水平中心对齐
                    y: updatedChildNode.position.y // 使用子节点的Y坐标，确保水平对齐
                  })

                  // 标记为已处理
                  processedNodes.add(grandchildId)
                }
              }
            }
          })
        }

        // 第二步：处理多子节点的分组和对称分布问题
        const arrangeMultiChildNodes = () => {
          // 找出所有有多个子节点的父节点
          const multiChildParents = new Set<string>()

          // 遍历所有节点，找出有多个子节点的父节点
          nodeMap.forEach((info, nodeId) => {
            if (info.children.length > 1) {
              multiChildParents.add(nodeId)
            }
          })

          // 按照深度排序父节点，先处理深度较小的节点
          const sortedParents = Array.from(multiChildParents).sort((a, b) => {
            const depthA = nodeMap.get(a)?.depth || 0
            const depthB = nodeMap.get(b)?.depth || 0
            return depthA - depthB
          })

          // 处理每个有多个子节点的父节点
          sortedParents.forEach(parentId => {
            const parentInfo = nodeMap.get(parentId)
            if (!parentInfo) return

            const parentNode = getNodeById(parentId)
            if (!parentNode) return

            // 获取所有子节点
            const childrenIds = parentInfo.children
            const childNodes = childrenIds.map(id => getNodeById(id)).filter(Boolean)

            if (childNodes.length <= 1) return

            // 计算父节点中心点
            const parentCenterX = parentNode.position.x + (parentNode.size?.width || NODE_WIDTH) / 2
            const parentCenterY = parentNode.position.y + NODE_HEIGHT / 2

            // 计算子节点的总高度
            const totalChildHeight = (childNodes.length - 1) * VERTICAL_SPACING + NODE_HEIGHT

            // 计算第一个子节点的Y坐标，使所有子节点对称分布在父节点两侧
            const firstChildY = parentCenterY - totalChildHeight / 2

            // 为每个子节点计算新的位置
            childrenIds.forEach((childId, index) => {
              const childNode = getNodeById(childId)
              if (!childNode) return

              // 计算子节点的X坐标，确保水平对齐
              const childNodeWidth = childNode.size?.width || NODE_WIDTH
              const childX = parentCenterX - childNodeWidth / 2 + FIXED_HORIZONTAL_SPACING

              // 计算子节点的Y坐标，确保垂直对称分布
              const childY = firstChildY + index * VERTICAL_SPACING

              // 移动子节点到新位置
              nodeStore.moveNode(childId, {
                x: childX,
                y: childY
              })

              // 标记为已处理
              processedNodes.add(childId)

              // 递归处理子节点的子节点
              const childInfo = nodeMap.get(childId)
              if (childInfo && childInfo.children.length > 0) {
                // 如果子节点有自己的子节点，递归处理
                if (childInfo.children.length === 1) {
                  // 如果只有一个孙子节点，确保与子节点水平对齐
                  const grandchildId = childInfo.children[0]
                  const grandchildNode = getNodeById(grandchildId)
                  if (grandchildNode) {
                    // 获取更新后的子节点位置
                    const updatedChildNode = getNodeById(childId)
                    if (updatedChildNode) {
                      // 计算子节点中心点X坐标
                      const childCenterX = updatedChildNode.position.x + (updatedChildNode.size?.width || NODE_WIDTH) / 2
                      const grandchildWidth = grandchildNode.size?.width || NODE_WIDTH

                      // 计算孙子节点应该的X坐标，使其中心与子节点中心水平对齐
                      const grandchildX = childCenterX - grandchildWidth / 2 + FIXED_HORIZONTAL_SPACING

                      // 将孙子节点与子节点水平对齐 - 原则1
                      nodeStore.moveNode(grandchildId, {
                        x: grandchildX, // 使用计算后的X坐标，确保水平中心对齐
                        y: updatedChildNode.position.y // 使用子节点的Y坐标，确保水平对齐
                      })

                      // 标记为已处理
                      processedNodes.add(grandchildId)
                    }
                  }
                } else if (childInfo.children.length > 1) {
                  // 如果有多个孙子节点，确保它们对称分布
                  const grandchildrenIds = childInfo.children
                  const grandchildNodes = grandchildrenIds.map(id => getNodeById(id)).filter(Boolean)

                  // 获取更新后的子节点位置
                  const updatedChildNode = getNodeById(childId)
                  if (!updatedChildNode) return

                  // 计算子节点中心点
                  const childCenterX = updatedChildNode.position.x + (updatedChildNode.size?.width || NODE_WIDTH) / 2
                  const childCenterY = updatedChildNode.position.y + NODE_HEIGHT / 2

                  // 计算孙子节点的总高度
                  const totalGrandchildHeight = (grandchildNodes.length - 1) * VERTICAL_SPACING + NODE_HEIGHT

                  // 计算第一个孙子节点的Y坐标，使所有孙子节点对称分布在子节点两侧
                  const firstGrandchildY = childCenterY - totalGrandchildHeight / 2

                  // 为每个孙子节点计算新的位置
                  grandchildrenIds.forEach((grandchildId, idx) => {
                    const grandchildNode = getNodeById(grandchildId)
                    if (!grandchildNode) return

                    // 计算孙子节点的X坐标，确保水平对齐
                    const grandchildWidth = grandchildNode.size?.width || NODE_WIDTH
                    const grandchildX = childCenterX - grandchildWidth / 2 + FIXED_HORIZONTAL_SPACING

                    // 计算孙子节点的Y坐标，确保垂直对称分布
                    const grandchildY = firstGrandchildY + idx * VERTICAL_SPACING

                    // 移动孙子节点到新位置
                    nodeStore.moveNode(grandchildId, {
                      x: grandchildX,
                      y: grandchildY
                    })

                    // 标记为已处理
                    processedNodes.add(grandchildId)
                  })
                }
              }
            })
          })
        }

        // 第三步：检查和解决节点重叠问题
        const resolveNodeOverlaps = () => {
          // 获取所有节点
          const allNodes = nodes.value

          // 按Y坐标排序节点，确保从上到下处理
          const sortedNodes = [...allNodes].sort((a, b) => a.position.y - b.position.y)

          // 检查每对节点是否重叠
          for (let i = 0; i < sortedNodes.length; i++) {
            for (let j = i + 1; j < sortedNodes.length; j++) {
              const nodeA = sortedNodes[i]
              const nodeB = sortedNodes[j]

              // 检查节点是否在同一列（X坐标接近）
              const sameColumn = Math.abs(nodeA.position.x - nodeB.position.x) < NODE_WIDTH / 2

              if (sameColumn) {
                // 检查垂直距离
                const verticalDistance = Math.abs(nodeA.position.y - nodeB.position.y)

                // 如果距离太近，调整位置
                if (verticalDistance < NODE_HEIGHT) {
                  // 获取节点A和节点B的信息
                  const nodeInfoA = nodeMap.get(nodeA.id)
                  const nodeInfoB = nodeMap.get(nodeB.id)

                  // 检查节点是否属于同一个父节点
                  const sameParent = nodeInfoA && nodeInfoB && nodeInfoA.parent === nodeInfoB.parent

                  if (sameParent) {
                    // 如果属于同一个父节点，按照原来的顺序调整位置
                    const parentInfo = nodeMap.get(nodeInfoA?.parent || '')
                    if (parentInfo) {
                      const indexA = parentInfo.children.indexOf(nodeA.id)
                      const indexB = parentInfo.children.indexOf(nodeB.id)

                      // 确保节点按照原来的顺序排列
                      if (indexA < indexB) {
                        // 向下移动节点B
                        nodeStore.moveNode(nodeB.id, {
                          x: nodeB.position.x,
                          y: nodeA.position.y + NODE_HEIGHT + VERTICAL_SPACING
                        })
                      } else {
                        // 向下移动节点A
                        nodeStore.moveNode(nodeA.id, {
                          x: nodeA.position.x,
                          y: nodeB.position.y + NODE_HEIGHT + VERTICAL_SPACING
                        })
                      }
                    }
                  } else {
                    // 如果不属于同一个父节点，检查它们的父节点关系
                    const parentA = nodeInfoA?.parent ? getNodeById(nodeInfoA.parent) : null
                    const parentB = nodeInfoB?.parent ? getNodeById(nodeInfoB.parent) : null

                    if (parentA && parentB) {
                      // 检查父节点的垂直位置关系
                      if (parentA.position.y < parentB.position.y) {
                        // 父节点A在父节点B上方，向下移动节点B
                        nodeStore.moveNode(nodeB.id, {
                          x: nodeB.position.x,
                          y: nodeA.position.y + NODE_HEIGHT + VERTICAL_SPACING
                        })
                      } else {
                        // 父节点B在父节点A上方，向下移动节点A
                        nodeStore.moveNode(nodeA.id, {
                          x: nodeA.position.x,
                          y: nodeB.position.y + NODE_HEIGHT + VERTICAL_SPACING
                        })
                      }
                    } else {
                      // 如果无法确定父节点关系，默认向下移动节点B
                      nodeStore.moveNode(nodeB.id, {
                        x: nodeB.position.x,
                        y: nodeA.position.y + NODE_HEIGHT + VERTICAL_SPACING
                      })
                    }
                  }

                  // 标记为已更改
                  hasChanges = true
                }
              } else {
                // 检查节点是否有水平和垂直重叠
                const horizontalOverlap =
                  nodeA.position.x < nodeB.position.x + NODE_WIDTH &&
                  nodeA.position.x + NODE_WIDTH > nodeB.position.x

                const verticalOverlap =
                  nodeA.position.y < nodeB.position.y + NODE_HEIGHT &&
                  nodeA.position.y + NODE_HEIGHT > nodeB.position.y

                // 如果两个节点重叠
                if (horizontalOverlap && verticalOverlap) {
                  // 水平移动节点B，避免重叠
                  nodeStore.moveNode(nodeB.id, {
                    x: nodeA.position.x + NODE_WIDTH + 20,
                    y: nodeB.position.y
                  })

                  // 标记为已更改
                  hasChanges = true
                }
              }
            }
          }
        }

        // 第四步：确保表节点不会穿插在子节点之间
        const ensureProperNodeGrouping = () => {
          // 按照父节点分组所有节点
          const nodesByParent = new Map<string, string[]>()

          // 收集所有有父节点的节点
          nodeMap.forEach((info, nodeId) => {
            if (info.parent) {
              if (!nodesByParent.has(info.parent)) {
                nodesByParent.set(info.parent, [])
              }
              nodesByParent.get(info.parent)?.push(nodeId)
            }
          })

          // 处理每个父节点的子节点
          nodesByParent.forEach((childIds, parentId) => {
            if (childIds.length <= 1) return

            const parentNode = getNodeById(parentId)
            if (!parentNode) return

            // 获取所有子节点
            const childNodes = childIds.map(id => getNodeById(id)).filter(Boolean)

            // 按Y坐标排序子节点
            childNodes.sort((a, b) => a.position.y - b.position.y)

            // 计算父节点中心点
            const parentCenterY = parentNode.position.y + NODE_HEIGHT / 2

            // 计算子节点的总高度
            const totalChildHeight = (childNodes.length - 1) * VERTICAL_SPACING + NODE_HEIGHT

            // 计算第一个子节点的Y坐标，使所有子节点对称分布在父节点两侧
            const firstChildY = parentCenterY - totalChildHeight / 2

            // 重新布局所有子节点，确保它们按顺序排列，不会被其他节点穿插
            childNodes.forEach((childNode, index) => {
              // 计算子节点的新Y坐标
              const newY = firstChildY + index * VERTICAL_SPACING

              // 移动子节点到新位置
              nodeStore.moveNode(childNode.id, {
                x: childNode.position.x,
                y: newY
              })

              // 标记为已处理
              processedNodes.add(childNode.id)
            })
          })
        }

        // 主循环：迭代处理，直到所有节点都满足原则1-5
        while (hasChanges && iteration < MAX_ITERATIONS) {
          hasChanges = false
          iteration++

          // 清空已处理节点集合
          processedNodes.clear()

          // 第一步：处理单子节点与父节点的水平对齐问题
          alignSingleChildNodes()

          // 第二步：处理多子节点的分组和对称分布问题
          arrangeMultiChildNodes()

          // 第三步：确保表节点不会穿插在子节点之间
          ensureProperNodeGrouping()

          // 第四步：检查和解决节点重叠问题
          resolveNodeOverlaps()

          // 如果有节点被处理过，标记为有变化
          if (processedNodes.size > 0) {
            hasChanges = true
          }
        }

        // 最后一次全局检查，确保所有节点都满足原则1-5
        balanceSubtrees()
      }

      // 计算节点的深度和每个深度层的节点数量
      const depthMap = new Map<number, string[]>()

      // 递归计算节点深度
      const calculateDepth = (nodeId: string, depth: number = 0) => {
        const nodeInfo = nodeMap.get(nodeId)
        if (!nodeInfo) return

        // 记录当前深度的节点
        if (!depthMap.has(depth)) {
          depthMap.set(depth, [])
        }
        depthMap.get(depth)?.push(nodeId)

        // 递归计算子节点深度
        nodeInfo.children.forEach(childId => {
          calculateDepth(childId, depth + 1)
        })
      }

      // 从根节点开始计算深度
      rootNodes.forEach(rootId => {
        calculateDepth(rootId)
      })

      // 如果没有根节点（可能是循环依赖），使用简单的网格布局
      if (rootNodes.length === 0) {
        const NODES_PER_ROW = Math.ceil(Math.sqrt(nodeCount))

        nodes.value.forEach((node, index) => {
          const row = Math.floor(index / NODES_PER_ROW)
          const col = index % NODES_PER_ROW

          nodeStore.moveNode(node.id, {
            x: ROOT_X + col * HORIZONTAL_SPACING,
            y: ROOT_Y + row * VERTICAL_SPACING
          })
        })

        return
      }

      // 计算每个深度层的节点总数，用于更好地分配垂直空间
      let maxNodesInDepth = 0
      depthMap.forEach((nodeIds) => {
        maxNodesInDepth = Math.max(maxNodesInDepth, nodeIds.length)
      })

      // 对每个深度层的节点进行垂直布局
      depthMap.forEach((nodeIds, depth) => {
        // 根据深度确定水平位置，使用不同的间距
        const x = ROOT_X + depth * (depth === 1 ? PARENT_CHILD_SPACING : HORIZONTAL_SPACING)

        // 计算当前深度层的总高度
        // 使用更大的垂直间距，确保节点不会太靠近
        const effectiveSpacing = depth === 1 ? VERTICAL_SPACING * 1.2 : VERTICAL_SPACING
        const totalHeight = (nodeIds.length - 1) * effectiveSpacing + NODE_HEIGHT

        // 计算起始y坐标，使节点垂直居中
        // 根据最大节点数计算画布中心位置，确保布局居中
        const canvasHeight = maxNodesInDepth * effectiveSpacing
        const startY = ROOT_Y + (canvasHeight - totalHeight) / 2

        // 如果节点数量大于1，则进行垂直分布
        if (nodeIds.length > 1) {
          // 首先，按父节点分组
          const nodesByParent = new Map<string | null, string[]>()

          // 收集每个父节点的所有子节点
          nodeIds.forEach(nodeId => {
            const nodeInfo = nodeMap.get(nodeId)
            if (!nodeInfo) return

            const parentId = nodeInfo.parent || null
            if (!nodesByParent.has(parentId)) {
              nodesByParent.set(parentId, [])
            }
            nodesByParent.get(parentId)?.push(nodeId)
          })

          // 对节点进行排序，确保相同父节点的子节点相邻
          // 并且根节点的子节点按照索引顺序排列，以便后续对称布局
          nodeIds.sort((a, b) => {
            const nodeA = nodeMap.get(a)
            const nodeB = nodeMap.get(b)

            if (!nodeA || !nodeB) return 0

            // 如果有相同的父节点，则保持它们相邻
            if (nodeA.parent === nodeB.parent) {
              // 如果有相同的父节点，根据在父节点中的索引排序
              const parentNode = nodeMap.get(nodeA.parent || '')
              if (parentNode) {
                const indexA = parentNode.children.indexOf(a)
                const indexB = parentNode.children.indexOf(b)
                return indexA - indexB
              }
            }

            // 如果没有父节点，则放在前面
            if (!nodeA.parent) return -1
            if (!nodeB.parent) return 1

            // 根据父节点ID排序，确保相关节点靠近
            return nodeA.parent.localeCompare(nodeB.parent)
          })

          // 特殊处理：如果是第一层（深度为1），确保子节点对称分布在根节点两侧
          if (depth === 1) {
            // 找出所有根节点
            const rootNodeIds = rootNodes.filter(rootId => {
              // 确保根节点有子节点在当前深度
              const rootInfo = nodeMap.get(rootId)
              return rootInfo && rootInfo.children.some(childId => nodeIds.includes(childId))
            })

            // 对每个根节点的子节点进行对称布局
            rootNodeIds.forEach(rootId => {
              const rootInfo = nodeMap.get(rootId)
              if (!rootInfo) return

              const rootNode = getNodeById(rootId)
              if (!rootNode) return

              // 获取当前根节点在当前深度的所有子节点
              const childrenOfRoot = rootInfo.children.filter(childId => nodeIds.includes(childId))

              if (childrenOfRoot.length > 0) {
                // 如果只有一个子节点，直接与根节点水平对齐
                if (childrenOfRoot.length === 1) {
                  nodeStore.moveNode(childrenOfRoot[0], { x, y: rootNode.position.y })
                }
                // 如果有多个子节点，对称分布在根节点两侧
                else {
                  // 计算子节点的总高度，使用适当的间距确保对称性
                  const totalChildHeight = (childrenOfRoot.length - 1) * (VERTICAL_SPACING * 1.1) + NODE_HEIGHT

                  // 计算根节点的中心点
                  const rootCenterY = rootNode.position.y + NODE_HEIGHT / 2

                  // 计算第一个子节点的Y坐标，使所有子节点对称分布在根节点两侧
                  const firstChildY = rootCenterY - totalChildHeight / 2

                  // 布局子节点
                  childrenOfRoot.forEach((childId, index) => {
                    const childY = firstChildY + index * (VERTICAL_SPACING * 1.1)

                    // 检查是否有其他节点在相同位置
                    const existingNode = nodes.value.find(node =>
                      node.id !== childId &&
                      Math.abs(node.position.x - x) < 10 &&
                      Math.abs(node.position.y - childY) < 10
                    )

                    // 如果有节点在相同位置，稍微调整Y坐标
                    const finalY = existingNode ? childY + 20 : childY

                    nodeStore.moveNode(childId, { x, y: finalY })
                  })
                }
              }
            })
          }
        }

        // 创建父节点到子节点的映射，用于对称布局
        const parentToChildrenMap = new Map<string, string[]>()

        // 收集每个父节点的所有子节点
        nodeIds.forEach(nodeId => {
          const nodeInfo = nodeMap.get(nodeId)
          if (nodeInfo && nodeInfo.parent) {
            if (!parentToChildrenMap.has(nodeInfo.parent)) {
              parentToChildrenMap.set(nodeInfo.parent, [])
            }
            parentToChildrenMap.get(nodeInfo.parent)?.push(nodeId)
          }
        })

        // 首先处理没有父节点的节点（根节点）
        const rootNodesInDepth = nodeIds.filter(nodeId => {
          const nodeInfo = nodeMap.get(nodeId)
          return !nodeInfo || !nodeInfo.parent
        })

        // 如果有多个根节点，确保它们对称分布
        if (rootNodesInDepth.length > 1) {
          // 计算根节点的总高度
          const totalRootHeight = (rootNodesInDepth.length - 1) * VERTICAL_SPACING + NODE_HEIGHT

          // 计算第一个根节点的Y坐标，使所有根节点垂直居中
          const firstRootY = startY + (canvasHeight - totalRootHeight) / 2

          rootNodesInDepth.forEach((nodeId, index) => {
            const y = firstRootY + index * VERTICAL_SPACING
            nodeStore.moveNode(nodeId, { x, y })
          })
        } else if (rootNodesInDepth.length === 1) {
          // 如果只有一个根节点，居中放置
          const y = startY + (canvasHeight - NODE_HEIGHT) / 2
          nodeStore.moveNode(rootNodesInDepth[0], { x, y })
        }

        // 然后处理有父节点的节点，按父节点分组处理
        parentToChildrenMap.forEach((childIds, parentId) => {
          const parentNode = nodeMap.get(parentId)
          if (!parentNode) return

          const parentY = parentNode.node.position.y
          const parentCenterY = parentY + NODE_HEIGHT / 2

          // 如果只有一个子节点，确保与父节点在同一水平线上 - 原则1
          if (childIds.length === 1) {
            // 获取子节点信息
            const childId = childIds[0]
            const childNode = getNodeById(childId)
            const childInfo = nodeMap.get(childId)

            if (!childNode) return

            // 计算父节点中心点X坐标
            const parentCenterX = parentNode.node.position.x + (parentNode.node.size?.width || 240) / 2
            const childNodeWidth = childNode.size?.width || 240

            // 计算子节点应该的X坐标，使其中心与父节点中心水平对齐
            // 然后添加固定的水平偏移量，确保子节点在父节点右侧 - 原则4
            const horizontalOffset = FIXED_HORIZONTAL_SPACING

            // 确保子节点的中心点与父节点的中心点水平对齐 - 原则1
            // 子节点X坐标 = 父节点中心点X坐标 - 子节点宽度/2 + 水平偏移量
            const childX = parentCenterX - childNodeWidth / 2 + horizontalOffset

            // 无论是否有子节点，都确保与父节点严格水平对齐，并且X坐标保持水平中心对齐 - 原则1
            nodeStore.moveNode(childId, {
              x: childX, // 设置为计算后的X坐标，确保水平中心对齐
              y: parentY // 使用父节点的Y坐标，确保水平对齐
            })

            // 如果子节点有自己的子节点，需要特殊处理
            if (childInfo && childInfo.children.length > 0) {
              // 标记这个节点需要特殊处理
              if (!singleParentNodes) {
                singleParentNodes = new Set<string>()
              }
              singleParentNodes.add(childId)

              // 如果子节点只有一个子节点，立即处理对齐 - 原则1
              if (childInfo.children.length === 1) {
                const grandchildId = childInfo.children[0]
                const grandchildNode = getNodeById(grandchildId)
                if (grandchildNode) {
                  // 计算子节点中心点X坐标
                  const childCenterX = childX + (childNode.size?.width || 240) / 2
                  const grandchildWidth = grandchildNode.size?.width || 240

                  // 计算孙子节点应该的X坐标，使其中心与子节点中心水平对齐
                  const grandchildX = childCenterX - grandchildWidth / 2 + FIXED_HORIZONTAL_SPACING

                  // 将孙子节点与子节点水平对齐 - 原则1
                  nodeStore.moveNode(grandchildId, {
                    x: grandchildX, // 确保水平中心对齐
                    y: parentY // 使用相同的Y坐标，确保水平对齐
                  })
                }
              }
            }
          }
          // 如果有多个子节点，对称分布在父节点两侧 - 原则2
          else if (childIds.length > 1) {
            // 根据子节点数量动态调整间距，确保足够的间距
            const dynamicSpacing = Math.max(
              childIds.length > 3 ? SIBLING_VERTICAL_SPACING : VERTICAL_SPACING,
              NODE_HEIGHT + 30 // 确保至少有节点高度加30px的间距
            )

            // 计算子节点的总高度
            const totalChildrenHeight = (childIds.length - 1) * dynamicSpacing

            // 计算第一个子节点的Y坐标，使所有子节点对称分布在父节点两侧 - 原则2
            const firstChildY = parentCenterY - totalChildrenHeight / 2 - NODE_HEIGHT / 2

            // 对子节点进行排序，确保它们按照在父节点中的索引顺序排列
            childIds.sort((a, b) => {
              const parentInfo = nodeMap.get(parentId)
              if (!parentInfo) return 0

              const indexA = parentInfo.children.indexOf(a)
              const indexB = parentInfo.children.indexOf(b)
              return indexA - indexB
            })

            // 计算父节点中心点X坐标
            const parentCenterX = parentNode.node.position.x + (parentNode.node.size?.width || 240) / 2
            const childNodeWidth = 240 // 子节点默认宽度

            // 计算子节点应该的X坐标，使其中心与父节点中心水平对齐
            // 然后添加固定的水平偏移量，确保子节点在父节点右侧 - 原则4
            const horizontalOffset = FIXED_HORIZONTAL_SPACING

            // 确保子节点的中心点与父节点的中心点水平对齐 - 原则2
            // 子节点X坐标 = 父节点中心点X坐标 - 子节点宽度/2 + 水平偏移量
            const childX = parentCenterX - childNodeWidth / 2 + horizontalOffset

            // 布局子节点，确保每个子节点有唯一的位置 - 原则3
            // 先检查所有子节点的位置，确保没有重叠
            const childPositions: {id: string, y: number}[] = []

            childIds.forEach((childId, index) => {
              // 计算初始Y坐标
              const initialY = firstChildY + index * dynamicSpacing

              // 检查是否与已放置的节点重叠
              let finalY = initialY
              let hasOverlap = true

              while (hasOverlap) {
                hasOverlap = false

                // 检查是否与已放置的子节点重叠
                for (const pos of childPositions) {
                  if (Math.abs(pos.y - finalY) < NODE_HEIGHT) {
                    hasOverlap = true
                    finalY += NODE_HEIGHT + 10
                    break
                  }
                }

                // 检查是否与其他节点重叠
                if (!hasOverlap) {
                  const existingNode = nodes.value.find(node =>
                    node.id !== childId &&
                    Math.abs(node.position.x - childX) < NODE_WIDTH / 2 &&
                    Math.abs(node.position.y - finalY) < NODE_HEIGHT
                  )

                  if (existingNode) {
                    hasOverlap = true
                    finalY += NODE_HEIGHT + 10
                  }
                }
              }

              // 添加到已放置节点列表
              childPositions.push({id: childId, y: finalY})

              // 移动节点到计算好的位置
              nodeStore.moveNode(childId, { x: childX, y: finalY })

              // 特殊处理：确保所有层级的子节点都遵循相同的原则
              // 检查当前节点是否有子节点
              const childInfo = nodeMap.get(childId)
              if (childInfo && childInfo.children.length > 0) {
                // 获取所有子节点
                const grandchildIds = childInfo.children
                const childNode = getNodeById(childId)
                if (!childNode) return

                // 计算子节点中心点X坐标
                const childCenterX = childNode.position.x + (childNode.size?.width || 240) / 2
                const grandchildWidth = 240 // 孙子节点默认宽度

                // 计算孙子节点应该的X坐标，使其中心与子节点中心水平对齐
                // 然后添加固定的水平偏移量，确保孙子节点在子节点右侧 - 原则4
                const grandchildX = childCenterX - grandchildWidth / 2 + FIXED_HORIZONTAL_SPACING

                // 如果只有一个孙子节点，确保与子节点水平对齐 - 原则1
                if (grandchildIds.length === 1) {
                  const grandchildId = grandchildIds[0]
                  const grandchildNode = getNodeById(grandchildId)
                  if (grandchildNode) {
                    // 将孙子节点与子节点水平对齐
                    nodeStore.moveNode(grandchildId, {
                      x: grandchildX,
                      y: finalY // 使用子节点的Y坐标，确保水平对齐
                    })
                  }
                }
                // 如果有多个孙子节点，对称分布 - 原则2
                else if (grandchildIds.length > 1) {
                  // 计算子节点的总高度
                  const totalGrandchildHeight = (grandchildIds.length - 1) * dynamicSpacing

                  // 计算当前节点的中心点
                  const childCenterY = finalY + NODE_HEIGHT / 2

                  // 计算第一个孙子节点的Y坐标，使所有孙子节点对称分布在子节点两侧 - 原则2
                  const firstGrandchildY = childCenterY - totalGrandchildHeight / 2 - NODE_HEIGHT / 2

                  // 布局孙子节点，确保每个孙子节点有唯一的位置 - 原则3
                  const grandchildPositions: {id: string, y: number}[] = []

                  grandchildIds.forEach((grandchildId, idx) => {
                    // 计算初始Y坐标
                    const initialY = firstGrandchildY + idx * dynamicSpacing

                    // 检查是否与已放置的节点重叠
                    let finalGrandchildY = initialY
                    let hasOverlap = true

                    while (hasOverlap) {
                      hasOverlap = false

                      // 检查是否与已放置的孙子节点重叠
                      for (const pos of grandchildPositions) {
                        if (Math.abs(pos.y - finalGrandchildY) < NODE_HEIGHT) {
                          hasOverlap = true
                          finalGrandchildY += NODE_HEIGHT + 10
                          break
                        }
                      }

                      // 检查是否与其他节点重叠
                      if (!hasOverlap) {
                        const existingNode = nodes.value.find(node =>
                          node.id !== grandchildId &&
                          Math.abs(node.position.x - grandchildX) < NODE_WIDTH / 2 &&
                          Math.abs(node.position.y - finalGrandchildY) < NODE_HEIGHT
                        )

                        if (existingNode) {
                          hasOverlap = true
                          finalGrandchildY += NODE_HEIGHT + 10
                        }
                      }
                    }

                    // 添加到已放置节点列表
                    grandchildPositions.push({id: grandchildId, y: finalGrandchildY})

                    // 移动节点到计算好的位置
                    nodeStore.moveNode(grandchildId, { x: grandchildX, y: finalGrandchildY })
                  })
                }
              }
            })
          }
        })

        // 最后处理没有被分组的节点（可能是孤立节点）
        const processedNodeIds = new Set<string>()

        // 添加已处理的根节点
        rootNodesInDepth.forEach(nodeId => processedNodeIds.add(nodeId))

        // 添加已处理的子节点
        parentToChildrenMap.forEach(childIds => {
          childIds.forEach(childId => processedNodeIds.add(childId))
        })

        // 处理剩余未处理的节点
        nodeIds.forEach((nodeId, index) => {
          if (!processedNodeIds.has(nodeId)) {
            const y = startY + index * VERTICAL_SPACING
            nodeStore.moveNode(nodeId, { x, y })
          }
        })
      })

      // 特殊处理只有一个子节点的父节点 - 确保原则1
      if (singleParentNodes.size > 0) {
        // 对每个只有一个子节点的父节点进行特殊处理
        singleParentNodes.forEach(nodeId => {
          const nodeInfo = nodeMap.get(nodeId)
          if (!nodeInfo || nodeInfo.children.length === 0) return

          const node = getNodeById(nodeId)
          if (!node) return

          // 获取该节点的所有子节点
          const childrenIds = nodeInfo.children

          // 如果子节点数量为1，确保子节点与父节点水平对齐 - 原则1
          if (childrenIds.length === 1) {
            const childId = childrenIds[0]
            const childNode = getNodeById(childId)
            if (!childNode) return

            // 计算父节点中心点X坐标
            const parentCenterX = node.position.x + (node.size?.width || 240) / 2
            const childNodeWidth = childNode.size?.width || 240

            // 确保子节点的中心点与父节点的中心点水平对齐 - 原则1
            // 子节点X坐标 = 父节点中心点X坐标 - 子节点宽度/2 + 水平偏移量
            const horizontalOffset = FIXED_HORIZONTAL_SPACING // 使用固定的水平间距 - 原则4
            const childX = parentCenterX - childNodeWidth / 2 + horizontalOffset

            // 将子节点与父节点水平对齐 - 严格对齐 - 原则1
            nodeStore.moveNode(childId, {
              x: childX, // 使用计算后的X坐标，确保水平中心对齐
              y: node.position.y // 使用父节点的Y坐标，确保水平对齐
            })

            // 获取更新后的子节点位置
            const updatedChildNode = getNodeById(childId)
            if (!updatedChildNode) return

            // 递归处理子节点的子节点
            const childInfo = nodeMap.get(childId)
            if (childInfo && childInfo.children.length > 0) {
              // 计算子节点中心点X坐标
              const childCenterX = updatedChildNode.position.x + (updatedChildNode.size?.width || 240) / 2
              const grandchildWidth = 240 // 孙子节点默认宽度

              // 计算孙子节点应该的X坐标，使其中心与子节点中心水平对齐
              // 然后添加固定的水平偏移量，确保孙子节点在子节点右侧 - 原则4
              const grandchildX = childCenterX - grandchildWidth / 2 + FIXED_HORIZONTAL_SPACING

              // 如果子节点有多个子节点，确保它们对称分布 - 原则2
              if (childInfo.children.length > 1) {
                const grandchildrenIds = childInfo.children

                // 计算孙子节点的总高度
                const totalGrandchildHeight = (grandchildrenIds.length - 1) * VERTICAL_SPACING + NODE_HEIGHT

                // 计算子节点的中心点 - 使用更新后的位置
                const childCenterY = updatedChildNode.position.y + NODE_HEIGHT / 2

                // 计算第一个孙子节点的Y坐标 - 原则2
                const firstGrandchildY = childCenterY - totalGrandchildHeight / 2 - NODE_HEIGHT / 2

                // 布局孙子节点，确保每个孙子节点有唯一的位置 - 原则3
                const grandchildPositions: {id: string, y: number}[] = []

                grandchildrenIds.forEach((grandchildId, index) => {
                  // 计算初始Y坐标
                  const initialY = firstGrandchildY + index * VERTICAL_SPACING

                  // 检查是否与已放置的节点重叠
                  let finalY = initialY
                  let hasOverlap = true

                  while (hasOverlap) {
                    hasOverlap = false

                    // 检查是否与已放置的孙子节点重叠
                    for (const pos of grandchildPositions) {
                      if (Math.abs(pos.y - finalY) < NODE_HEIGHT) {
                        hasOverlap = true
                        finalY += NODE_HEIGHT + 10
                        break
                      }
                    }

                    // 检查是否与其他节点重叠
                    if (!hasOverlap) {
                      const existingNode = nodes.value.find(node =>
                        node.id !== grandchildId &&
                        Math.abs(node.position.x - grandchildX) < NODE_WIDTH / 2 &&
                        Math.abs(node.position.y - finalY) < NODE_HEIGHT
                      )

                      if (existingNode) {
                        hasOverlap = true
                        finalY += NODE_HEIGHT + 10
                      }
                    }
                  }

                  // 添加到已放置节点列表
                  grandchildPositions.push({id: grandchildId, y: finalY})

                  // 移动节点到计算好的位置
                  nodeStore.moveNode(grandchildId, { x: grandchildX, y: finalY })
                })
              }
              // 如果子节点只有一个子节点，确保它与子节点水平对齐 - 原则1
              else if (childInfo.children.length === 1) {
                const grandchildId = childInfo.children[0]
                const grandchildNode = getNodeById(grandchildId)
                if (!grandchildNode) return

                // 将孙子节点与子节点水平对齐 - 使用更新后的子节点位置 - 原则1
                nodeStore.moveNode(grandchildId, {
                  x: grandchildX, // 使用计算后的X坐标，确保水平中心对齐
                  y: updatedChildNode.position.y // 使用子节点的Y坐标，确保水平对齐
                })

                // 递归处理孙子节点的子节点
                const grandchildInfo = nodeMap.get(grandchildId)
                if (grandchildInfo && grandchildInfo.children.length === 1) {
                  const greatGrandchildId = grandchildInfo.children[0]
                  const greatGrandchildNode = getNodeById(greatGrandchildId)
                  if (greatGrandchildNode) {
                    // 计算孙子节点中心点X坐标
                    const updatedGrandchildNode = getNodeById(grandchildId)
                    if (updatedGrandchildNode) {
                      const grandchildCenterX = updatedGrandchildNode.position.x + (updatedGrandchildNode.size?.width || 240) / 2
                      const greatGrandchildWidth = greatGrandchildNode.size?.width || 240

                      // 计算曾孙节点应该的X坐标，使其中心与孙子节点中心水平对齐
                      // 然后添加固定的水平偏移量，确保曾孙节点在孙子节点右侧 - 原则4
                      const greatGrandchildX = grandchildCenterX - greatGrandchildWidth / 2 + FIXED_HORIZONTAL_SPACING

                      // 将曾孙节点与孙子节点水平对齐 - 原则1
                      nodeStore.moveNode(greatGrandchildId, {
                        x: greatGrandchildX, // 使用计算后的X坐标，确保水平中心对齐
                        y: updatedGrandchildNode.position.y // 使用孙子节点的Y坐标，确保水平对齐
                      })
                    }
                  }
                }
              }
            }
          }
        })
      }

      // 优化连线路径 - 调整节点位置以避免重叠
      optimizeEdgePaths()

      // 平衡子树 - 确保子树在垂直方向上居中对齐
      balanceSubtrees()

      // 应用全局协调机制
      applyGlobalCoordination()

      // 强制调整节点位置，确保符合布局原则
      forceAdjustNodePositions()
      
      // 如果启用了自动优化重叠，调用节点间距优化函数
      if (AUTO_OPTIMIZE_OVERLAPS) {
        const overlapsDetected = optimizeNodeSpacing();
        if (overlapsDetected) {
          console.log('检测到节点重叠并已修复');
        }
      }

      // 最后检查节点位置变化
      checkNodePositionChanges(initialPositions)

      // 全局布局调整 - 处理深层节点重叠问题
      const resolveGlobalOverlaps = () => {
        // 计算每个节点的深度
        const nodeDepths = new Map<string, number>()

        // 递归计算节点深度
        const calculateNodeDepth = (nodeId: string, depth: number = 0) => {
          nodeDepths.set(nodeId, depth)

          const nodeInfo = nodeMap.get(nodeId)
          if (!nodeInfo) return

          nodeInfo.children.forEach(childId => {
            calculateNodeDepth(childId, depth + 1)
          })
        }

        // 从根节点开始计算所有节点的深度
        rootNodes.forEach(rootId => {
          calculateNodeDepth(rootId)
        })

        // 获取所有第三级或更深的节点
        const deepNodes = Array.from(nodeDepths.entries())
          .filter(([_, depth]) => depth >= 2)
          .map(([nodeId, _]) => nodeId)

        // 检查每个深层节点是否与其他节点重叠
        deepNodes.forEach(nodeId => {
          const node = getNodeById(nodeId)
          if (!node) return

          const nodeInfo = nodeMap.get(nodeId)
          if (!nodeInfo || !nodeInfo.parent) return

          // 获取父节点信息
          const parentInfo = nodeMap.get(nodeInfo.parent)
          if (!parentInfo) return

          // 获取所有可能与当前节点重叠的节点
          const potentialOverlappingNodes = nodes.value.filter(otherNode =>
            otherNode.id !== nodeId && // 不是当前节点
            otherNode.id !== nodeInfo.parent && // 不是父节点
            Math.abs(otherNode.position.x - node.position.x) < NODE_WIDTH && // X方向接近
            Math.abs(otherNode.position.y - node.position.y) < NODE_HEIGHT // Y方向接近
          )

          // 如果有重叠，需要调整布局
          if (potentialOverlappingNodes.length > 0) {
            // 获取当前节点的所有兄弟节点
            const siblingIds = parentInfo.children.filter(id => id !== nodeId)
            const siblingNodes = siblingIds.map(id => getNodeById(id)).filter(Boolean)

            // 获取父节点
            const parentNode = getNodeById(nodeInfo.parent)
            if (!parentNode) return

            // 计算父节点中心点
            const parentCenterY = parentNode.position.y + NODE_HEIGHT / 2

            // 计算所有子节点（包括当前节点）
            const allChildNodes = [...siblingNodes, node]

            // 按Y坐标排序
            allChildNodes.sort((a, b) => a.position.y - b.position.y)

            // 计算子节点的总高度
            const totalChildrenHeight = (allChildNodes.length - 1) * VERTICAL_SPACING

            // 计算第一个子节点的Y坐标，使所有子节点对称分布在父节点两侧
            const firstChildY = parentCenterY - totalChildrenHeight / 2

            // 重新布局所有子节点
            allChildNodes.forEach((childNode, index) => {
              const newY = firstChildY + index * VERTICAL_SPACING

              // 移动子节点
              nodeStore.moveNode(childNode.id, {
                x: childNode.position.x,
                y: newY
              })

              // 递归处理子节点的子节点
              const childInfo = nodeMap.get(childNode.id)
              if (childInfo && childInfo.children.length > 0) {
                // 获取子节点的所有子节点
                const grandchildIds = childInfo.children
                const grandchildNodes = grandchildIds.map(id => getNodeById(id)).filter(Boolean)

                if (grandchildNodes.length > 0) {
                  // 更新后的子节点位置
                  const updatedChildNode = getNodeById(childNode.id)
                  if (!updatedChildNode) return

                  // 计算子节点中心点
                  const childCenterY = updatedChildNode.position.y + NODE_HEIGHT / 2

                  // 计算孙子节点的总高度
                  const totalGrandchildHeight = (grandchildNodes.length - 1) * VERTICAL_SPACING

                  // 计算第一个孙子节点的Y坐标
                  const firstGrandchildY = childCenterY - totalGrandchildHeight / 2

                  // 如果只有一个孙子节点，直接与子节点水平对齐
                  if (grandchildNodes.length === 1) {
                    nodeStore.moveNode(grandchildIds[0], {
                      x: grandchildNodes[0].position.x,
                      y: updatedChildNode.position.y
                    })
                  }
                  // 如果有多个孙子节点，对称分布
                  else {
                    // 按Y坐标排序
                    grandchildNodes.sort((a, b) => a.position.y - b.position.y)

                    // 重新布局所有孙子节点
                    grandchildNodes.forEach((grandchildNode, idx) => {
                      const grandchildY = firstGrandchildY + idx * VERTICAL_SPACING

                      nodeStore.moveNode(grandchildNode.id, {
                        x: grandchildNode.position.x,
                        y: grandchildY
                      })
                    })
                  }
                }
              }
            })
          }
        })

        // 最后检查所有节点，确保没有重叠
        const allNodes = nodes.value
        let hasOverlap = true
        let iterations = 0
        const MAX_ITERATIONS = 5 // 防止无限循环

        while (hasOverlap && iterations < MAX_ITERATIONS) {
          hasOverlap = false
          iterations++

          for (let i = 0; i < allNodes.length; i++) {
            for (let j = i + 1; j < allNodes.length; j++) {
              const nodeA = allNodes[i]
              const nodeB = allNodes[j]

              // 检查两个节点是否在同一列（X坐标接近）
              const sameColumn = Math.abs(nodeA.position.x - nodeB.position.x) < NODE_WIDTH / 2

              // 检查两个节点是否重叠
              const overlapping =
                Math.abs(nodeA.position.x - nodeB.position.x) < NODE_WIDTH &&
                Math.abs(nodeA.position.y - nodeB.position.y) < NODE_HEIGHT

              if (overlapping) {
                hasOverlap = true

                // 如果在同一列，垂直调整
                if (sameColumn) {
                  // 向下移动节点B
                  nodeStore.moveNode(nodeB.id, {
                    x: nodeB.position.x,
                    y: nodeA.position.y + NODE_HEIGHT + 20
                  })
                } else {
                  // 如果不在同一列，水平调整
                  nodeStore.moveNode(nodeB.id, {
                    x: nodeB.position.x + NODE_WIDTH + 20,
                    y: nodeB.position.y
                  })
                }
              }
            }
          }
        }
      }

      // 执行预处理函数 - 调整父节点和叔叔节点的位置，确保有足够的空间
      // 使用上面定义的函数实现规则3：增大父节点与叔叔节点之间的间距，并向上传导
      preAdjustParentUncleSpacing(nodeMap, rootNodes)

      // 第二步：防止父节点下移和表节点穿插
      // 确保子节点不会与表节点（叔叔节点的子节点）重叠
      preventParentShiftAndCousinsIntermixing(nodeMap, rootNodes)

      // 第三步：全局协调，处理节点位置变化的连锁反应
      // 确保所有节点满足规则1和规则2：单子节点水平对齐，多子节点对称分布
      coordinateGlobalLayout(nodeMap, rootNodes)

      // 应用对称布局 - 确保节点对称分布
      applySymmetricLayout(nodeMap, rootNodes)
      
      // 应用节点间距优化 - 防止节点重叠
      if (AUTO_OPTIMIZE_OVERLAPS) {
        // 调用优化节点间距函数，确保节点不会重叠
        const overlapsFixed = optimizeNodeSpacing();
        if (overlapsFixed) {
          console.log('自动布局：已优化节点间距，解决了节点重叠问题');
        }
      }

      // 发送自动布局事件
      emit('auto-layout')

      // 第五步：最终优化 - 确保终端节点的对称布局
      optimizeTerminalNodesLayout(nodeMap, rootNodes)

      // 执行全局布局调整，解决深层节点重叠问题
      resolveGlobalOverlaps()

      // 最后再次优化连线路径，确保所有调整后的连线合理
      optimizeEdgePaths()
    }

    // 优化连线路径，避免重叠
    const optimizeEdgePaths = () => {
      // 如果自动布局被禁用，则不进行任何优化
      if (!autoLayoutEnabled.value) return

      // 获取所有边
      const allEdges = edges.value
      const NODE_HEIGHT = 90
      const NODE_WIDTH = 240
      const SYMBOL_WIDTH = 30
      const MIN_HORIZONTAL_GAP = 40  // 减少节点与计算符号之间的最小间距，确保适当的间距
      const CONNECTOR_LENGTH = 60    // 适当增加计算符号右侧的短直线长度，使虚线起点更靠右
      const MIN_VERTICAL_GAP = 120   // 保持节点之间的最小垂直间距

      // 保存当前节点位置，用于后续恢复相对位置关系
      const originalPositions = new Map(
        nodes.value.map(node => [node.id, { ...node.position }])
      )

      // 构建节点关系树，用于优化布局
      const { nodeMap } = buildNodeTree()

      // 第一步：确保所有子节点在父节点的右侧，且有足够的水平距离
      allEdges.forEach(edge => {
        const sourceNode = getNodeById(edge.source)
        const targetNode = getNodeById(edge.target)

        if (!sourceNode || !targetNode) return

        // 计算计算符号的位置
        const symbolX = sourceNode.position.x + NODE_WIDTH + MIN_HORIZONTAL_GAP

        // 计算连接器终点位置（计算符号右侧的短直线终点）
        const connectorEndX = symbolX + SYMBOL_WIDTH + CONNECTOR_LENGTH

        // 确保目标节点在连接器终点的右侧，保证虚线始终从实线右侧开始
        // 适当减少最小距离，从30px减少到20px，使子节点更靠近父节点，形成更好的曲线效果
        const minTargetX = connectorEndX + 20

        // 如果目标节点位置不合理，调整其位置
        if (targetNode.position.x <= minTargetX) {
          // 调整目标节点位置，确保水平间距合理
          nodeStore.moveNode(targetNode.id, {
            x: minTargetX, // 使用更大的最小距离
            y: targetNode.position.y
          })
        }
      })

      // 第二步：处理单子节点的特殊情况，确保水平对齐 - 原则1
      const singleChildParents = new Set<string>()

      // 找出所有只有一个子节点的父节点
      Object.entries(nodeMap).forEach(([nodeId, nodeInfo]) => {
        if (nodeInfo.children.length === 1) {
          singleChildParents.add(nodeId)
        }
      })

      // 对每个只有一个子节点的父节点进行处理
      singleChildParents.forEach(parentId => {
        const parentNode = getNodeById(parentId)
        const nodeInfo = nodeMap.get(parentId)

        if (!parentNode || !nodeInfo) return

        const childId = nodeInfo.children[0]
        const childNode = getNodeById(childId)

        if (!childNode) return

        // 计算父节点中心点X坐标
        const parentCenterX = parentNode.position.x + (parentNode.size?.width || 240) / 2
        const childNodeWidth = childNode.size?.width || 240

        // 确保子节点的中心点与父节点的中心点水平对齐 - 原则1
        // 子节点X坐标 = 父节点中心点X坐标 - 子节点宽度/2 + 水平偏移量
        const horizontalOffset = FIXED_HORIZONTAL_SPACING // 使用固定的水平间距 - 原则4
        const childX = parentCenterX - childNodeWidth / 2 + horizontalOffset

        // 确保子节点与父节点水平对齐 - 原则1
        nodeStore.moveNode(childId, {
          x: childX, // 使用计算后的X坐标，确保水平中心对齐
          y: parentNode.position.y // 使用父节点的Y坐标，确保水平对齐
        })

        // 递归处理子节点的子节点
        const childInfo = nodeMap.get(childId)
        if (childInfo && childInfo.children.length === 1) {
          const grandchildId = childInfo.children[0]
          const grandchildNode = getNodeById(grandchildId)

          if (grandchildNode) {
            // 计算子节点中心点X坐标
            const childCenterX = childX + (childNode.size?.width || 240) / 2
            const grandchildWidth = grandchildNode.size?.width || 240

            // 计算孙子节点应该的X坐标，使其中心与子节点中心水平对齐
            // 然后添加固定的水平偏移量，确保孙子节点在子节点右侧 - 原则4
            const grandchildX = childCenterX - grandchildWidth / 2 + FIXED_HORIZONTAL_SPACING

            // 确保孙子节点与子节点水平对齐 - 原则1
            nodeStore.moveNode(grandchildId, {
              x: grandchildX, // 使用计算后的X坐标，确保水平中心对齐
              y: childNode.position.y // 使用子节点的Y坐标，确保水平对齐
            })
          }
        }
      })

      // 第三步：检查并解决节点与连线的重叠问题
      allEdges.forEach(edge => {
        const sourceNode = getNodeById(edge.source)
        const targetNode = getNodeById(edge.target)

        if (!sourceNode || !targetNode) return

        // 检查是否有节点与连线重叠
        nodes.value.forEach(node => {
          if (node.id === sourceNode.id || node.id === targetNode.id) return

          // 检查节点是否在源节点和目标节点之间
          if (node.position.x > sourceNode.position.x &&
              node.position.x < targetNode.position.x) {

            // 检查节点是否与连线垂直重叠
            // 使用更精确的连线路径计算
            const sourceY = sourceNode.position.y + NODE_HEIGHT / 2
            const targetY = targetNode.position.y + NODE_HEIGHT / 2

            // 简化的连线路径检查 - 检查节点是否与连线的大致路径重叠
            const nodeTop = node.position.y
            const nodeBottom = node.position.y + NODE_HEIGHT

            // 计算连线的大致垂直范围
            const lineMinY = Math.min(sourceY, targetY) - 20
            const lineMaxY = Math.max(sourceY, targetY) + 20

            // 检查节点是否与连线垂直范围重叠
            if ((nodeTop <= lineMaxY && nodeBottom >= lineMinY) ||
                (Math.abs(sourceY - targetY) < 10 && Math.abs((nodeTop + nodeBottom)/2 - sourceY) < NODE_HEIGHT/2)) {

              // 节点与连线重叠，向下移动节点
              nodeStore.moveNode(node.id, {
                x: node.position.x,
                y: Math.max(sourceNode.position.y, targetNode.position.y) + NODE_HEIGHT + MIN_VERTICAL_GAP
              })
            }
          }
        })
      })

      // 第四步：确保同一深度的节点水平对齐
      const depthMap = new Map<number, string[]>()

      // 计算每个节点的深度
      const calculateNodeDepth = (nodeId: string, depth: number = 0) => {
        const nodeInfo = nodeMap.get(nodeId)
        if (!nodeInfo) return

        // 记录当前深度的节点
        if (!depthMap.has(depth)) {
          depthMap.set(depth, [])
        }
        depthMap.get(depth)?.push(nodeId)

        // 递归计算子节点深度
        nodeInfo.children.forEach(childId => {
          calculateNodeDepth(childId, depth + 1)
        })
      }

      // 从根节点开始计算深度
      const rootNodes = Array.from(nodeMap.values())
        .filter(info => info.parent === null)
        .map(info => info.node.id)

      rootNodes.forEach(rootId => {
        calculateNodeDepth(rootId)
      })

      // 对每个深度层的节点进行水平对齐
      depthMap.forEach((nodeIds, depth) => {
        if (nodeIds.length <= 1) return

        // 计算该深度层的平均X坐标
        let sumX = 0
        let validNodeCount = 0

        nodeIds.forEach(nodeId => {
          const node = getNodeById(nodeId)
          if (node) {
            sumX += node.position.x
            validNodeCount++
          }
        })

        if (validNodeCount > 0) {
          const avgX = sumX / validNodeCount

          // 将该深度层的所有节点水平对齐
          nodeIds.forEach(nodeId => {
            const node = getNodeById(nodeId)
            if (node) {
              nodeStore.moveNode(nodeId, {
                x: avgX,
                y: node.position.y
              })
            }
          })
        }
      })

      // 第五步：最后检查所有节点，确保没有重叠
      const allNodes = nodes.value
      for (let i = 0; i < allNodes.length; i++) {
        for (let j = i + 1; j < allNodes.length; j++) {
          const nodeA = allNodes[i]
          const nodeB = allNodes[j]

          // 检查水平重叠
          const horizontalOverlap =
            nodeA.position.x < nodeB.position.x + NODE_WIDTH &&
            nodeA.position.x + NODE_WIDTH > nodeB.position.x

          // 检查垂直重叠
          const verticalOverlap =
            nodeA.position.y < nodeB.position.y + NODE_HEIGHT &&
            nodeA.position.y + NODE_HEIGHT > nodeB.position.y

          // 如果两个节点重叠
          if (horizontalOverlap && verticalOverlap) {
            // 移动节点B，避免重叠
            nodeStore.moveNode(nodeB.id, {
              x: nodeB.position.x,
              y: nodeA.position.y + NODE_HEIGHT + MIN_VERTICAL_GAP
            })
          }
        }
      }

      // 第六步：再次检查所有边，确保连线路径合理
      allEdges.forEach(edge => {
        const sourceNode = getNodeById(edge.source)
        const targetNode = getNodeById(edge.target)

        if (!sourceNode || !targetNode) return

        // 如果目标节点在源节点左侧，强制将其移到右侧
        if (targetNode.position.x <= sourceNode.position.x + NODE_WIDTH) {
          const symbolX = sourceNode.position.x + NODE_WIDTH + MIN_HORIZONTAL_GAP
          const connectorEndX = symbolX + SYMBOL_WIDTH + CONNECTOR_LENGTH
          const minTargetX = connectorEndX + 70  // 减少最小距离，从100px减少到70px，使子节点更靠近父节点

          nodeStore.moveNode(targetNode.id, {
            x: minTargetX,
            y: targetNode.position.y
          })
        }

        // 即使目标节点在源节点右侧，也要确保有足够的距离
        else if (targetNode.position.x < sourceNode.position.x + NODE_WIDTH + MIN_HORIZONTAL_GAP + SYMBOL_WIDTH + CONNECTOR_LENGTH + 30) {
          // 计算合适的最小X坐标
          const symbolX = sourceNode.position.x + NODE_WIDTH + MIN_HORIZONTAL_GAP
          const connectorEndX = symbolX + SYMBOL_WIDTH + CONNECTOR_LENGTH
          const minTargetX = connectorEndX + 30  // 大幅减少最小距离，从50px减少到30px，使子节点更靠近父节点，形成更好的曲线效果

          // 调整目标节点位置
          nodeStore.moveNode(targetNode.id, {
            x: minTargetX,
            y: targetNode.position.y
          })
        }
      })

      // 第七步：检测并解决第三级节点重叠问题
      // 获取所有节点的深度信息
      const nodeDepths = new Map<string, number>()

      // 从根节点开始计算深度
      const calculateDepths = (rootId: string, depth = 0) => {
        nodeDepths.set(rootId, depth)

        const nodeInfo = nodeMap.get(rootId)
        if (!nodeInfo) return

        nodeInfo.children.forEach(childId => {
          calculateDepths(childId, depth + 1)
        })
      }

      // 计算所有节点的深度
      rootNodes.forEach(rootId => {
        calculateDepths(rootId)
      })

      // 获取所有第三级或更深的节点
      const deepNodes = Array.from(nodeDepths.entries())
        .filter(([_, depth]) => depth >= 2)
        .map(([nodeId, _]) => nodeId)

      // 检查每个深层节点是否与其"叔叔"节点的子节点（表兄弟）重叠
      deepNodes.forEach(nodeId => {
        const node = getNodeById(nodeId)
        if (!node) return

        const nodeInfo = nodeMap.get(nodeId)
        if (!nodeInfo || !nodeInfo.parent) return

        // 获取父节点信息
        const parentInfo = nodeMap.get(nodeInfo.parent)
        if (!parentInfo || !parentInfo.parent) return

        // 获取祖父节点信息
        const grandparentInfo = nodeMap.get(parentInfo.parent)
        if (!grandparentInfo) return

        // 获取父节点的所有兄弟节点（叔叔节点）
        const uncleNodeIds = grandparentInfo.children.filter(id => id !== nodeInfo.parent)

        // 获取所有表兄弟节点（叔叔的子节点）
        const cousinNodes: any[] = []

        uncleNodeIds.forEach(uncleId => {
          const uncleInfo = nodeMap.get(uncleId)
          if (uncleInfo) {
            uncleInfo.children.forEach(cousinId => {
              const cousinNode = getNodeById(cousinId)
              if (cousinNode) cousinNodes.push(cousinNode)
            })
          }
        })

        // 检查是否与任何表兄弟节点重叠
        const overlappingNodes = cousinNodes.filter(cousinNode =>
          Math.abs(cousinNode.position.x - node.position.x) < NODE_WIDTH &&
          Math.abs(cousinNode.position.y - node.position.y) < NODE_HEIGHT
        )

        // 如果有重叠，需要全局调整
        if (overlappingNodes.length > 0) {
          // 首先尝试调整当前节点及其兄弟节点的位置
          adjustNodeAndSiblings(nodeId, nodeInfo, parentInfo)

          // 如果调整后仍有重叠，则需要调整父节点及其兄弟节点的位置
          const stillOverlapping = cousinNodes.some(cousinNode =>
            Math.abs(cousinNode.position.x - node.position.x) < NODE_WIDTH &&
            Math.abs(cousinNode.position.y - node.position.y) < NODE_HEIGHT
          )

          if (stillOverlapping) {
            // 调整父节点及其兄弟节点（叔叔节点）的位置
            adjustParentAndUncles(nodeInfo.parent, parentInfo, grandparentInfo)

            // 重新调整当前节点及其兄弟节点
            adjustNodeAndSiblings(nodeId, nodeInfo, parentInfo)
          }
        }
      })

      // 调整节点及其兄弟节点的位置
      function adjustNodeAndSiblings(nodeId: string, nodeInfo: any, parentInfo: any) {
        // 获取当前节点
        const node = getNodeById(nodeId)
        if (!node) return

        // 获取当前节点的所有兄弟节点
        const siblingIds = parentInfo.children.filter(id => id !== nodeId)
        const siblingNodes = siblingIds.map(id => getNodeById(id)).filter(Boolean)

        // 获取父节点
        const parentNode = getNodeById(nodeInfo.parent)
        if (!parentNode) return

        // 计算父节点中心点
        const parentCenterY = parentNode.position.y + NODE_HEIGHT / 2

        // 计算所有子节点（包括当前节点）
        const allChildNodes = [...siblingNodes, node]

        // 按Y坐标排序
        allChildNodes.sort((a, b) => a.position.y - b.position.y)

        // 计算子节点的总高度
        const totalChildrenHeight = (allChildNodes.length - 1) * VERTICAL_SPACING

        // 计算第一个子节点的Y坐标，使所有子节点对称分布在父节点两侧
        const firstChildY = parentCenterY - totalChildrenHeight / 2

        // 重新布局所有子节点
        allChildNodes.forEach((childNode, index) => {
          const newY = firstChildY + index * VERTICAL_SPACING

          // 移动子节点
          nodeStore.moveNode(childNode.id, {
            x: childNode.position.x,
            y: newY
          })
        })
      }

      // 调整父节点及其兄弟节点的位置
      function adjustParentAndUncles(parentId: string, parentInfo: any, grandparentInfo: any) {
        // 获取父节点
        const parentNode = getNodeById(parentId)
        if (!parentNode) return

        // 获取父节点的所有兄弟节点（叔叔节点）
        const uncleIds = grandparentInfo.children.filter(id => id !== parentId)
        const uncleNodes = uncleIds.map(id => getNodeById(id)).filter(Boolean)

        // 获取祖父节点
        const grandparentNode = getNodeById(grandparentInfo.node.id)
        if (!grandparentNode) return

        // 计算祖父节点中心点
        const grandparentCenterY = grandparentNode.position.y + NODE_HEIGHT / 2

        // 计算所有子节点（父节点及其兄弟节点）
        const allParentLevelNodes = [parentNode, ...uncleNodes]

        // 按Y坐标排序
        allParentLevelNodes.sort((a, b) => a.position.y - b.position.y)

        // 计算父级节点之间需要的垂直间距
        // 为每个父节点计算其子节点占用的垂直空间
        const parentSpacings: number[] = []

        allParentLevelNodes.forEach(pNode => {
          const pInfo = nodeMap.get(pNode.id)
          if (!pInfo) {
            parentSpacings.push(VERTICAL_SPACING)
            return
          }

          // 获取该父节点的所有子节点
          const childIds = pInfo.children
          const childCount = childIds.length

          if (childCount <= 1) {
            // 如果没有子节点或只有一个子节点，使用标准间距
            parentSpacings.push(VERTICAL_SPACING)
          } else {
            // 如果有多个子节点，计算它们占用的垂直空间
            const childSpace = (childCount - 1) * VERTICAL_SPACING + NODE_HEIGHT
            // 确保父节点间距至少是标准间距
            parentSpacings.push(Math.max(childSpace + NODE_HEIGHT, VERTICAL_SPACING * 1.5))
          }
        })

        // 计算父级节点的总高度（考虑每个父节点的子节点空间）
        let totalParentHeight = 0
        for (let i = 0; i < allParentLevelNodes.length - 1; i++) {
          totalParentHeight += parentSpacings[i]
        }

        // 计算第一个父级节点的Y坐标
        const firstParentY = grandparentCenterY - totalParentHeight / 2

        // 重新布局所有父级节点
        let currentY = firstParentY
        allParentLevelNodes.forEach((pNode, index) => {
          // 移动父节点
          nodeStore.moveNode(pNode.id, {
            x: pNode.position.x,
            y: currentY
          })

          // 更新下一个父节点的Y坐标
          if (index < allParentLevelNodes.length - 1) {
            currentY += parentSpacings[index]
          }

          // 获取父节点信息
          const pInfo = nodeMap.get(pNode.id)
          if (!pInfo) return

          // 调整该父节点的所有子节点
          const childIds = pInfo.children
          if (childIds.length > 0) {
            // 重新获取更新后的父节点位置
            const updatedParentNode = getNodeById(pNode.id)
            if (!updatedParentNode) return

            // 计算父节点中心点
            const updatedParentCenterY = updatedParentNode.position.y + NODE_HEIGHT / 2

            // 获取所有子节点
            const childNodes = childIds.map(id => getNodeById(id)).filter(Boolean)

            // 按Y坐标排序
            childNodes.sort((a, b) => a.position.y - b.position.y)

            // 如果只有一个子节点，直接与父节点水平对齐
            if (childNodes.length === 1) {
              nodeStore.moveNode(childNodes[0].id, {
                x: childNodes[0].position.x,
                y: updatedParentNode.position.y
              })
            } else if (childNodes.length > 1) {
              // 计算子节点的总高度
              const totalChildHeight = (childNodes.length - 1) * VERTICAL_SPACING

              // 计算第一个子节点的Y坐标
              const firstChildY = updatedParentCenterY - totalChildHeight / 2

              // 重新布局所有子节点
              childNodes.forEach((childNode, idx) => {
                const newY = firstChildY + idx * VERTICAL_SPACING

                // 移动子节点
                nodeStore.moveNode(childNode.id, {
                  x: childNode.position.x,
                  y: newY
                })
              })
            }
          }
        })
      }
    }

    // 专门的对称布局函数
    const applySymmetricLayout = (nodeMap: Map<string, any>, rootNodes: string[]) => {
      console.log('应用对称布局');
      
      // 按深度处理节点，从根节点开始
      const processNodesByDepth = (depth = 0, processedNodes = new Set<string>()) => {
        // 获取当前深度的所有节点
        const nodesAtDepth: string[] = [];
        
        // 递归函数，收集指定深度的节点
        const collectNodesAtDepth = (nodeId: string, currentDepth = 0) => {
          const nodeInfo = nodeMap.get(nodeId);
          if (!nodeInfo) return;
          
          if (currentDepth === depth) {
            nodesAtDepth.push(nodeId);
            return;
          }
          
          if (currentDepth < depth) {
            nodeInfo.children.forEach((childId: string) => {
              collectNodesAtDepth(childId, currentDepth + 1);
            });
          }
        };
        
        // 从所有根节点开始收集
        rootNodes.forEach(rootId => {
          collectNodesAtDepth(rootId);
        });
        
        // 按父节点分组处理节点
        const nodesByParent = new Map<string, string[]>();
        
        nodesAtDepth.forEach(nodeId => {
          if (processedNodes.has(nodeId)) return;
          
          const nodeInfo = nodeMap.get(nodeId);
          if (!nodeInfo) return;
          
          const parentId = nodeInfo.parent;
          if (!parentId) return;
          
          if (!nodesByParent.has(parentId)) {
            nodesByParent.set(parentId, []);
          }
          
          nodesByParent.get(parentId)!.push(nodeId);
        });
        
        // 对每组子节点应用对称布局
        nodesByParent.forEach((childIds, parentId) => {
          applySymmetryToChildren(parentId, childIds, nodeMap);
          
          // 标记这些节点为已处理
          childIds.forEach(id => processedNodes.add(id));
        });
        
        // 处理下一个深度
        if (nodesAtDepth.length > 0) {
          processNodesByDepth(depth + 1, processedNodes);
        }
      };
      
      // 从深度0开始处理
      processNodesByDepth();
    };

    // 对一组子节点应用对称布局
    const applySymmetryToChildren = (parentId: string, childIds: string[], nodeMap: Map<string, any>) => {
      if (childIds.length <= 1) return;
      
      const parentNode = getNodeById(parentId);
      if (!parentNode) return;
      
      // 获取所有子节点
      const childNodes = childIds.map(id => getNodeById(id)).filter(Boolean);
      if (childNodes.length <= 1) return;
      
      // 计算父节点中心点
      const parentCenterY = parentNode.position.y + NODE_HEIGHT / 2;
      
      // 确定使用哪种垂直间距
      const nodeInfo = nodeMap.get(parentId);
      const allTerminalNodes = childIds.every(childId => {
        const childInfo = nodeMap.get(childId);
        return childInfo && childInfo.isTerminalNode;
      });
      
      // 使用适当的垂直间距 - 对终端节点使用TERMINAL_NODE_SPACING
      const spacing = allTerminalNodes ? TERMINAL_NODE_SPACING : VERTICAL_SPACING;
      
      // 计算子节点的总高度
      const totalChildHeight = (childNodes.length - 1) * spacing + NODE_HEIGHT;
      
      // 计算第一个子节点的Y坐标，使所有子节点对称分布在父节点两侧
      const firstChildY = parentCenterY - totalChildHeight / 2;
      
      // 计算父节点中心点X坐标
      const parentCenterX = parentNode.position.x + (parentNode.size?.width || NODE_WIDTH) / 2;
      
      // 为每个子节点计算新的位置，确保严格对称
      childNodes.forEach((childNode, index) => {
        // 计算子节点的X坐标，确保水平对齐
        const childNodeWidth = childNode.size?.width || NODE_WIDTH;
        const childX = parentCenterX - childNodeWidth / 2 + FIXED_HORIZONTAL_SPACING;
        
        // 计算子节点的Y坐标，确保垂直对称分布
        const childY = firstChildY + index * spacing;
        
        // 移动子节点到新位置
        nodeStore.moveNode(childNode.id, {
          x: childX,
          y: childY
        });
        
        console.log(`对称布局: 移动节点 ${childNode.id} 到 (${childX}, ${childY})`);
      });
    };

    // 终端节点优化函数 - 实现规则2：终端节点保持左右对齐，上下间距相等，与父节点形成对称效果
    const optimizeTerminalNodesLayout = (nodeMap: Map<string, any>, rootNodes: string[]) => {
      // 找出所有有终端子节点的父节点
      const parentsWithTerminalChildren = new Set<string>();

      nodeMap.forEach((info, nodeId) => {
        if (info.children.length > 1) {
          // 检查是否所有子节点都是终端节点
          const allTerminalNodes = info.children.every(childId => {
            const childInfo = nodeMap.get(childId);
            return childInfo && childInfo.isTerminalNode;
          });

          if (allTerminalNodes) {
            parentsWithTerminalChildren.add(nodeId);
          }
        }
      });

      // 处理每个有终端子节点的父节点
      parentsWithTerminalChildren.forEach(parentId => {
        // 使用applySymmetryToChildren函数确保终端节点对称布局
        const parentInfo = nodeMap.get(parentId);
        if (!parentInfo) return;
        
        applySymmetryToChildren(parentId, parentInfo.children, nodeMap);
      });
    };

    // 平衡子树 - 确保子树在垂直方向上居中对齐
    const balanceSubtrees = () => {
      // 如果自动布局被禁用，则不进行任何平衡操作
      if (!autoLayoutEnabled.value) return

      // 保存当前节点位置，用于后续恢复相对位置关系
      const originalPositions = new Map(
        nodes.value.map(node => [node.id, { ...node.position }])
      )
      
      // 构建节点关系树
      const { nodeMap, rootNodes } = buildNodeTree()
      
      // 计算子树信息，包括高度和中心点
      const calculateSubtreeInfo = (nodeId: string) => {
        const nodeInfo = nodeMap.get(nodeId)
        if (!nodeInfo || nodeInfo.children.length === 0) {
          // 叶子节点
          const node = getNodeById(nodeId)
          if (!node) return { height: 0, center: 0, nodeCount: 0 }

          return {
            height: NODE_HEIGHT,
            center: node.position.y + NODE_HEIGHT / 2,
            nodeCount: 1
          }
        }

        // 计算所有子节点的高度和中心点
        const childrenInfo = nodeInfo.children.map(childId => {
          return {
            id: childId,
            ...calculateSubtreeInfo(childId)
          }
        })

        // 计算子树的总高度和中心点
        if (childrenInfo.length === 1) {
          // 如果只有一个子节点，直接返回其高度和中心点
          return {
            ...childrenInfo[0],
            nodeCount: childrenInfo[0].nodeCount
          }
        } else {
          // 如果有多个子节点，计算总高度和中心点
          // 总高度 = 所有子节点高度 + 间距 * (子节点数-1)
          const totalNodeCount = childrenInfo.reduce((sum, info) => sum + info.nodeCount, 0)
          const totalHeight = childrenInfo.reduce((sum, info) => sum + info.height, 0) +
                             VERTICAL_GAP * (childrenInfo.length - 1)

          // 计算中心点 - 使用第一个和最后一个子节点的中心点计算
          const firstCenter = childrenInfo[0].center
          const lastCenter = childrenInfo[childrenInfo.length - 1].center
          const center = (firstCenter + lastCenter) / 2

          return { height: totalHeight, center, nodeCount: totalNodeCount }
        }
      }

      // 递归平衡子树
      const balanceSubtree = (nodeId: string) => {
        const nodeInfo = nodeMap.get(nodeId)
        if (!nodeInfo || nodeInfo.children.length === 0) return

        const node = getNodeById(nodeId)
        if (!node) return

        // 如果只有一个子节点，确保与父节点在同一水平线上 - 原则1
        if (nodeInfo.children.length === 1) {
          const childId = nodeInfo.children[0]
          const childNode = getNodeById(childId)
          if (!childNode) return

          // 确保子节点在父节点右侧且有足够距离 - 原则4
          const symbolX = node.position.x + NODE_WIDTH + MIN_HORIZONTAL_GAP
          const connectorEndX = symbolX + SYMBOL_WIDTH + CONNECTOR_LENGTH
          // 适当减少最小距离，从30px减少到20px，使子节点更靠近父节点，形成更好的曲线效果
          const minChildX = connectorEndX + 20

          // 计算父节点中心点X坐标
          const parentCenterX = node.position.x + (node.size?.width || 240) / 2
          const childNodeWidth = childNode.size?.width || 240

          // 确保子节点的中心点与父节点的中心点水平对齐 - 原则1
          // 子节点X坐标 = 父节点中心点X坐标 - 子节点宽度/2 + 水平偏移量
          const horizontalOffset = FIXED_HORIZONTAL_SPACING // 使用固定的水平间距 - 原则4
          const childX = parentCenterX - childNodeWidth / 2 + horizontalOffset

          // 水平对齐 - 使用计算后的X坐标，确保水平中心对齐 - 严格对齐 - 原则1
          nodeStore.moveNode(childId, {
            x: childX,
            y: node.position.y // 使用父节点的Y坐标，确保水平对齐
          })

          // 修改：确保爷爷节点与其所有子节点保持对称
          // 检查是否有爷爷节点
          if (parentInfo.parent) {
            const grandparentId = parentInfo.parent;
            const grandparentInfo = nodeMap.get(grandparentId);
            const grandparentNode = getNodeById(grandparentId);
            
            if (grandparentInfo && grandparentNode) {
              // 获取所有叔叔节点（父节点的同级节点）
              const uncleIds = grandparentInfo.children.filter(id => id !== nodeId);
              
              if (uncleIds.length > 0) {
                // 获取所有叔叔节点
                const uncleNodes = uncleIds.map(id => getNodeById(id)).filter(Boolean);
                
                if (uncleNodes.length > 0) {
                  // 计算所有子节点（父节点及其叔叔节点）的中心位置
                  let allChildrenMinY = Math.min(node.position.y, ...uncleNodes.map(n => n.position.y));
                  let allChildrenMaxY = Math.max(node.position.y, ...uncleNodes.map(n => n.position.y));
                  
                  const allChildrenCenterY = (allChildrenMinY + allChildrenMaxY) / 2 + NODE_HEIGHT / 2;
                  
                  // 计算爷爷节点需要移动的新Y坐标
                  const newGrandparentY = allChildrenCenterY - NODE_HEIGHT / 2;
                  
                  // 如果偏移显著，移动爷爷节点以保持对称
                  if (Math.abs(grandparentNode.position.y - newGrandparentY) > 5) {
                    nodeStore.moveNode(grandparentId, {
                      x: grandparentNode.position.x,
                      y: newGrandparentY
                    });
                  }
                }
              }
            }
          }

          // 获取子节点信息
          const childInfo = nodeMap.get(childId)

          // 如果子节点有自己的子节点，需要特殊处理
          if (childInfo && childInfo.children.length > 0) {
            // 如果子节点有多个子节点，确保它们对称分布 - 原则2
            if (childInfo.children.length > 1) {
              // 计算孙子节点的总高度
              const grandchildrenIds = childInfo.children

              // 使用更大的垂直间距，确保孙子节点不会重叠
              const grandchildSpacing = Math.max(VERTICAL_GAP, NODE_HEIGHT + MIN_VERTICAL_DISTANCE)
              const totalGrandchildHeight = (grandchildrenIds.length - 1) * grandchildSpacing + NODE_HEIGHT

              // 计算子节点的中心点 - 使用更新后的位置
              const childCenterY = childNode.position.y + NODE_HEIGHT / 2

              // 计算子节点中心点X坐标
              const childCenterX = childNode.position.x + (childNode.size?.width || 240) / 2
              const grandchildWidth = 240 // 孙子节点默认宽度

              // 计算孙子节点应该的X坐标，使其中心与子节点中心水平对齐
              // 然后添加固定的水平偏移量，确保孙子节点在子节点右侧 - 原则4
              const horizontalOffset = FIXED_HORIZONTAL_SPACING
              const grandchildX = childCenterX - grandchildWidth / 2 + horizontalOffset

              // 计算第一个孙子节点的Y坐标 - 原则2
              const firstGrandchildY = childCenterY - totalGrandchildHeight / 2 - NODE_HEIGHT / 2

              // 布局孙子节点，确保每个孙子节点有唯一的位置 - 原则3
              const grandchildPositions: {id: string, y: number}[] = []

              grandchildrenIds.forEach((grandchildId, index) => {
                // 计算初始Y坐标
                const initialY = firstGrandchildY + index * grandchildSpacing
                const grandchildNode = getNodeById(grandchildId)
                if (!grandchildNode) return

                // 检查是否与已放置的节点重叠
                let finalY = initialY
                let hasOverlap = true

                while (hasOverlap) {
                  hasOverlap = false

                  // 检查是否与已放置的孙子节点重叠
                  for (const pos of grandchildPositions) {
                    if (Math.abs(pos.y - finalY) < NODE_HEIGHT) {
                      hasOverlap = true
                      finalY += NODE_HEIGHT + 10
                      break
                    }
                  }

                  // 检查是否与其他节点重叠
                  if (!hasOverlap) {
                    const existingNode = nodes.value.find(node =>
                      node.id !== grandchildId &&
                      Math.abs(node.position.x - grandchildX) < NODE_WIDTH / 2 &&
                      Math.abs(node.position.y - finalY) < NODE_HEIGHT
                    )

                    if (existingNode) {
                      hasOverlap = true
                      finalY += NODE_HEIGHT + 10
                    }
                  }
                }

                // 添加到已放置节点列表
                grandchildPositions.push({id: grandchildId, y: finalY})

                // 移动节点到计算好的位置
                nodeStore.moveNode(grandchildId, {
                  x: grandchildX, // 使用计算后的X坐标，确保水平中心对齐
                  y: finalY
                })
              })
            }
            // 如果子节点只有一个子节点，确保它与子节点水平对齐 - 原则1
            else if (childInfo.children.length === 1) {
              const grandchildId = childInfo.children[0]
              const grandchildNode = getNodeById(grandchildId)
              if (!grandchildNode) return

              // 确保孙子节点在子节点右侧且有足够距离 - 原则4
              const updatedChildNode = getNodeById(childId)
              if (!updatedChildNode) return

              // 计算子节点中心点X坐标
              const childCenterX = updatedChildNode.position.x + (updatedChildNode.size?.width || 240) / 2
              const grandchildWidth = grandchildNode.size?.width || 240

              // 计算孙子节点应该的X坐标，使其中心与子节点中心水平对齐
              // 然后添加固定的水平偏移量，确保孙子节点在子节点右侧 - 原则4
              const horizontalOffset = FIXED_HORIZONTAL_SPACING
              const grandchildX = childCenterX - grandchildWidth / 2 + horizontalOffset

              // 将孙子节点与子节点水平对齐，并确保X坐标合理 - 原则1
              nodeStore.moveNode(grandchildId, {
                x: grandchildX, // 使用计算后的X坐标，确保水平中心对齐
                y: updatedChildNode.position.y // 使用子节点的Y坐标，确保水平对齐
              })

              // 递归处理孙子节点
              const grandchildInfo = nodeMap.get(grandchildId)
              if (grandchildInfo && grandchildInfo.children.length === 1) {
                const greatGrandchildId = grandchildInfo.children[0]
                const greatGrandchildNode = getNodeById(greatGrandchildId)
                if (greatGrandchildNode) {
                  // 计算孙子节点中心点X坐标
                  const grandchildCenterX = grandchildNode.position.x + (grandchildNode.size?.width || 240) / 2
                  const greatGrandchildWidth = greatGrandchildNode.size?.width || 240

                  // 计算曾孙节点应该的X坐标，使其中心与孙子节点中心水平对齐
                  // 然后添加固定的水平偏移量，确保曾孙节点在孙子节点右侧 - 原则4
                  const horizontalOffset = FIXED_HORIZONTAL_SPACING
                  const greatGrandchildX = grandchildCenterX - greatGrandchildWidth / 2 + horizontalOffset

                  // 将曾孙节点与孙子节点水平对齐，并确保X坐标合理 - 原则1
                  nodeStore.moveNode(greatGrandchildId, {
                    x: greatGrandchildX, // 使用计算后的X坐标，确保水平中心对齐
                    y: grandchildNode.position.y // 使用孙子节点的Y坐标，确保水平对齐
                  })
                }
              }
            }
          }

          // 递归平衡子树
          balanceSubtree(childId)
          return
        }

        // 对子节点按照在父节点中的索引顺序排序
        const sortedChildren = [...nodeInfo.children].sort((a, b) => {
          const indexA = nodeInfo.children.indexOf(a)
          const indexB = nodeInfo.children.indexOf(b)
          return indexA - indexB
        })

        // 计算子树信息
        const childrenInfo = sortedChildren.map(childId => {
          return {
            id: childId,
            ...calculateSubtreeInfo(childId)
          }
        })

        // 计算父节点的中心点
        const parentCenter = node.position.y + NODE_HEIGHT / 2

        // 计算子树的总高度（包括间距）
        const totalHeight = childrenInfo.reduce((sum, info, index) => {
          // 添加当前子树高度
          let height = info.height
          // 如果不是最后一个子树，添加间距
          if (index < childrenInfo.length - 1) {
            height += VERTICAL_GAP
          }
          return sum + height
        }, 0)

        // 计算起始Y坐标，使子树垂直居中于父节点
        let currentY = parentCenter - totalHeight / 2

        // 调整子节点位置，确保对称布局
        childrenInfo.forEach(info => {
          const childNode = getNodeById(info.id)
          if (!childNode) return

          // 计算子节点的新Y坐标，考虑子树的中心点
          const newY = currentY + (info.height / 2) - (NODE_HEIGHT / 2)

          // 检查是否有其他节点在相同位置或非常接近
          const existingNode = nodes.value.find(node =>
            node.id !== info.id &&
            Math.abs(node.position.x - childNode.position.x) < 10 &&
            Math.abs(node.position.y - newY) < NODE_HEIGHT
          )

          // 如果有节点在相同位置，稍微调整Y坐标
          const finalY = existingNode ? newY + NODE_HEIGHT + MIN_VERTICAL_DISTANCE : newY

          // 调整子节点位置
          nodeStore.moveNode(info.id, {
            x: childNode.position.x,
            y: finalY
          })

          // 递归平衡子树
          balanceSubtree(info.id)

          // 更新下一个子节点的Y坐标，确保足够的间距
          // 使用子节点的实际位置计算下一个节点的位置，避免累积误差
          const actualChildNode = getNodeById(info.id)
          if (actualChildNode) {
            currentY = actualChildNode.position.y + NODE_HEIGHT + VERTICAL_GAP
          } else {
            currentY += info.height + VERTICAL_GAP
          }
        })
      }

      // 从根节点开始平衡子树
      rootNodes.forEach(rootId => {
        balanceSubtree(rootId)
      })

      // 最后再次检查所有边，确保连线路径合理
      const allEdges = edges.value
      allEdges.forEach(edge => {
        const sourceNode = getNodeById(edge.source)
        const targetNode = getNodeById(edge.target)

        if (!sourceNode || !targetNode) return

        // 如果目标节点在源节点左侧，强制将其移到右侧
        if (targetNode.position.x <= sourceNode.position.x + NODE_WIDTH) {
          const symbolX = sourceNode.position.x + NODE_WIDTH + MIN_HORIZONTAL_GAP
          const connectorEndX = symbolX + SYMBOL_WIDTH + CONNECTOR_LENGTH
          const minTargetX = connectorEndX + 20  // 适当减少最小距离，从30px减少到20px，使子节点更靠近父节点，形成更好的曲线效果

          nodeStore.moveNode(targetNode.id, {
            x: minTargetX,
            y: targetNode.position.y
          })
        }

        // 即使目标节点在源节点右侧，也要确保有足够的距离
        else if (targetNode.position.x < sourceNode.position.x + NODE_WIDTH + MIN_HORIZONTAL_GAP + SYMBOL_WIDTH + CONNECTOR_LENGTH + 20) {
          // 计算合适的最小X坐标
          const symbolX = sourceNode.position.x + NODE_WIDTH + MIN_HORIZONTAL_GAP
          const connectorEndX = symbolX + SYMBOL_WIDTH + CONNECTOR_LENGTH
          const minTargetX = connectorEndX + 20  // 适当减少最小距离，从30px减少到20px，使子节点更靠近父节点，形成更好的曲线效果

          // 调整目标节点位置
          nodeStore.moveNode(targetNode.id, {
            x: minTargetX,
            y: targetNode.position.y
          })
        }
      })

      // 对于同一深度的节点，确保它们的水平位置一致
      const depthMap = new Map<number, string[]>()

      // 计算每个节点的深度
      const calculateNodeLevels = (nodeId: string, depth: number = 0) => {
        const nodeInfo = nodeMap.get(nodeId)
        if (!nodeInfo) return

        // 记录当前深度的节点
        if (!depthMap.has(depth)) {
          depthMap.set(depth, [])
        }
        depthMap.get(depth)?.push(nodeId)

        // 递归计算子节点深度
        nodeInfo.children.forEach(childId => {
          calculateNodeLevels(childId, depth + 1)
        })
      }

      // 从根节点开始计算深度
      rootNodes.forEach(rootId => {
        calculateNodeLevels(rootId)
      })

      // 对每个深度层的节点进行水平对齐
      depthMap.forEach((nodeIds, depth) => {
        if (nodeIds.length <= 1) return

        // 计算该深度层的目标X坐标
        // 使用固定间距，确保布局一致性
        // 为所有层级使用相同的水平间距，确保连线不会扰曲
        const targetX = ROOT_X + depth * FIXED_HORIZONTAL_SPACING

        // 将该深度层的所有节点水平对齐
        nodeIds.forEach(nodeId => {
          const node = getNodeById(nodeId)
          if (node) {
            // 检查是否有其他节点在相同的Y坐标附近
            const nodesAtSimilarY = nodes.value.filter(otherNode =>
              otherNode.id !== nodeId &&
              Math.abs(otherNode.position.y - node.position.y) < NODE_HEIGHT / 2 &&
              Math.abs(otherNode.position.x - targetX) < NODE_WIDTH / 2
            )

            // 如果有节点在相似位置，稍微调整Y坐标避免重叠
            let adjustedY = node.position.y
            if (nodesAtSimilarY.length > 0) {
              adjustedY = node.position.y + NODE_HEIGHT / 2
            }

            // 移动节点到目标X坐标
            nodeStore.moveNode(nodeId, {
              x: targetX,
              y: adjustedY
            })
          }
        })
      })

      // 最终检查 - 确保没有节点重叠
      const allNodes = nodes.value

      // 多次迭代检查，确保所有重叠都被解决
      for (let iteration = 0; iteration < 2; iteration++) {
        // 按Y坐标排序节点，确保从上到下处理
        const sortedNodes = [...allNodes].sort((a, b) => a.position.y - b.position.y)

        for (let i = 0; i < sortedNodes.length; i++) {
          for (let j = i + 1; j < sortedNodes.length; j++) {
            const nodeA = sortedNodes[i]
            const nodeB = sortedNodes[j]

            // 检查节点是否在同一列（X坐标接近）
            const sameColumn = Math.abs(nodeA.position.x - nodeB.position.x) < NODE_WIDTH / 2

            if (sameColumn) {
              // 检查垂直距离
              const verticalDistance = Math.abs(nodeA.position.y - nodeB.position.y)
              const minVerticalDistance = NODE_HEIGHT + VERTICAL_GAP  // 使用完整的VERTICAL_GAP

              // 如果距离太近，调整位置
              if (verticalDistance < minVerticalDistance) {
                // 向下移动节点B，确保足够的间距
                nodeStore.moveNode(nodeB.id, {
                  x: nodeB.position.x,
                  y: nodeA.position.y + minVerticalDistance
                })

                // 检查是否有连接到这个节点的边
                const connectedEdges = edges.value.filter(
                  edge => edge.source === nodeB.id || edge.target === nodeB.id
                )

                // 如果有连接的边，可能需要再次优化连线路径
                if (connectedEdges.length > 0) {
                  // 标记需要在下一次迭代中重新检查
                  continue
                }
              }
            }

            // 检查节点是否在不同列但仍然有重叠
            const horizontalOverlap =
              nodeA.position.x < nodeB.position.x + NODE_WIDTH &&
              nodeA.position.x + NODE_WIDTH > nodeB.position.x

            const verticalOverlap =
              nodeA.position.y < nodeB.position.y + NODE_HEIGHT &&
              nodeA.position.y + NODE_HEIGHT > nodeB.position.y

            // 如果两个节点重叠但不在同一列
            if (horizontalOverlap && verticalOverlap && !sameColumn) {
              // 水平移动节点B，避免重叠
              nodeStore.moveNode(nodeB.id, {
                x: nodeA.position.x + NODE_WIDTH + 20,
                y: nodeB.position.y
              })
            }
          }
        }
      }

      // 应用全局协调机制 - 原则5
      // 确保所有节点都满足原则1-4，并处理节点位置变化的连锁反应

      // 处理所有节点的布局
      nodeMap.forEach((info, nodeId) => {
        const parentNode = getNodeById(nodeId)
        if (!parentNode) return

        // 计算父节点中心点
        const parentCenterX = parentNode.position.x + (parentNode.size?.width || NODE_WIDTH) / 2
        const parentCenterY = parentNode.position.y + NODE_HEIGHT / 2

        // 处理单个子节点与父节点水平对齐 - 原则1
        if (info.children.length === 1) {
          const childId = info.children[0]
          const childNode = getNodeById(childId)
          if (!childNode) return

          const childNodeWidth = childNode.size?.width || NODE_WIDTH

          // 计算子节点应该的X坐标，使其中心与父节点中心水平对齐
          const childX = parentCenterX - childNodeWidth / 2 + FIXED_HORIZONTAL_SPACING

          // 将子节点与父节点水平对齐 - 严格对齐 - 原则1
          nodeStore.moveNode(childId, {
            x: childX, // 使用计算后的X坐标，确保水平中心对齐
            y: parentNode.position.y // 使用父节点的Y坐标，确保水平对齐
          })
        }
        // 处理多子节点的对称分布 - 原则2
        else if (info.children.length > 1) {
          // 获取所有子节点
          const childNodes = info.children
            .map((id: string) => getNodeById(id))
            .filter((node: any) => node !== null) as NodeType[]

          if (childNodes.length === 0) return

          // 计算子节点的总高度
          const totalChildHeight = (childNodes.length - 1) * VERTICAL_SPACING + NODE_HEIGHT

          // 计算第一个子节点的Y坐标，使所有子节点对称分布在父节点两侧
          const firstChildY = parentCenterY - totalChildHeight / 2

          // 为每个子节点计算新的位置
          childNodes.forEach((childNode, index) => {
            // 计算子节点的X坐标，确保水平对齐
            const childNodeWidth = childNode.size?.width || NODE_WIDTH
            const childX = parentCenterX - childNodeWidth / 2 + FIXED_HORIZONTAL_SPACING

            // 计算子节点的Y坐标，确保垂直对称分布
            const childY = firstChildY + index * VERTICAL_SPACING

            // 移动子节点到新位置
            nodeStore.moveNode(childNode.id, {
              x: childX,
              y: childY
            })
          })
        }
      })
    }

    // 精确检测节点变化 - 全局版本，用于监听节点变化
    const detectNodeChanges = () => {
      // 构建当前的父子节点映射关系
      const currentParentMap = new Map<string, string[]>()
      
      // 遍历所有边，构建当前的父子关系
      edges.value.forEach(edge => {
        const parentId = edge.source
        const childId = edge.target
        
        if (!currentParentMap.has(parentId)) {
          currentParentMap.set(parentId, [])
        }
        
        currentParentMap.get(parentId)?.push(childId)
      })
      
      // 检测哪些父节点有新增子节点
      const parentsWithNewChildren = new Set<string>()
      
      // 比较当前和之前的父子映射关系
      currentParentMap.forEach((currentChildren, parentId) => {
        // 获取上一次的子节点列表 - 注意这里需要转换Set为数组
        const previousChildrenSet = previousState.value.previousParentChildMap.get(parentId)
        const previousChildren = previousChildrenSet ? Array.from(previousChildrenSet) : []
        
        // 检查是否有新增子节点
        const hasNewChildren = currentChildren.some(childId => !previousChildren.includes(childId))
        
        // 如果有新增子节点，添加到集合中
        if (hasNewChildren) {
          parentsWithNewChildren.add(parentId)
          console.log(`检测到父节点 ${parentId} 有新增子节点`)
          
          // 递归向上添加所有祖先节点，确保间距变化向上传导
          let currentParent = parentId
          let ancestorDepth = 0
          const ANCESTOR_PROPAGATION_DEPTH = 5 // 向上传导的最大深度
          
          while (currentParent && ancestorDepth < ANCESTOR_PROPAGATION_DEPTH) {
            const grandparentId = previousState.value.nodeParentMap.get(currentParent)
            if (grandparentId) {
              parentsWithNewChildren.add(grandparentId)
              currentParent = grandparentId
              ancestorDepth++
            } else {
              break
            }
          }
        }
      })
      
      // 更新previousParentChildMap，用于下次比较
      // 将数组格式的映射转换为Set格式保存
      const newPreviousParentChildMap = new Map<string, Set<string>>()
      currentParentMap.forEach((children, parentId) => {
        newPreviousParentChildMap.set(parentId, new Set(children))
      })
      previousState.value.previousParentChildMap = newPreviousParentChildMap
      
      return {
        parentsWithNewChildren,
        currentParentMap
      }
    }
    
    // 查找新添加的节点 - 增强版，更准确地识别新添加的节点
    const findNewlyAddedNode = (oldNodeCount: number) => {
      if (nodes.value.length <= oldNodeCount) return null

      // 获取当前所有节点ID
      const currentNodeIds = new Set(nodes.value.map(node => node.id))

      // 获取之前记录的节点ID
      const previousNodeIds = new Set(
        previousState.value.previousNodeIds || []
      )

      // 找出新添加的节点ID
      const newNodeIds = Array.from(currentNodeIds).filter(id => !previousNodeIds.has(id))

      // 更新之前记录的节点ID
      previousState.value.previousNodeIds = Array.from(currentNodeIds)

      // 如果找到新添加的节点，返回第一个
      if (newNodeIds.length > 0) {
        console.log('检测到新添加的节点:', newNodeIds)
        return newNodeIds[0]
      }

      // 如果没有找到新添加的节点，使用默认方法（假设最新添加的节点是数组中的最后一个）
      return nodes.value[nodes.value.length - 1].id
    }

    // 确保父节点与叔节点之间的间距增大而不是减小 - 全新优化版本
    const ensureParentUncleSpacing = (nodeMap: Map<string, any>, newNodeId: string) => {
      // 获取新节点信息
      const newNodeInfo = nodeMap.get(newNodeId)
      if (!newNodeInfo || !newNodeInfo.parent) return

      // 获取父节点
      const parentId = newNodeInfo.parent
      const parentInfo = nodeMap.get(parentId)
      const parentNode = getNodeById(parentId)
      if (!parentInfo || !parentNode) return

      // 检查父节点是否有父节点（祖父节点）
      if (!parentInfo.parent) return

      // 获取祖父节点
      const grandparentId = parentInfo.parent
      const grandparentInfo = nodeMap.get(grandparentId)
      const grandparentNode = getNodeById(grandparentId)
      if (!grandparentInfo || !grandparentNode) return

      // 获取父节点的所有兄弟节点（叔叔节点）
      const uncleIds = grandparentInfo.children.filter((id: string) => id !== parentId)
      if (uncleIds.length === 0) return

      // 获取所有叔叔节点
      const uncleNodes = uncleIds.map((id: string) => getNodeById(id)).filter(Boolean) as NodeType[]
      if (uncleNodes.length === 0) return

      // 计算父节点需要的空间 - 关键改进：更准确地计算所需空间
      const childrenCount = parentInfo.children.length

      // 基础空间需求 - 节点自身高度
      let requiredSpace = NODE_HEIGHT

      // 根据子节点数量动态计算所需空间
      if (childrenCount > 1) {
        // 如果有多个子节点，计算它们占用的垂直空间
        // 使用更大的间距系数，确保有足够空间
        requiredSpace = (childrenCount - 1) * VERTICAL_SPACING * 2.5 + NODE_HEIGHT
      } else if (childrenCount === 1) {
        // 即使只有一个子节点，也预留一定空间
        requiredSpace = NODE_HEIGHT * 3.0
      }

      // 增加父节点与叔叔节点之间的间距
      // 按Y坐标排序所有叔叔节点和父节点
      const allParentLevelNodes = [...uncleNodes, parentNode].sort((a, b) => a.position.y - b.position.y)

      // 找到父节点在排序后的位置
      const parentIndex = allParentLevelNodes.findIndex(node => node.id === parentId)
      if (parentIndex === -1) return

      // 预先检查叔叔节点的子节点情况，进一步增加间距
      let additionalSpaceForCousins = 0
      uncleIds.forEach((uncleId: string) => {
        const uncleInfo = nodeMap.get(uncleId)
        if (!uncleInfo) return

        // 如果叔叔节点有子节点，增加额外间距
        const cousinCount = uncleInfo.children.length
        if (cousinCount > 0) {
          // 根据表兄弟节点数量增加额外间距
          if (cousinCount > 1) {
            additionalSpaceForCousins = Math.max(
              additionalSpaceForCousins,
              (cousinCount - 1) * VERTICAL_SPACING * 1.5
            )
          } else {
            additionalSpaceForCousins = Math.max(
              additionalSpaceForCousins,
              NODE_HEIGHT * 2.0
            )
          }
        }
      })

      // 计算所需的最小间距 - 关键改进：确保间距会随子节点和表兄弟节点数量增加而显著增大
      // 使用更大的基础间距和更高的系数
      // 添加一个额外的乘数，确保新增子节点时间距增加更明显
      const newNodeMultiplier = 1.8 // 新增子节点时使用更大的乘数
      const minRequiredGap = (PARENT_UNCLE_VERTICAL_SPACING + requiredSpace + additionalSpaceForCousins) * newNodeMultiplier

      // 记录日志，便于调试
      console.log('计算父叔节点间距:', {
        parentId,
        childrenCount,
        requiredSpace,
        additionalSpaceForCousins,
        minRequiredGap
      })

      // 记录所有需要移动的节点及其新位置
      const nodesToMove = new Map<string, {x: number, y: number}>()

      // 如果父节点不是第一个节点，检查与上一个叔叔节点的间距
      if (parentIndex > 0) {
        const prevUncle = allParentLevelNodes[parentIndex - 1]
        if (!prevUncle) return

        // 计算当前间距
        const currentGap = parentNode.position.y - (prevUncle.position.y + NODE_HEIGHT)

        // 如果间距不足，向下移动父节点及其后面的所有节点
        if (currentGap < minRequiredGap) {
          const additionalSpace = minRequiredGap - currentGap

          // 记录父节点及其后面的所有节点需要移动的位置
          for (let i = parentIndex; i < allParentLevelNodes.length; i++) {
            const nodeToMove = allParentLevelNodes[i]
            if (!nodeToMove) continue

            nodesToMove.set(nodeToMove.id, {
              x: nodeToMove.position.x,
              y: nodeToMove.position.y + additionalSpace
            })
          }
        }
      }

      // 如果父节点不是最后一个节点，检查与下一个叔叔节点的间距
      if (parentIndex < allParentLevelNodes.length - 1) {
        // 获取更新后的父节点位置（考虑前面可能已经移动过）
        const updatedParentY = nodesToMove.has(parentId)
          ? nodesToMove.get(parentId)!.y
          : parentNode.position.y

        const nextUncle = allParentLevelNodes[parentIndex + 1]
        if (!nextUncle) return

        // 计算当前间距（考虑前面可能已经移动过）
        const nextUncleY = nodesToMove.has(nextUncle.id)
          ? nodesToMove.get(nextUncle.id)!.y
          : nextUncle.position.y

        const currentGap = nextUncleY - (updatedParentY + NODE_HEIGHT)

        // 如果间距不足，向下移动下一个叔叔节点及其后面的所有节点
        if (currentGap < minRequiredGap) {
          const additionalSpace = minRequiredGap - currentGap

          // 记录下一个叔叔节点及其后面的所有节点需要移动的位置
          for (let i = parentIndex + 1; i < allParentLevelNodes.length; i++) {
            const nodeToMove = allParentLevelNodes[i]
            if (!nodeToMove) continue

            const currentY = nodesToMove.has(nodeToMove.id)
              ? nodesToMove.get(nodeToMove.id)!.y
              : nodeToMove.position.y

            nodesToMove.set(nodeToMove.id, {
              x: nodeToMove.position.x,
              y: currentY + additionalSpace
            })
          }
        }
      }

      // 执行所有节点移动操作
      nodesToMove.forEach((newPosition, nodeId) => {
        nodeStore.moveNode(nodeId, newPosition)
      })

      // 递归处理：确保叔叔节点的子节点（表兄弟节点）也有足够的间距
      uncleIds.forEach((uncleId: string) => {
        const uncleInfo = nodeMap.get(uncleId)
        if (!uncleInfo || uncleInfo.children.length === 0) return

        // 如果叔叔节点有子节点，确保它们也有足够的间距
        const uncleNode = getNodeById(uncleId)
        if (!uncleNode) return

        // 获取叔叔节点的新位置（如果已移动）
        const uncleY = nodesToMove.has(uncleId)
          ? nodesToMove.get(uncleId)!.y
          : uncleNode.position.y

        // 移动叔叔节点的所有子节点，保持相对位置不变
        if (nodesToMove.has(uncleId)) {
          const deltaY = uncleY - uncleNode.position.y

          uncleInfo.children.forEach((cousinId: string) => {
            const cousinNode = getNodeById(cousinId)
            if (!cousinNode) return

            nodeStore.moveNode(cousinId, {
              x: cousinNode.position.x,
              y: cousinNode.position.y + deltaY
            })
          })
        }
      })

      // 获取更新后的父节点位置
      const updatedParentY = nodesToMove.has(parentId)
        ? nodesToMove.get(parentId)!.y
        : parentNode.position.y

      // 检查并处理表兄弟节点与新节点的重叠问题
      checkAndResolveCousinOverlap(nodeMap, newNodeId, parentId, uncleIds)
    }

    // 检查并解决表兄弟节点与新节点的重叠问题
    const checkAndResolveCousinOverlap = (nodeMap: Map<string, any>, newNodeId: string, parentId: string, uncleIds: string[]) => {
      // 获取新节点
      const newNode = getNodeById(newNodeId)
      if (!newNode) return

      // 获取父节点信息
      const parentInfo = nodeMap.get(parentId)
      if (!parentInfo) return

      // 获取父节点
      const parentNode = getNodeById(parentId)
      if (!parentNode) return

      // 收集所有表兄弟节点（叔叔节点的子节点）
      const cousinNodes: NodeType[] = []

      // 遍历所有叔叔节点
      uncleIds.forEach(uncleId => {
        const uncleInfo = nodeMap.get(uncleId)
        if (!uncleInfo || uncleInfo.children.length === 0) return

        // 获取叔叔节点
        const uncleNode = getNodeById(uncleId)
        if (!uncleNode) return

        // 收集所有表兄弟节点
        uncleInfo.children.forEach((cousinId: string) => {
          const cousinNode = getNodeById(cousinId)
          if (cousinNode) {
            cousinNodes.push(cousinNode)
          }
        })
      })

      // 如果没有表兄弟节点，直接返回
      if (cousinNodes.length === 0) return

      // 检查新节点与表兄弟节点是否重叠
      const NODE_HEIGHT = 90
      const MIN_VERTICAL_GAP = 150 // 使用更大的最小垂直间距

      // 按Y坐标排序所有表兄弟节点
      cousinNodes.sort((a, b) => a.position.y - b.position.y)

      // 检查新节点与每个表兄弟节点的垂直距离
      for (const cousinNode of cousinNodes) {
        // 检查新节点与表兄弟节点是否在同一列（X坐标接近）
        const sameColumn = Math.abs(newNode.position.x - cousinNode.position.x) < 240 // NODE_WIDTH

        if (sameColumn) {
          // 计算垂直距离
          const verticalDistance = Math.abs(newNode.position.y - cousinNode.position.y)

          // 如果距离太近，调整位置
          if (verticalDistance < MIN_VERTICAL_GAP) {
            // 确定哪个节点在上方
            if (newNode.position.y < cousinNode.position.y) {
              // 新节点在上方，向下移动表兄弟节点
              nodeStore.moveNode(cousinNode.id, {
                x: cousinNode.position.x,
                y: newNode.position.y + NODE_HEIGHT + MIN_VERTICAL_GAP
              })
            } else {
              // 表兄弟节点在上方，向下移动新节点
              nodeStore.moveNode(newNode.id, {
                x: newNode.position.x,
                y: cousinNode.position.y + NODE_HEIGHT + MIN_VERTICAL_GAP
              })

              // 如果新节点被移动，可能需要调整其兄弟节点
              const siblings = parentInfo.children.filter((id: string) => id !== newNodeId)
              if (siblings.length > 0) {
                // 获取所有兄弟节点
                const siblingNodes = siblings.map((id: string) => getNodeById(id)).filter(Boolean) as NodeType[]

                // 按Y坐标排序兄弟节点
                siblingNodes.sort((a, b) => a.position.y - b.position.y)

                // 计算新的垂直间距
                const totalSiblings = siblingNodes.length + 1 // 包括新节点
                const totalHeight = (totalSiblings - 1) * VERTICAL_SPACING

                // 计算父节点中心点
                const parentCenterY = parentNode.position.y + NODE_HEIGHT / 2

                // 计算第一个子节点的Y坐标
                const firstChildY = parentCenterY - totalHeight / 2

                // 重新布局所有子节点，包括新节点
                const allChildren = [...siblingNodes, newNode].sort((a, b) => a.position.y - b.position.y)

                allChildren.forEach((childNode, index) => {
                  nodeStore.moveNode(childNode.id, {
                    x: childNode.position.x,
                    y: firstChildY + index * VERTICAL_SPACING
                  })
                })
              }
            }
          }
        }
      }
    }

    // 存储上一次的节点和边的状态，用于检测变化 - 增强版，更全面地跟踪状态变化
    const previousState = ref({
      nodeCount: 0,
      edgeCount: 0,
      nodeParentMap: new Map<string, string>(), // 记录每个节点的父节点
      parentChildrenCount: new Map<string, number>(), // 记录每个父节点的子节点数量
      lastAddedNodeId: null as string | null, // 记录最后添加的节点ID
      lastAddedNodeParentId: null as string | null, // 记录最后添加的节点的父节点ID
      previousNodeIds: [] as string[], // 记录之前的所有节点ID
      nodePositions: new Map<string, {x: number, y: number}>(), // 记录每个节点的位置
      nodeAncestors: new Map<string, string[]>(), // 记录每个节点的所有祖先节点
      nodeDescendants: new Map<string, string[]>(), // 记录每个节点的所有后代节点
      nodesWithNewChildren: new Set<string>(), // 记录有新增子节点的节点
      lastLayoutTime: 0, // 记录上次布局时间
      layoutIterations: 0, // 记录布局迭代次数
      lastCalculatedSpacing: 0, // 上次计算的父叔节点间距
      parentSpacingHistory: new Map<string, Map<string, number>>(), // 修改为嵌套Map，记录每对父节点-叔叔节点的间距历史
      previousParentChildMap: new Map<string, Set<string>>() // 记录之前的父子关系映射
    })

    // 计算所需的间距 - 辅助函数 - 完全重写版本
    const calculateRequiredSpacing = (childrenCount: number) => {
      // 获取该父节点的历史间距记录
      const parentId = previousState.value.lastAddedNodeParentId || '';
      
      // 初始化父节点的间距历史记录（如果不存在）
      if (!previousState.value.parentSpacingHistory.has(parentId)) {
        previousState.value.parentSpacingHistory.set(parentId, new Map());
      }
      
      // 使用一个固定的键来存储总体间距
      const TOTAL_SPACING_KEY = '_total_';
      const parentSpacingMap = previousState.value.parentSpacingHistory.get(parentId)!;
      let totalSpacing = parentSpacingMap.get(TOTAL_SPACING_KEY) || 0;

      // 获取上次记录的子节点数量
      const lastChildCount = previousState.value.parentChildrenCount.get(parentId) || 0;

      // 检查是否是新增子节点
      const isNewChild = childrenCount > lastChildCount;

      // 如果是新增子节点，大幅增加间距
      if (isNewChild) {
        // 记录新的子节点数量
        previousState.value.parentChildrenCount.set(parentId, childrenCount);

        // 每次添加新子节点时，增加固定的间距 - 使用极大的增量
        const incrementPerChild = FIXED_SPACING_PER_CHILD; // 使用常量定义的值
        totalSpacing += incrementPerChild;

        // 更新父节点的总体间距历史记录
        parentSpacingMap.set(TOTAL_SPACING_KEY, totalSpacing);

        console.log(`检测到新增子节点，父节点 ${parentId} 子节点数量从 ${lastChildCount} 增加到 ${childrenCount}，间距增加 ${incrementPerChild}，当前累积间距: ${totalSpacing}`);
      }

      // 基础间距计算 - 使用指数增长确保间距随子节点数量快速增加
      const baseSpacing = BASE_PARENT_UNCLE_SPACING + childrenCount * PARENT_UNCLE_SPACING_INCREMENT;

      // 应用指数增长，确保子节点数量增加时间距增加更快
      const exponentialFactor = Math.pow(EXPONENTIAL_BASE, childrenCount);
      const calculatedSpacing = baseSpacing * exponentialFactor;

      // 确保间距至少达到最小要求
      const minRequiredSpacing = MIN_PARENT_UNCLE_GAP * (1 + childrenCount * 0.5);

      // 最终间距 = 最大值(计算间距, 累积间距, 最小间距)
      const finalSpacing = Math.max(calculatedSpacing, totalSpacing, minRequiredSpacing);

      console.log(`计算间距详情 - 子节点数量: ${childrenCount}, 累积间距: ${totalSpacing}, 计算间距: ${calculatedSpacing}, 最小间距: ${minRequiredSpacing}, 最终间距: ${finalSpacing}, 是否新增子节点: ${isNewChild}`);

      return finalSpacing;
    }

    // 强制调整父节点与叔叔节点之间的间距 - 考虑子节点位置的增强版
    // 增加childrenCount参数，确保每次新增子节点都能正确计算间距
    const forceAdjustParentUncleSpacing = (nodeMap: Map<string, any>, parentId: string, forcedChildrenCount?: number) => {
      console.log('强制调整父节点与叔叔节点间距 - 父节点:', parentId)

      // 获取父节点信息
      const parentInfo = nodeMap.get(parentId)
      if (!parentInfo || !parentInfo.parent) {
        console.log('父节点没有父节点，无法调整间距')
        return false
      }

      // 获取父节点
      const parentNode = getNodeById(parentId)
      if (!parentNode) {
        console.log('找不到父节点，无法调整间距')
        return false
      }

      // 获取祖父节点信息
      const grandparentId = parentInfo.parent
      const grandparentInfo = nodeMap.get(grandparentId)
      if (!grandparentInfo) {
        console.log('找不到祖父节点，无法调整间距')
        return false
      }

      // 获取叔叔节点（父节点的兄弟节点）
      const uncleIds = grandparentInfo.children.filter(id => id !== parentId)
      if (uncleIds.length === 0) {
        console.log('没有叔叔节点，无法调整间距')
        return false
      }

      console.log('找到叔叔节点:', uncleIds)

      // 获取所有叔叔节点
      const uncleNodes = uncleIds
        .map(id => getNodeById(id))
        .filter(Boolean)

      if (uncleNodes.length === 0) {
        console.log('无法获取叔叔节点对象，无法调整间距')
        return false
      }

      // 计算子节点数量 - 使用强制指定的数量或实际数量
      const childrenCount = forcedChildrenCount !== undefined
        ? forcedChildrenCount // 使用外部指定的子节点数量
        : parentInfo.children.length // 使用实际子节点数量

      // 初始化父节点的间距历史记录（如果不存在）
      if (!previousState.value.parentSpacingHistory.has(parentId)) {
        previousState.value.parentSpacingHistory.set(parentId, new Map());
      }
      const parentSpacingMap = previousState.value.parentSpacingHistory.get(parentId)!;

      // 记录当前子节点数量，无论是否真的增加
      previousState.value.parentChildrenCount.set(parentId, childrenCount);

      // 记录所有需要移动的节点及其新位置
      const nodesToMove = new Map<string, {x: number, y: number}>()

      // 计算父节点及其子树的边界
      const parentTreeBounds = calculateNodeTreeBounds(parentId, nodeMap);
      if (!parentTreeBounds) {
        console.log('无法计算父节点树边界，无法调整间距');
        return false;
      }
      
      console.log('父节点树边界:', parentTreeBounds);

      // 按Y坐标排序所有叔叔节点
      const sortedUncleNodes = [...uncleNodes].sort((a, b) => a.position.y - b.position.y);

      // 找出父节点上方的所有叔叔节点
      const unclesAbove = sortedUncleNodes.filter(uncle => uncle.position.y < parentNode.position.y);

      // 找出父节点下方的所有叔叔节点
      const unclesBelow = sortedUncleNodes.filter(uncle => uncle.position.y > parentNode.position.y);

      // 处理所有叔叔节点 - 无论上方还是下方
      if (uncleNodes.length > 0) {
        // 对每个叔叔节点进行处理
        for (const uncle of uncleNodes) {
          // 计算叔叔节点及其子树的边界
          const uncleTreeBounds = calculateNodeTreeBounds(uncle.id, nodeMap);
          if (!uncleTreeBounds) continue;
          
          console.log(`叔叔节点(${uncle.id})树边界:`, uncleTreeBounds);
          
          // 获取该对父节点-叔叔节点的历史间距
          const historicalSpacing = parentSpacingMap.get(uncle.id) || 0;
          
          // 计算当前间距 - 基于树边界而不是节点中心点
          let currentGap = 0;
          let requiredGap = 0;
          
          // 根据叔叔节点位置确定间距计算方式
          if (uncle.position.y < parentNode.position.y) {
            // 叔叔节点在父节点上方
            // 间距 = 父节点树顶部 - 叔叔节点树底部
            currentGap = parentTreeBounds.top - uncleTreeBounds.bottom;
            
            // 计算基础间距 - 根据子节点数量动态调整
            const baseSpacing = TERMINAL_NODE_SPACING * (1 + childrenCount * 0.5);
            
            // 每次调用增加固定的间距增量
            const incrementPerChild = 3000; // 每次调用增加3000单位的间距
            
            // 计算新的间距，确保不小于历史间距
            requiredGap = Math.max(
              baseSpacing + incrementPerChild,
              historicalSpacing + incrementPerChild,
              TERMINAL_NODE_SPACING * (1 + childrenCount * 0.5),
              180 * childrenCount // 确保至少有足够的空间容纳所有子节点
            );
          } else {
            // 叔叔节点在父节点下方
            // 间距 = 叔叔节点树顶部 - 父节点树底部
            currentGap = uncleTreeBounds.top - parentTreeBounds.bottom;
            
            // 计算基础间距 - 根据子节点数量动态调整
            const baseSpacing = TERMINAL_NODE_SPACING * (1 + childrenCount * 0.5);
            
            // 每次调用增加固定的间距增量
            const incrementPerChild = 3000; // 每次调用增加3000单位的间距
            
            // 计算新的间距，确保不小于历史间距
            requiredGap = Math.max(
              baseSpacing + incrementPerChild,
              historicalSpacing + incrementPerChild,
              TERMINAL_NODE_SPACING * (1 + childrenCount * 0.5),
              180 * childrenCount // 确保至少有足够的空间容纳所有子节点
            );
          }
          
          // 更新历史记录
          parentSpacingMap.set(uncle.id, requiredGap);
          
          console.log(`父节点(${parentId})与叔叔节点(${uncle.id})间距计算:`, {
            currentGap,
            historicalSpacing,
            requiredGap,
            childrenCount
          });

          // 如果当前间距小于所需间距，调整位置
          if (currentGap < requiredGap) {
            const additionalSpace = requiredGap - currentGap;
            
            // 根据叔叔节点相对于父节点的位置决定移动方向和方式
            if (uncle.position.y < parentNode.position.y) {
              // 叔叔节点在父节点上方
              console.log(`叔叔节点(${uncle.id})在父节点上方，向上移动叔叔节点及其子树，距离: ${additionalSpace}`);
              
              // 向上移动叔叔节点及其所有祖先
              moveNodeAndAncestorsUp(uncle.id, additionalSpace, nodeMap);
            } else {
              // 叔叔节点在父节点下方
              console.log(`叔叔节点(${uncle.id})在父节点下方，向下移动叔叔节点及其子树，距离: ${additionalSpace}`);
              
              // 向下移动叔叔节点及其所有后代
              moveNodeAndDescendantsDown(uncle.id, additionalSpace, nodeMap);
            }
          } else {
            console.log(`父节点(${parentId})与叔叔节点(${uncle.id})的间距已足够:`, {
              currentGap,
              requiredGap
            });
          }
        }
      } else {
        console.log(`父节点(${parentId})没有叔叔节点，无需调整间距`);
      }

      // 特殊处理：确保所有叔叔节点的子节点（表节点）与父节点的子节点不会重叠
      // 这是对上面逻辑的补充，确保即使叔叔节点移动后，其子节点也不会与父节点的子节点重叠
      if (uncleNodes.length > 0 && parentInfo.children.length > 0) {
        // 获取父节点的所有子节点
        const childNodes = parentInfo.children.map(id => getNodeById(id)).filter(Boolean);

        // 获取所有叔叔节点的子节点（表节点）
        const cousinNodes: any[] = [];
        uncleNodes.forEach(uncle => {
          const uncleInfo = nodeMap.get(uncle.id);
          if (uncleInfo && uncleInfo.children.length > 0) {
            uncleInfo.children.forEach(cousinId => {
              const cousinNode = getNodeById(cousinId);
              if (cousinNode) {
                cousinNodes.push({
                  node: cousinNode,
                  parentId: uncle.id
                });
              }
            });
          }
        });

        // 检查每个子节点与每个表节点是否有重叠
        childNodes.forEach(childNode => {
          if (!childNode) return;

          // 获取子节点的更新后位置
          const childY = nodesToMove.has(childNode.id)
            ? nodesToMove.get(childNode.id)!.y
            : childNode.position.y;

          cousinNodes.forEach(cousin => {
            // 获取表节点的更新后位置
            const cousinY = nodesToMove.has(cousin.node.id)
              ? nodesToMove.get(cousin.node.id)!.y
              : cousin.node.position.y;

            // 计算垂直距离
            const verticalDistance = Math.abs(childY - cousinY);

            // 如果距离太近，需要进一步调整
            if (verticalDistance < NODE_HEIGHT * 1.5) {
              // 确定移动方向 - 向上或向下
              const moveDirection = cousinY < childY ? -1 : 1;

              // 计算需要移动的距离
              const additionalDistance = NODE_HEIGHT * 1.5 - verticalDistance;
              const moveDistance = additionalDistance * moveDirection;

              console.log(`检测到子节点(${childNode.id})与表节点(${cousin.node.id})重叠，移动表节点，距离: ${moveDistance}`);

              // 移动表节点
              nodesToMove.set(cousin.node.id, {
                x: cousin.node.position.x,
                y: cousinY + moveDistance
              });

              // 同时移动表节点的所有后代节点
              const descendants = getAllDescendants(cousin.node.id, nodeMap);
              descendants.forEach(descendantId => {
                const descendantNode = getNodeById(descendantId);
                if (descendantNode) {
                  // 考虑节点可能已经被移动过
                  const currentY = nodesToMove.has(descendantId)
                    ? nodesToMove.get(descendantId)!.y
                    : descendantNode.position.y;

                  nodesToMove.set(descendantId, {
                    x: descendantNode.position.x,
                    y: currentY + moveDistance
                  });
                }
              });
            }
          });
        });
      }

      // 全局节点重叠检测 - 确保没有节点重叠
      if (GLOBAL_NODE_OVERLAP_CHECK) {
        console.log('执行全局节点重叠检测')

        // 获取所有节点
        const allNodes = nodes.value

        // 按Y坐标排序节点，确保从上到下处理
        const sortedNodes = [...allNodes].sort((a, b) => a.position.y - b.position.y)

        // 检查所有节点对，查找重叠
        for (let i = 0; i < sortedNodes.length; i++) {
          for (let j = i + 1; j < sortedNodes.length; j++) {
            const nodeA = sortedNodes[i]
            const nodeB = sortedNodes[j]

            // 检查节点是否在同一列（X坐标接近）
            const sameColumn = Math.abs(nodeA.position.x - nodeB.position.x) < NODE_WIDTH / 2

            if (sameColumn) {
              // 获取节点A的更新后位置（如果已经被标记为需要移动）
              const nodeAY = nodesToMove.has(nodeA.id)
                ? nodesToMove.get(nodeA.id)!.y
                : nodeA.position.y

              // 获取节点B的更新后位置（如果已经被标记为需要移动）
              const nodeBY = nodesToMove.has(nodeB.id)
                ? nodesToMove.get(nodeB.id)!.y
                : nodeB.position.y

              // 计算垂直距离
              const verticalDistance = Math.abs(nodeAY - nodeBY)

              // 如果距离太近，调整位置
              if (verticalDistance < NODE_HEIGHT + OVERLAP_SAFETY_MARGIN) {
                console.log(`检测到节点重叠: ${nodeA.id} 和 ${nodeB.id}，调整位置`)

                // 向下移动节点B
                const newY = nodeAY + NODE_HEIGHT + OVERLAP_SAFETY_MARGIN

                // 更新节点B的位置
                nodesToMove.set(nodeB.id, {
                  x: nodeB.position.x,
                  y: newY
                })

                // 同时移动节点B的所有后代节点
                const descendants = getAllDescendants(nodeB.id, nodeMap)
                descendants.forEach(descendantId => {
                  const descendantNode = getNodeById(descendantId)
                  if (descendantNode) {
                    // 计算相对位置变化
                    const deltaY = newY - nodeB.position.y

                    // 如果后代节点已经被标记为需要移动，则累加位移
                    if (nodesToMove.has(descendantId)) {
                      const currentPos = nodesToMove.get(descendantId)!
                      nodesToMove.set(descendantId, {
                        x: currentPos.x,
                        y: currentPos.y + deltaY
                      })
                    } else {
                      // 否则，直接设置新位置
                      nodesToMove.set(descendantId, {
                        x: descendantNode.position.x,
                        y: descendantNode.position.y + deltaY
                      })
                    }
                  }
                })
              }
            }
          }
        }
      }

      // 执行所有节点移动操作
      nodesToMove.forEach((newPosition, nodeId) => {
        const node = getNodeById(nodeId)
        if (!node) return

        // 移动节点
        nodeStore.moveNode(nodeId, newPosition)
      })

      return nodesToMove.size > 0
    }

    // 辅助函数：获取节点的所有后代节点
    const getAllDescendants = (nodeId: string, nodeMap: Map<string, any>) => {
      const descendants: string[] = []

      // 递归获取所有后代节点
      const collectDescendants = (id: string) => {
        const nodeInfo = nodeMap.get(id)
        if (!nodeInfo) return

        // 添加所有子节点
        nodeInfo.children.forEach((childId: string) => {
          descendants.push(childId)
          // 递归获取孙子节点
          collectDescendants(childId)
        })
      }

      // 从指定节点开始收集所有后代
      collectDescendants(nodeId)

      return descendants
    }
    
    // 计算节点及其所有后代的边界
    const calculateNodeTreeBounds = (nodeId: string, nodeMap: Map<string, any>) => {
      // 获取节点
      const node = getNodeById(nodeId)
      if (!node) return null
      
      // 初始化边界为当前节点的边界
      let bounds = {
        top: node.position.y,
        bottom: node.position.y + NODE_HEIGHT,
        left: node.position.x,
        right: node.position.x + NODE_WIDTH
      }
      
      // 获取所有后代节点
      const descendants = getAllDescendants(nodeId, nodeMap)
      
      // 遍历所有后代节点，扩展边界
      descendants.forEach(descendantId => {
        const descendantNode = getNodeById(descendantId)
        if (!descendantNode) return
        
        // 更新边界
        bounds.top = Math.min(bounds.top, descendantNode.position.y)
        bounds.bottom = Math.max(bounds.bottom, descendantNode.position.y + NODE_HEIGHT)
        bounds.left = Math.min(bounds.left, descendantNode.position.x)
        bounds.right = Math.max(bounds.right, descendantNode.position.x + NODE_WIDTH)
      })
      
      // 检测并解决重叠问题 - 增强版本
      const resolveOverlapping = () => {
        // 计算节点的有效区域，增加必要的间距缓冲区
        const getNodeBounds = (node: any, extraPadding = 0) => {
          return {
            left: node.position.x - extraPadding,
            right: node.position.x + NODE_WIDTH + extraPadding,
            top: node.position.y - extraPadding,
            bottom: node.position.y + NODE_HEIGHT + extraPadding
          }
        }

        // 检测两个节点是否重叠，增加更大的间距要求
        const checkOverlap = (node1: any, node2: any) => {
          // 使用固定的间距要求，确保节点之间有更大的空间
          const safetyPadding = 40; // 增加间距安全缓冲区
          const bounds1 = getNodeBounds(node1, safetyPadding)
          const bounds2 = getNodeBounds(node2, safetyPadding)

          return !(bounds1.right < bounds2.left ||
                   bounds1.left > bounds2.right ||
                   bounds1.bottom < bounds2.top ||
                   bounds1.top > bounds2.bottom)
        }
        
        // 实际执行节点重叠检测和解决
        const allNodes = nodes.value;
        const movedNodes = new Set(); // 记录已移动的节点以避免重复移动
        
        // 按Y坐标排序节点，确保从上到下处理
        const sortedNodes = [...allNodes].sort((a, b) => a.position.y - b.position.y);
        
        // 多次迭代检查，确保所有重叠都被解决
        for (let iteration = 0; iteration < 3; iteration++) {
          let overlapsResolved = 0;
          
          for (let i = 0; i < sortedNodes.length; i++) {
            for (let j = i + 1; j < sortedNodes.length; j++) {
              const nodeA = sortedNodes[i];
              const nodeB = sortedNodes[j];
              
              // 检查节点是否重叠
              if (checkOverlap(nodeA, nodeB)) {
                // 检查节点是否在同一列（X坐标接近）
                const sameColumn = Math.abs(nodeA.position.x - nodeB.position.x) < NODE_WIDTH / 2;
                
                if (sameColumn) {
                  // 垂直方向的重叠，向下移动节点B
                  const minDistance = NODE_HEIGHT + VERTICAL_GAP; // 使用更大的间距
                  
                  // 移动节点B到合适位置
                  nodeStore.moveNode(nodeB.id, {
                    x: nodeB.position.x,
                    y: nodeA.position.y + minDistance
                  });
                  
                  // 记录节点已经被移动
                  movedNodes.add(nodeB.id);
                  overlapsResolved++;
                } else {
                  // 非垂直方向的重叠，水平移动节点B
                  const minHorizontalDistance = NODE_WIDTH + 30; // 使用更大的水平间距
                  
                  // 移动节点B到合适位置
                  nodeStore.moveNode(nodeB.id, {
                    x: nodeA.position.x + minHorizontalDistance,
                    y: nodeB.position.y
                  });
                  
                  // 记录节点已经被移动
                  movedNodes.add(nodeB.id);
                  overlapsResolved++;
                }
              }
            }
          }
          
          // 如果当前迭代中没有解决任何重叠，则退出循环
          if (overlapsResolved === 0) break;
          
          // 更新节点排序，以便下一次迭代有准确的位置信息
          sortedNodes.sort((a, b) => a.position.y - b.position.y);
        }
        
        // 检查并处理节点的父子关系
        // 确保子节点始终在父节点右侧
        edges.value.forEach(edge => {
          const sourceNode = getNodeById(edge.source);
          const targetNode = getNodeById(edge.target);
          
          if (!sourceNode || !targetNode) return;
          
          // 如果目标节点在源节点左侧，强制将其移到右侧
          if (targetNode.position.x <= sourceNode.position.x + NODE_WIDTH) {
            const minTargetX = sourceNode.position.x + NODE_WIDTH + MIN_HORIZONTAL_GAP + 20;
            
            nodeStore.moveNode(targetNode.id, {
              x: minTargetX,
              y: targetNode.position.y
            });
          }
        });
        
        return movedNodes.size > 0; // 返回是否有节点被移动
      }
      
      return bounds
    }
    
    // 向上移动节点及其所有祖先
    const moveNodeAndAncestorsUp = (nodeId: string, distance: number, nodeMap: Map<string, any>) => {
      // 获取节点
      const node = getNodeById(nodeId)
      if (!node) return
      
      // 移动当前节点
      nodeStore.moveNode(nodeId, {
        x: node.position.x,
        y: node.position.y - distance
      })
      
      // 获取父节点ID
      const nodeInfo = nodeMap.get(nodeId)
      if (!nodeInfo || !nodeInfo.parent) return
      
      // 递归移动父节点
      moveNodeAndAncestorsUp(nodeInfo.parent, distance, nodeMap)
    }
    
    // 向下移动节点及其所有后代
    const moveNodeAndDescendantsDown = (nodeId: string, distance: number, nodeMap: Map<string, any>) => {
      // 获取节点
      const node = getNodeById(nodeId)
      if (!node) return
      
      // 移动当前节点
      nodeStore.moveNode(nodeId, {
        x: node.position.x,
        y: node.position.y + distance
      })
      
      // 移动所有后代节点
      moveNodeDescendants(nodeId, distance, nodeMap)
    }
    
    // 移动节点的所有后代
    const moveNodeDescendants = (nodeId: string, distance: number, nodeMap: Map<string, any>) => {
      const nodeInfo = nodeMap.get(nodeId)
      if (!nodeInfo) return
      
      // 移动所有子节点
      nodeInfo.children.forEach((childId: string) => {
        const childNode = getNodeById(childId)
        if (!childNode) return
        
        // 移动子节点
        nodeStore.moveNode(childId, {
          x: childNode.position.x,
          y: childNode.position.y + distance
        })
        
        // 递归移动子节点的所有后代
        moveNodeDescendants(childId, distance, nodeMap)
      })
    }

    // 专门处理父节点与叔叔节点之间的间距 - 增强版，支持历史间距记录，考虑子树边界
    const adjustParentUncleSpacing = (nodeMap: Map<string, any>, changedParents: Map<string, {addedChildren: string[], removedChildren: string[]}>) => {
      if (changedParents.size === 0) return false

      console.log('调整父节点与叔叔节点间距 - 变化的父节点:', Array.from(changedParents.keys()))

      // 记录所有需要移动的节点及其新位置
      const nodesToMove = new Map<string, {x: number, y: number}>()

      // 对每个有变化的父节点应用间距调整
      changedParents.forEach((changes, parentId) => {
        // 只处理有新增子节点的父节点
        if (changes.addedChildren.length === 0) return

        const parentInfo = nodeMap.get(parentId)
        if (!parentInfo || !parentInfo.parent) return

        const parentNode = getNodeById(parentId)
        if (!parentNode) return

        const grandparentId = parentInfo.parent
        const grandparentInfo = nodeMap.get(grandparentId)
        if (!grandparentInfo) return

        // 获取所有叔叔节点（父节点的兄弟节点）
        const uncleIds = grandparentInfo.children.filter(id => id !== parentId)
        if (uncleIds.length === 0) return

        // 获取所有叔叔节点
        const uncleNodes = uncleIds
          .map(id => getNodeById(id))
          .filter(Boolean)

        if (uncleNodes.length === 0) return

        // 初始化父节点的间距历史记录（如果不存在）
        if (!previousState.value.parentSpacingHistory.has(parentId)) {
          previousState.value.parentSpacingHistory.set(parentId, new Map());
        }
        const parentSpacingMap = previousState.value.parentSpacingHistory.get(parentId)!;

        // 计算父节点及其子树的边界
        const parentTreeBounds = calculateNodeTreeBounds(parentId, nodeMap);
        if (!parentTreeBounds) return;
        
        console.log(`父节点 ${parentId} 树边界:`, parentTreeBounds);

        // 处理所有叔叔节点
        uncleNodes.forEach(uncle => {
          // 获取该对父节点-叔叔节点的历史间距
          const historicalSpacing = parentSpacingMap.get(uncle.id) || 0;
          
          // 计算叔叔节点及其子树的边界
          const uncleTreeBounds = calculateNodeTreeBounds(uncle.id, nodeMap);
          if (!uncleTreeBounds) return;
          
          console.log(`叔叔节点 ${uncle.id} 树边界:`, uncleTreeBounds);
          
          // 计算当前间距 - 基于树边界而不是节点中心点
          let currentSpacing = 0;
          
          // 根据叔叔节点位置确定间距计算方式
          if (uncle.position.y < parentNode.position.y) {
            // 叔叔节点在父节点上方
            // 间距 = 父节点树顶部 - 叔叔节点树底部
            currentSpacing = parentTreeBounds.top - uncleTreeBounds.bottom;
          } else {
            // 叔叔节点在父节点下方
            // 间距 = 叔叔节点树顶部 - 父节点树底部
            currentSpacing = uncleTreeBounds.top - parentTreeBounds.bottom;
          }
          
          // 确保间距为正值
          currentSpacing = Math.abs(currentSpacing);
          
          // 计算基础间距 - 基于新增子节点数量
          const BASE_SPACING = 200 // 基础间距
          const SPACING_PER_CHILD = 150 // 每个新增子节点增加的间距
          const baseSpacing = BASE_SPACING + changes.addedChildren.length * SPACING_PER_CHILD
          
          // 计算新的间距，确保不小于历史间距
          const requiredSpacing = Math.max(baseSpacing, historicalSpacing + SPACING_PER_CHILD);
          
          // 更新历史记录
          parentSpacingMap.set(uncle.id, requiredSpacing);
          
          console.log(`父节点 ${parentId} 与叔叔节点 ${uncle.id} 间距计算:`, {
            currentSpacing,
            historicalSpacing,
            baseSpacing,
            requiredSpacing,
            addedChildrenCount: changes.addedChildren.length
          });
          
          // 如果当前间距小于所需间距，调整位置
          if (currentSpacing < requiredSpacing) {
            // 计算需要增加的间距
            const additionalSpacing = requiredSpacing - currentSpacing
            
            // 确定移动方向
            const moveDirection = uncle.position.y < parentNode.position.y ? -1 : 1
            
            // 计算移动距离
            const moveDistance = additionalSpacing * moveDirection
            
            console.log(`移动叔叔节点 ${uncle.id} 及其子树 - 当前间距: ${currentSpacing}, 所需间距: ${requiredSpacing}, 移动距离: ${moveDistance}`)
            
            if (moveDirection < 0) {
              // 叔叔节点在父节点上方，向上移动叔叔节点及其所有祖先
              moveNodeAndAncestorsUp(uncle.id, additionalSpacing, nodeMap);
            } else {
              // 叔叔节点在父节点下方，向下移动叔叔节点及其所有后代
              moveNodeAndDescendantsDown(uncle.id, additionalSpacing, nodeMap);
            }
          }
        })
      })

      return true; // 返回操作已执行
    }
    
    // 辅助函数：移动节点及其所有后代
    const moveNodeAndDescendants = (nodeId: string, moveDistance: number, nodeMap: Map<string, any>, nodesToMove: Map<string, {x: number, y: number}>) => {
      const nodeInfo = nodeMap.get(nodeId)
      if (!nodeInfo) return
      
      // 移动所有子节点
      nodeInfo.children.forEach((childId: string) => {
        const childNode = getNodeById(childId)
        if (!childNode) return
        
        // 考虑节点可能已经被移动过
        const currentY = nodesToMove.has(childId)
          ? nodesToMove.get(childId)!.y
          : childNode.position.y
        
        // 记录子节点的新位置
        nodesToMove.set(childId, {
          x: childNode.position.x,
          y: currentY + moveDistance
        })
        
        // 递归移动子节点的所有后代
        moveNodeAndDescendants(childId, moveDistance, nodeMap, nodesToMove)
      })
    }

    // 辅助函数：确保间距历史被保留
    const ensureSpacingHistoryPreserved = (nodeId1: string, nodeId2: string) => {
      // 确保两个节点的间距历史记录存在
      if (!previousState.value.parentSpacingHistory.has(nodeId1)) {
        previousState.value.parentSpacingHistory.set(nodeId1, new Map());
      }
      if (!previousState.value.parentSpacingHistory.has(nodeId2)) {
        previousState.value.parentSpacingHistory.set(nodeId2, new Map());
      }
      
      const spacing1 = previousState.value.parentSpacingHistory.get(nodeId1)!.get(nodeId2) || 0;
      const spacing2 = previousState.value.parentSpacingHistory.get(nodeId2)!.get(nodeId1) || 0;
      
      // 使用最大值确保间距不会减小
      const maxSpacing = Math.max(spacing1, spacing2);
      
      // 更新两个方向的间距记录
      previousState.value.parentSpacingHistory.get(nodeId1)!.set(nodeId2, maxSpacing);
      previousState.value.parentSpacingHistory.get(nodeId2)!.set(nodeId1, maxSpacing);
      
      console.log(`保留节点间距历史: ${nodeId1} <-> ${nodeId2}, 间距: ${maxSpacing}`);
    }

    // 监听节点或连线变化，自动应用布局
    watch([() => nodes.value.length, () => edges.value.length], ([newNodeCount, newEdgeCount], [oldNodeCount, oldEdgeCount]) => {
      // 当节点或连线数量变化时，应用自动布局
      if (newNodeCount > 0 && (newNodeCount !== oldNodeCount || newEdgeCount !== oldEdgeCount)) {
        // 只有在添加新节点或连线时才启用自动布局
        if (newNodeCount > oldNodeCount || newEdgeCount > oldEdgeCount) {
          // 重新启用自动布局
          autoLayoutEnabled.value = true

          // 添加延迟，确保DOM已更新并且所有节点信息都已加载
          setTimeout(() => {
            // 构建节点关系树
            const { nodeMap, rootNodes } = buildNodeTree()
            
            // 使用新的精确检测函数检测节点变化
            const { parentsWithNewChildren, currentParentMap } = detectNodeChanges()
            console.log('精确检测到的父节点变化:', Array.from(parentsWithNewChildren))
            
            // 找出新添加的节点和新添加的边
            const newNodeId = findNewlyAddedNode(oldNodeCount)
            console.log('检测到新添加的节点:', newNodeId)

            // 记录新节点的父节点
            let newNodeParentId = null
            if (newNodeId) {
              // 查找新节点的父节点
              for (const edge of edges.value) {
                if (edge.target === newNodeId) {
                  newNodeParentId = edge.source
                  break
                }
              }

              // 更新最后添加的节点记录
              previousState.value.lastAddedNodeId = newNodeId
              previousState.value.lastAddedNodeParentId = newNodeParentId

              console.log('新节点的父节点:', newNodeParentId)
            }
            
            // 检测新增的叔叔节点，确保保留已有的父节点和叔叔节点间距
            const changedParents = detectNodeChangesInternal(nodeMap);
            changedParents.forEach(({ addedChildren }, parentId) => {
              // 如果是祖父节点新增了子节点（即新增了叔叔节点）
              const grandparentInfo = nodeMap.get(parentId);
              if (grandparentInfo) {
                // 获取所有子节点（包括新增的叔叔节点）
                grandparentInfo.children.forEach(uncleId => {
                  // 对于每个叔叔节点，检查它是否有兄弟节点
                  grandparentInfo.children.forEach(siblingId => {
                    if (uncleId !== siblingId) {
                      // 确保这对兄弟节点之间的间距记录被保留
                      ensureSpacingHistoryPreserved(siblingId, uncleId);
                    }
                  });
                });
              }
            });

            // 使用已经从detectNodeChanges获取的parentsWithNewChildren
            // 不再需要重复构建父子关系映射和检测变化

            // 更新状态记录 - 增强版，更全面地记录状态
            // 记录所有节点的当前位置
            const currentNodePositions = new Map<string, {x: number, y: number}>()
            nodes.value.forEach(node => {
              currentNodePositions.set(node.id, { ...node.position })
            })

            // 构建节点祖先和后代关系
            const nodeAncestors = new Map<string, string[]>()
            const nodeDescendants = new Map<string, string[]>()
            
            // 首先构建子节点到父节点的映射
            const childToParentMap = new Map<string, string>()
            currentParentMap.forEach((childIds, parentId) => {
              childIds.forEach(childId => {
                childToParentMap.set(childId, parentId)
              })
            })

            // 递归计算所有祖先节点
            const calculateAncestors = (nodeId: string, ancestors: string[] = []) => {
              const parentId = childToParentMap.get(nodeId)
              if (parentId) {
                const newAncestors = [...ancestors, parentId]
                nodeAncestors.set(nodeId, newAncestors)
                calculateAncestors(parentId, newAncestors)
              } else {
                nodeAncestors.set(nodeId, ancestors)
              }
            }

            // 递归计算所有后代节点
            const calculateDescendants = (nodeId: string) => {
              const children: string[] = []

              // 查找所有直接子节点
              currentParentMap.forEach((childIds, potentialParentId) => {
                if (potentialParentId === nodeId) {
                  // 添加所有子节点
                  children.push(...childIds)

                  // 递归查找每个子节点的子节点
                  childIds.forEach(childId => {
                    const grandchildren = calculateDescendants(childId)
                    children.push(...grandchildren)
                  })
                }
              })

              nodeDescendants.set(nodeId, children)
              return children
            }

            // 计算所有节点的祖先和后代关系
            nodes.value.forEach(node => {
              calculateAncestors(node.id)
            })

            // 找出所有根节点（没有父节点的节点）
            const rootNodeIds = nodes.value
              .filter(node => !childToParentMap.has(node.id))
              .map(node => node.id)

            // 从根节点开始计算所有后代
            rootNodeIds.forEach(rootId => {
              calculateDescendants(rootId)
            })

            // 更新状态记录，包含更多信息
            previousState.value = {
              ...previousState.value,
              nodeCount: newNodeCount,
              edgeCount: newEdgeCount,
              nodeParentMap: childToParentMap,
              parentChildrenCount: new Map(
                Array.from(currentParentMap.entries()).map(([parentId, children]) => [parentId, children.length])
              ),
              nodePositions: currentNodePositions,
              nodeAncestors,
              nodeDescendants,
              nodesWithNewChildren: parentsWithNewChildren,
              lastLayoutTime: Date.now(),
              layoutIterations: previousState.value.layoutIterations + 1
            }

            // 如果有父节点新增了子节点，强制增大父节点与叔叔节点之间的间距
            if (parentsWithNewChildren.size > 0) {
              console.log('检测到有父节点新增子节点，强制调整间距:', Array.from(parentsWithNewChildren))

              // 对每个有新增子节点的父节点应用强制间距调整
              let anyNodeMoved = false

              // 创建一个处理顺序数组，确保从下到上处理节点（先处理子节点，再处理父节点）
              const parentsToProcess = Array.from(parentsWithNewChildren)

              // 按照节点层级排序，确保先处理叶子节点
              parentsToProcess.sort((a, b) => {
                const aInfo = nodeMap.get(a)
                const bInfo = nodeMap.get(b)
                // 子节点数量越多，层级越高，越晚处理
                return (aInfo?.children?.length || 0) - (bInfo?.children?.length || 0)
              })

              console.log('处理顺序:', parentsToProcess)

              for (const parentId of parentsToProcess) {
                // 获取当前父节点的子节点数量
                const nodeInfo = nodeMap.get(parentId)
                if (!nodeInfo) continue

                const childrenCount = nodeInfo.children.length

                // 使用强制调整函数处理父节点与叔叔节点之间的间距
                // 传递实际的子节点数量，确保间距计算正确

                // 多次调用强制调整函数，确保间距累积增加
                let moved = false;
                for (let i = 0; i < 5; i++) {
                  // 每次使用更大的子节点数量，确保间距足够大
                  const forcedChildCount = childrenCount * (i + 2);
                  console.log(`处理父节点 ${parentId}，第${i+1}次强制调整，使用子节点数量: ${forcedChildCount}`);

                  const result = forceAdjustParentUncleSpacing(nodeMap, parentId, forcedChildCount);
                  moved = moved || result;
                }

                anyNodeMoved = anyNodeMoved || moved

                // 如果有节点移动，重新构建节点关系树
                if (moved) {
                  // 更新节点映射
                  const { nodeMap: updatedNodeMap } = buildNodeTree()
                  // 更新当前使用的nodeMap引用
                  Object.assign(nodeMap, updatedNodeMap)
                }
              }

              console.log('强制调整父节点与叔叔节点间距完成，是否有节点被移动:', anyNodeMoved)

              // 如果有节点被移动，重新构建节点关系树
              if (anyNodeMoved) {
                const { nodeMap: updatedNodeMap } = buildNodeTree()

                // 第一次应用布局算法
                applyAutoLayout(true)

                // 二次布局，确保所有节点位置都已优化
                setTimeout(() => {
                  // 再次应用布局算法，处理可能的边缘情况
                  applyAutoLayout(true)
                }, 100)

                return // 提前返回，避免重复应用布局
              }
            }

            // 如果找到新节点，使用强制函数确保父节点与叔节点之间的间距增大
            if (newNodeId && newNodeParentId) {
              console.log(`对新节点 ${newNodeId} 的父节点 ${newNodeParentId} 应用强制间距调整`)

              // 获取父节点的子节点数量
              const parentInfo = nodeMap.get(newNodeParentId)
              const childrenCount = parentInfo ? parentInfo.children.length : 1

              // 使用强制调整函数处理父节点与叔叔节点之间的间距
              // 传递实际的子节点数量，确保间距计算正确

              // 多次调用强制调整函数，确保间距累积增加
              let moved = false;
              for (let i = 0; i < 5; i++) {
                // 每次使用更大的子节点数量，确保间距足够大
                const forcedChildCount = childrenCount * (i + 2);
                console.log(`处理新节点父节点 ${newNodeParentId}，第${i+1}次强制调整，使用子节点数量: ${forcedChildCount}`);

                const result = forceAdjustParentUncleSpacing(nodeMap, newNodeParentId, forcedChildCount);
                moved = moved || result;
              }

              console.log('新节点父节点间距调整完成，是否有节点被移动:', moved, '子节点数量:', childrenCount)

              // 如果有节点被移动，重新构建节点关系树
              if (moved) {
                const { nodeMap: updatedNodeMap } = buildNodeTree()

                // 第一次应用布局算法
                applyAutoLayout(true)

                // 二次布局，确保所有节点位置都已优化
                setTimeout(() => {
                  // 再次应用布局算法，处理可能的边缘情况
                  applyAutoLayout(true)
                }, 100)

                return // 提前返回，避免重复应用布局
              }
            }

            // 第一次应用布局算法
            applyAutoLayout()

            // 二次布局，确保所有节点位置都已优化
            setTimeout(() => {
              // 再次应用布局算法，处理可能的边缘情况
              applyAutoLayout()

              // 三次布局，确保对称性和平衡性
              setTimeout(() => {
                // 特别关注平衡子树，确保对称布局
                const { nodeMap, rootNodes } = buildNodeTree()

                // 从根节点开始平衡子树
                rootNodes.forEach(rootId => {
                  const balanceSubtree = (nodeId: string) => {
                    const nodeInfo = nodeMap.get(nodeId)
                    if (!nodeInfo || !nodeInfo.children.length) return

                    // 对子节点进行排序
                    const sortedChildren = [...nodeInfo.children].sort((a, b) => {
                      const indexA = nodeInfo.children.indexOf(a)
                      const indexB = nodeInfo.children.indexOf(b)
                      return indexA - indexB
                    })

                    // 递归处理子节点
                    sortedChildren.forEach(childId => {
                      balanceSubtree(childId)
                    })
                  }

                  balanceSubtree(rootId)
                })

                // 特别处理单个子节点的情况
                const processSingleChildNodes = () => {
                  // 创建一个处理过的节点集合，避免重复处理
                  const processedNodes = new Set<string>()

                  // 递归处理单子节点
                  const processSingleChild = (nodeId: string) => {
                    if (processedNodes.has(nodeId)) return
                    processedNodes.add(nodeId)

                    const nodeInfo = nodeMap.get(nodeId)
                    if (!nodeInfo) return

                    // 如果节点只有一个子节点
                    if (nodeInfo.children.length === 1) {
                      const childId = nodeInfo.children[0]
                      const childNode = getNodeById(childId)
                      const parentNode = getNodeById(nodeId)
                      if (!childNode || !parentNode) return

                      // 计算父节点中心点X坐标
                      const parentCenterX = parentNode.position.x + (parentNode.size?.width || 240) / 2
                      const childNodeWidth = childNode.size?.width || 240

                      // 确保子节点的中心点与父节点的中心点水平对齐
                      // 子节点X坐标 = 父节点中心点X坐标 - 子节点宽度/2 + 水平偏移量
                      const horizontalOffset = 350
                      const childX = parentCenterX - childNodeWidth / 2 + horizontalOffset

                      // 确保子节点与父节点在同一水平线上 - 严格对齐
                      nodeStore.moveNode(childId, {
                        x: childX,
                        y: parentNode.position.y
                      })

                      // 检查子节点是否有自己的子节点
                      const childInfo = nodeMap.get(childId)
                      if (childInfo && childInfo.children.length === 1) {
                        // 如果子节点也只有一个子节点，确保孙子节点也与子节点对齐
                        const grandchildId = childInfo.children[0]
                        const grandchildNode = getNodeById(grandchildId)
                        if (grandchildNode) {
                          // 计算子节点中心点X坐标
                          const childCenterX = childNode.position.x + (childNode.size?.width || 240) / 2
                          const grandchildWidth = grandchildNode.size?.width || 240

                          // 计算孙子节点应该的X坐标，使其中心与子节点中心水平对齐
                          // 然后添加水平偏移量，确保孙子节点在子节点右侧
                          const horizontalOffset = 350
                          const grandchildX = childCenterX - grandchildWidth / 2 + horizontalOffset

                          // 确保孙子节点的中心点与子节点的中心点水平对齐
                          nodeStore.moveNode(grandchildId, {
                            x: grandchildX,
                            y: childNode.position.y
                          })
                        }
                      }

                      // 递归处理子节点
                      processSingleChild(childId)
                    } else if (nodeInfo.children.length > 1) {
                      // 如果有多个子节点，先处理当前节点的子节点对齐
                      const parentNode = getNodeById(nodeId)
                      if (!parentNode) return

                      // 对于多个子节点，确保它们对称分布在父节点两侧
                      const childrenIds = nodeInfo.children
                      const childNodes = childrenIds.map(id => getNodeById(id)).filter(Boolean)

                      // 如果所有子节点都在同一列（X坐标接近），确保它们对称分布
                      if (childNodes.length > 0) {
                        const sameColumn = childNodes.every(node =>
                          node && Math.abs(node.position.x - (childNodes[0]?.position.x || 0)) < 10
                        )

                        if (sameColumn) {
                          // 定义布局常量
                          const NODE_HEIGHT = 90
                          const VERTICAL_SPACING = 180

                          // 计算子节点的总高度
                          const totalHeight = (childNodes.length - 1) * VERTICAL_SPACING + NODE_HEIGHT

                          // 计算父节点的中心点
                          const parentCenterY = parentNode.position.y + NODE_HEIGHT / 2

                          // 计算第一个子节点的Y坐标，使所有子节点对称分布在父节点两侧
                          const firstChildY = parentCenterY - totalHeight / 2

                          // 布局子节点
                          childNodes.forEach((childNode, index) => {
                            if (childNode) {
                              nodeStore.moveNode(childNode.id, {
                                x: childNode.position.x,
                                y: firstChildY + index * VERTICAL_SPACING
                              })
                            }
                          })
                        }
                      }

                      // 然后递归处理每个子节点
                      nodeInfo.children.forEach(childId => {
                        processSingleChild(childId)
                      })
                    }
                  }

                  // 从根节点开始处理
                  rootNodes.forEach(rootId => {
                    processSingleChild(rootId)
                  })
                }

                // 处理单个子节点的情况
                processSingleChildNodes()

                // 最后再次应用完整布局，确保所有优化都已应用
                applyAutoLayout()

                // 四次布局，最终确保连线不会错乱
                setTimeout(() => {
                  // 最后一次优化，特别关注连线路径
                  optimizeEdgePaths()

                  // 再次平衡子树
                  balanceSubtrees()

                  // 应用全局协调机制 - 原则5
                  // 确保所有节点都满足原则1-4，并处理节点位置变化的连锁反应
                  // 直接应用自动布局，而不是调用未定义的函数
                  applyAutoLayout(true) // 强制应用自动布局

                  // 最终检查所有节点，确保没有重叠
                  const allNodes = nodes.value
                  const NODE_HEIGHT = 90
                  const NODE_WIDTH = 240
                  const MIN_VERTICAL_GAP = 150 // 增加最小垂直间距，从120增加到150
                  const MIN_HORIZONTAL_GAP = 50 // 最小水平间距

                  // 按Y坐标排序节点，确保从上到下处理
                  const sortedNodes = [...allNodes].sort((a, b) => a.position.y - b.position.y)

                  // 多次迭代检查，确保所有重叠都被解决
                  for (let iteration = 0; iteration < 3; iteration++) {
                    let hasOverlap = false;

                    for (let i = 0; i < sortedNodes.length; i++) {
                      for (let j = i + 1; j < sortedNodes.length; j++) {
                        const nodeA = sortedNodes[i]
                        const nodeB = sortedNodes[j]

                        // 检查节点是否在同一列（X坐标接近）
                        const sameColumn = Math.abs(nodeA.position.x - nodeB.position.x) < NODE_WIDTH / 2

                        if (sameColumn) {
                          // 检查垂直距离
                          const verticalDistance = Math.abs(nodeA.position.y - nodeB.position.y)

                          // 如果距离太近，调整位置
                          if (verticalDistance < MIN_VERTICAL_GAP) {
                            // 向下移动节点B
                            nodeStore.moveNode(nodeB.id, {
                              x: nodeB.position.x,
                              y: nodeA.position.y + NODE_HEIGHT + MIN_VERTICAL_GAP
                            })
                            hasOverlap = true;
                          }
                        } else {
                          // 检查节点是否有水平和垂直重叠
                          const horizontalOverlap =
                            nodeA.position.x < nodeB.position.x + NODE_WIDTH &&
                            nodeA.position.x + NODE_WIDTH > nodeB.position.x;

                          const verticalOverlap =
                            nodeA.position.y < nodeB.position.y + NODE_HEIGHT &&
                            nodeA.position.y + NODE_HEIGHT > nodeB.position.y;

                          // 如果两个节点重叠
                          if (horizontalOverlap && verticalOverlap) {
                            // 计算水平和垂直方向的重叠程度
                            const horizontalOverlapAmount = Math.min(
                              nodeA.position.x + NODE_WIDTH - nodeB.position.x,
                              nodeB.position.x + NODE_WIDTH - nodeA.position.x
                            );

                            const verticalOverlapAmount = Math.min(
                              nodeA.position.y + NODE_HEIGHT - nodeB.position.y,
                              nodeB.position.y + NODE_HEIGHT - nodeA.position.y
                            );

                            // 选择移动方向（水平或垂直）
                            if (horizontalOverlapAmount < verticalOverlapAmount) {
                              // 水平移动节点B
                              nodeStore.moveNode(nodeB.id, {
                                x: nodeB.position.x + horizontalOverlapAmount + MIN_HORIZONTAL_GAP,
                                y: nodeB.position.y
                              });
                            } else {
                              // 垂直移动节点B
                              nodeStore.moveNode(nodeB.id, {
                                x: nodeB.position.x,
                                y: nodeB.position.y + verticalOverlapAmount + MIN_HORIZONTAL_GAP
                              });
                            }
                            hasOverlap = true;
                          }
                        }
                      }
                    }

                    // 如果没有重叠，提前结束迭代
                    if (!hasOverlap) break;

                    // 更新排序后的节点列表，反映新位置
                    sortedNodes.sort((a, b) => a.position.y - b.position.y);
                  }
                }, 200)
              }, 150)
            }, 100)
          }, 50)
        }
      }
    })

    // 监听节点删除，自动重新布局
    watch(() => nodes.value.length, (newCount, oldCount) => {
      if (newCount < oldCount && newCount > 0) {
        // 节点被删除时，重新启用自动布局
        autoLayoutEnabled.value = true

        // 节点被删除，重新布局
        setTimeout(() => {
          applyAutoLayout()
        }, 50) // 短暂延迟，确保状态已更新
      }
    })

    // 监听节点位置变化，完全禁用自动布局
    watch(() => nodes.value.map(node => ({ id: node.id, x: node.position.x, y: node.position.y })),
      (newPositions, oldPositions) => {
        // 检查是否有节点位置发生变化（用户手动拖动）
        if (newPositions.length === oldPositions.length && newPositions.length > 0) {
          const positionChanged = newPositions.some((newPos, index) => {
            const oldPos = oldPositions[index]
            return newPos.id === oldPos.id &&
                  (newPos.x !== oldPos.x || newPos.y !== oldPos.y)
          })

          // 如果位置变化是由用户手动拖动引起的，禁用自动布局
          if (positionChanged) {
            // 禁用自动布局，确保节点位置不会被自动调整
            autoLayoutEnabled.value = false

            // 不再调用任何自动布局或优化方法
            // 完全保留用户手动拖动的节点位置
          }
        }
      },
      { deep: true }
    )



    // 暴露forceAdjustParentUncleSpacing方法给外部使用
    const publicForceAdjustParentUncleSpacing = (parentId: string, childCount: number) => {
      console.log('外部调用强制调整父叔节点间距:', parentId, childCount);

      // 构建节点关系树
      const { nodeMap } = buildNodeTree();

      // 调用内部方法，强制调整父叔节点间距
      return forceAdjustParentUncleSpacing(nodeMap, parentId, childCount);
    };

    return {
      canvasRef,
      canvasScale,
      canvasPosition,
      canvasDimensions,
      nodes,
      nodesWithDragState,
      edges,
      selectedNodeId,
      selectedEdgeId,
      isConnecting,
      getNodeById,
      onMouseDown,
      onMouseMove,
      onMouseUp,
      onWheel,
      onCanvasContextMenu,
      selectNode,
      moveNode,
      handleAddRelation,
      handleShowAttribution,
      handleDragStart,
      handleDragEnd,
      handleCopyNode,
      handleDeleteNode,
      selectEdge,
      onSymbolClick,
      onConnectorClick,
      startConnecting,
      finishConnecting,
      getTempEdgePath,
      applyAutoLayout,
      autoLayoutEnabled,
      draggedNodeId,
      showThumbnail,
      containerSize,
      buildNodeTree,
      createEmptyNode,
      onEmptyCanvasRightClick,
      showEmptyCanvasMenu,
      emptyCanvasMenuPosition,
      rightClickPosition,
      closeEmptyCanvasMenu,
      handleAddNodeFromMenu,
      handlePasteFromMenu,
      handleAutoLayoutFromMenu,
      forceAdjustParentUncleSpacing: publicForceAdjustParentUncleSpacing
    }
  }
})
</script>

<style lang="scss" scoped>
.canvas {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
  background-color: #fff;
  background-image:
    linear-gradient(rgba(0, 0, 0, 0.05) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 0, 0, 0.05) 1px, transparent 1px);
  background-size: 20px 20px;

  .empty-canvas-background-hint {
    position: absolute;
    bottom: 30px;
    left: 0;
    right: 0;
    display: flex;
    justify-content: center;
    z-index: 50;
    pointer-events: none;

    .hint-text {
      background-color: rgba(255, 255, 255, 0.9);
      padding: 8px 16px;
      border-radius: 20px;
      color: #666;
      font-size: 13px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      animation: fadeInUp 0.5s ease-out forwards;
      animation-delay: 1s;
      opacity: 0;
      transform: translateY(20px);
    }
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .canvas-content {
    position: absolute;
    transform-origin: top left;
    /* 移除过渡效果，使拖拽更加即时 */
    transition: none;
    /* 启用GPU加速 */
    transform: translate3d(0, 0, 0);
    will-change: transform;
    /* 减少闪烁 */
    backface-visibility: hidden;
  }

  .temp-edge {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
  }

  .empty-canvas-node-prompt {
    position: absolute;
    top: 50%;
    left: 30%; /* 将位置从50%（居中）调整为30%（偏左） */
    transform: translate(-50%, -50%);
    width: 240px;
    background-color: #fff;
    border-radius: 4px;
    border-left: 4px solid #1890ff;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    animation: float 3s ease-in-out infinite;
    z-index: 200; /* 增加z-index确保在最上层 */

    &:hover {
      box-shadow: 0 4px 16px rgba(24, 144, 255, 0.25);
      transform: translate(-50%, -50%) scale(1.02);
    }

    .node-header {
      padding: 10px 12px;
      border-bottom: 1px solid #f0f0f0;
      background-color: #fafafa;

      .header-text {
        font-size: 14px;
        color: #333;
        font-weight: 500;
      }
    }

    .node-content {
      padding: 20px;
      display: flex;
      flex-direction: column;
      align-items: center;

      .add-icon-container {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background-color: rgba(24, 144, 255, 0.1);
        display: flex;
        justify-content: center;
        align-items: center;
        margin-bottom: 16px;
        transition: all 0.3s ease;

        .add-icon {
          font-size: 36px;
          color: #1890ff;
          line-height: 1;
          font-weight: 300;
        }
      }

      .prompt-text {
        color: #666;
        font-size: 14px;
        text-align: center;
      }
    }
  }

  @keyframes float {
    0% {
      transform: translate(-50%, -50%) translateY(0px);
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }
    50% {
      transform: translate(-50%, -50%) translateY(-10px);
      box-shadow: 0 15px 20px rgba(0, 0, 0, 0.05);
    }
    100% {
      transform: translate(-50%, -50%) translateY(0px);
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }
  }



  .thumbnail-toggle-btn {
    position: absolute;
    bottom: 20px;
    right: 250px;
    width: 30px;
    height: 30px;
    background-color: rgba(255, 255, 255, 0.95);
    color: #333;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 16px;
    z-index: 100;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 3px 12px rgba(0, 0, 0, 0.15), 0 0 1px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;

    .toggle-icon {
      color: rgba(0, 0, 0, 0.6);
    }

    &:hover {
      background-color: rgba(255, 255, 255, 1);
      box-shadow: 0 4px 14px rgba(0, 0, 0, 0.2), 0 0 1px rgba(0, 0, 0, 0.1);
    }
  }
}
</style>