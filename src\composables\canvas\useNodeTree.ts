import { computed } from 'vue'
import type { Node as NodeType } from '../../types'

// Node layout constants
const NODE_HEIGHT = 90
const NODE_WIDTH = 240
const TERMINAL_NODE_SPACING = 180
const VERTICAL_SPACING = 180
const FUTURE_NODE_SPACE_FACTOR = 1.2

interface NodeInfo {
  node: NodeType;
  children: string[];
  parent: string | null;
  depth: number;
  siblingIndex: number;
  hasSingleChild: boolean;
  totalSiblings: number;
  parentGroup: string;
  siblingNodes: string[];
  isTerminalNode: boolean;
  childrenCount: number;
  uncleNodes: string[];
  cousinNodes: string[];
  requiredSpace: number;
  hasNewChildren: boolean;
  lastChildrenCount: number;
}

export function useNodeTree(nodes: any, edges: any, getNodeById: (id: string) => NodeType | undefined) {
  // Build the node relationship tree
  const buildNodeTree = () => {
    const nodeMap = new Map<string, NodeInfo>()
    
    // Initialize node mapping
    nodes.value.forEach((node: NodeType) => {
      nodeMap.set(node.id, {
        node,
        children: [],
        parent: null,
        depth: 0,
        siblingIndex: 0,
        hasSingleChild: false,
        totalSiblings: 0,
        parentGroup: '',
        siblingNodes: [],
        isTerminalNode: true, // Default all nodes as terminal
        childrenCount: 0,
        uncleNodes: [],
        cousinNodes: [],
        requiredSpace: NODE_HEIGHT,
        hasNewChildren: false,
        lastChildrenCount: 0
      })
    })
    
    // Build parent-child relationships
    edges.value.forEach((edge: any) => {
      const sourceInfo = nodeMap.get(edge.source)
      const targetInfo = nodeMap.get(edge.target)
      
      if (sourceInfo && targetInfo) {
        sourceInfo.children.push(edge.target)
        sourceInfo.childrenCount = sourceInfo.children.length
        sourceInfo.isTerminalNode = false // Has children, not a terminal node
        targetInfo.parent = edge.source
        
        // Mark as having new children
        sourceInfo.hasNewChildren = true
        
        // Save current children count for next comparison
        sourceInfo.lastChildrenCount = sourceInfo.childrenCount
      }
    })
    
    // Find root nodes
    const rootNodes: string[] = []
    nodeMap.forEach((info, nodeId) => {
      if (!info.parent) {
        rootNodes.push(nodeId)
      }
    })
    
    // Calculate node depth
    const calculateNodeDepth = (nodeId: string, depth = 0) => {
      const nodeInfo = nodeMap.get(nodeId)
      if (!nodeInfo) return
      
      nodeInfo.depth = depth
      
      // Recursively calculate child depths
      nodeInfo.children.forEach(childId => {
        calculateNodeDepth(childId, depth + 1)
      })
    }
    
    // Calculate from root nodes
    rootNodes.forEach(rootId => {
      calculateNodeDepth(rootId)
    })
    
    // Calculate uncle and cousin relationships
    nodeMap.forEach((info, nodeId) => {
      if (info.parent) {
        const parentInfo = nodeMap.get(info.parent)
        if (parentInfo && parentInfo.parent) {
          const grandparentInfo = nodeMap.get(parentInfo.parent)
          if (grandparentInfo) {
            // Get all uncle nodes (parent's siblings)
            info.uncleNodes = grandparentInfo.children.filter(id => id !== info.parent)
            
            // Get all cousin nodes (uncle's children)
            info.uncleNodes.forEach(uncleId => {
              const uncleInfo = nodeMap.get(uncleId)
              if (uncleInfo) {
                info.cousinNodes.push(...uncleInfo.children)
              }
            })
          }
        }
      }
    })
    
    // Calculate required space for each node
    const calculateRequiredSpace = (nodeId: string): number => {
      const nodeInfo = nodeMap.get(nodeId)
      if (!nodeInfo) return NODE_HEIGHT
      
      // Terminal nodes only need their own height
      if (nodeInfo.isTerminalNode) {
        return NODE_HEIGHT
      }
      
      // If only one child
      if (nodeInfo.children.length === 1) {
        const childId = nodeInfo.children[0]
        const childSpace = calculateRequiredSpace(childId)
        return Math.max(NODE_HEIGHT, childSpace)
      }
      
      // If multiple children, calculate total space needed
      let totalChildrenSpace = 0
      
      // Check if all children are terminal nodes
      const allTerminalNodes = nodeInfo.children.every(childId => {
        const childInfo = nodeMap.get(childId)
        return childInfo && childInfo.isTerminalNode
      })
      
      // Use appropriate spacing
      const spacing = allTerminalNodes ? TERMINAL_NODE_SPACING : VERTICAL_SPACING
      
      // Calculate total space needed by children
      nodeInfo.children.forEach(childId => {
        const childSpace = calculateRequiredSpace(childId)
        totalChildrenSpace += childSpace
      })
      
      // Add spacing between children
      totalChildrenSpace += (nodeInfo.children.length - 1) * spacing
      
      // Reserve space for future nodes
      const requiredSpace = Math.max(NODE_HEIGHT, totalChildrenSpace * FUTURE_NODE_SPACE_FACTOR)
      
      // Update node's required space
      nodeInfo.requiredSpace = requiredSpace
      
      return requiredSpace
    }
    
    // Calculate required space from root nodes
    rootNodes.forEach(rootId => {
      calculateRequiredSpace(rootId)
    })
    
    return { nodeMap, rootNodes }
  }
  
  // Optimize node spacing to prevent overlaps
  const optimizeNodeSpacing = () => {
    if (nodes.value.length <= 1) return false // No need to process if 0 or 1 node
    
    // Calculate node bounds with safety margin
    const getNodeBounds = (node: NodeType, safetyMargin = 20) => {
      return {
        left: node.position.x - safetyMargin,
        right: node.position.x + (node.size?.width || NODE_WIDTH) + safetyMargin,
        top: node.position.y - safetyMargin,
        bottom: node.position.y + (node.size?.height || NODE_HEIGHT) + safetyMargin
      }
    }
    
    // Check if two nodes overlap
    const checkOverlap = (node1: NodeType, node2: NodeType) => {
      const bounds1 = getNodeBounds(node1)
      const bounds2 = getNodeBounds(node2)
      
      return !(bounds1.right < bounds2.left ||
               bounds1.left > bounds2.right ||
               bounds1.bottom < bounds2.top ||
               bounds1.top > bounds2.bottom)
    }
    
    // Check if nodes are in same column (X coordinates close)
    const isInSameColumn = (node1: NodeType, node2: NodeType) => {
      return Math.abs(node1.position.x - node2.position.x) < (node1.size?.width || NODE_WIDTH) / 2
    }
    
    // Sort nodes by Y coordinate
    const sortedNodes = [...nodes.value].sort((a: NodeType, b: NodeType) => a.position.y - b.position.y)
    let nodesAdjusted = false
    
    // Multiple iterations to resolve all overlaps
    for (let iteration = 0; iteration < 3; iteration++) {
      let adjustmentsInThisIteration = 0
      
      for (let i = 0; i < sortedNodes.length; i++) {
        for (let j = i + 1; j < sortedNodes.length; j++) {
          const nodeA = sortedNodes[i]
          const nodeB = sortedNodes[j]
          
          // Check for overlap
          if (checkOverlap(nodeA, nodeB)) {
            if (isInSameColumn(nodeA, nodeB)) {
              // Vertical overlap - move nodeB down
              const minVerticalGap = 150
              const newY = nodeA.position.y + (nodeA.size?.height || NODE_HEIGHT) + minVerticalGap
              
              // Move nodeB
              sortedNodes[j].position.y = newY
              adjustmentsInThisIteration++
              nodesAdjusted = true
            } else {
              // Horizontal overlap - move nodeB right
              const minHorizontalGap = 80
              const newX = nodeA.position.x + (nodeA.size?.width || NODE_WIDTH) + minHorizontalGap
              
              // Move nodeB
              sortedNodes[j].position.x = newX
              adjustmentsInThisIteration++
              nodesAdjusted = true
            }
          }
        }
      }
      
      // Exit if no adjustments in this iteration
      if (adjustmentsInThisIteration === 0) break
      
      // Re-sort nodes for next iteration
      sortedNodes.sort((a: NodeType, b: NodeType) => a.position.y - b.position.y)
    }
    
    // Ensure parent-child relationships have correct positions
    edges.value.forEach((edge: any) => {
      const sourceNode = getNodeById(edge.source)
      const targetNode = getNodeById(edge.target)
      
      if (!sourceNode || !targetNode) return
      
      // If target node (child) is to the left of source node (parent), move it right
      if (targetNode.position.x <= sourceNode.position.x + (sourceNode.size?.width || NODE_WIDTH)) {
        const minHorizontalGap = 100 // Minimum parent-child gap
        const newX = sourceNode.position.x + (sourceNode.size?.width || NODE_WIDTH) + minHorizontalGap
        
        // Move target node
        targetNode.position.x = newX
        nodesAdjusted = true
      }
    })
    
    return nodesAdjusted
  }
  
  return {
    buildNodeTree,
    optimizeNodeSpacing
  }
}
