import { defineStore } from 'pinia'
import type { NodeState, Node, Position } from '../types'
import { v4 as uuidv4 } from 'uuid'

export const useNodeStore = defineStore('node', {
  state: (): NodeState => ({
    nodes: [],
    selectedNodeId: null
  }),
  
  getters: {
    getNodeById: (state) => (id: string) => {
      return state.nodes.find(node => node.id === id) || null
    },
    
    selectedNode: (state): Node | null => {
      if (!state.selectedNodeId) return null
      return state.nodes.find(node => node.id === state.selectedNodeId) || null
    }
  },
  
  actions: {
    // 添加节点
    addNode(node: Node) {
      // 确保节点有唯一ID
      const newNode = {
        ...node,
        id: node.id || uuidv4()
      }
      
      this.nodes.push(newNode)
      return newNode.id
    },
    
    // 更新节点
    updateNode(id: string, updates: Partial<Node>) {
      const index = this.nodes.findIndex(node => node.id === id)
      if (index !== -1) {
        this.nodes[index] = { ...this.nodes[index], ...updates }
      }
    },
    
    // 移动节点
    moveNode(id: string, position: Position) {
      const index = this.nodes.findIndex(node => node.id === id)
      if (index !== -1) {
        this.nodes[index].position = position
      }
    },
    
    // 删除节点
    removeNode(id: string) {
      this.nodes = this.nodes.filter(node => node.id !== id)
      if (this.selectedNodeId === id) {
        this.selectedNodeId = null
      }
    },
    
    // 选择节点
    selectNode(id: string | null) {
      this.selectedNodeId = id
    },
    
    // 清空所有节点
    clearNodes() {
      this.nodes = []
      this.selectedNodeId = null
    },
    
    // 批量添加节点
    addNodes(nodes: Node[]) {
      const newNodes = nodes.map(node => ({
        ...node,
        id: node.id || uuidv4()
      }))
      
      this.nodes.push(...newNodes)
    }
  }
})