{"name": "visual-config-system", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "start": "vite", "start:prod": "vite --mode production", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"pinia": "^3.0.2", "sass": "^1.87.0", "uuid": "^11.1.0", "vue": "^3.5.13", "vue-router": "^4.5.1"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.2", "@vue/tsconfig": "^0.7.0", "typescript": "~5.7.2", "vite": "^6.3.1", "vue-tsc": "^2.2.8"}}