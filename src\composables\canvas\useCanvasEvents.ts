import { ref } from 'vue'
import type { Position } from '../../types'
import { useNodeStore } from '../../stores/node'
import { useEdgeStore } from '../../stores/edge'

export function useCanvasEvents(canvasRef: any, canvasPosition: any, canvasScale: any, readonly = false, emit: Function) {
  const nodeStore = useNodeStore()
  const edgeStore = useEdgeStore()
  
  // Empty canvas context menu state
  const showEmptyCanvasMenu = ref(false)
  const emptyCanvasMenuPosition = ref<Position>({ x: 0, y: 0 })
  const rightClickPosition = ref<Position>({ x: 0, y: 0 })
  
  // Handle canvas mouse down event
  const onMouseDown = (event: MouseEvent, startCanvasDrag: Function) => {
    // If clicking on canvas background (not a node), start dragging canvas
    if (event.target === canvasRef.value || event.target === canvasRef.value?.firstElementChild) {
      // Send close popup event
      if (!readonly) {
        emit('close-relation-selector')
      }
      
      startCanvasDrag(event)
      
      // In non-readonly mode, deselect nodes and edges
      if (!readonly) {
        nodeStore.selectNode(null)
        edgeStore.selectEdge(null)
      }
      
      // Prevent default behavior
      event.preventDefault()
    }
  }
  
  // Handle canvas context menu
  const onCanvasContextMenu = (event: MouseEvent) => {
    // If in readonly mode, don't handle
    if (readonly) return
    
    // Prevent default context menu
    event.preventDefault()
    
    // Show empty canvas context menu if no nodes
    const nodes = nodeStore.nodes
    if (nodes.length === 0) {
      onEmptyCanvasRightClick(event)
    }
  }
  
  // Handle right click on empty canvas
  const onEmptyCanvasRightClick = (event: MouseEvent) => {
    if (readonly) return
    
    // Show context menu
    if (canvasRef.value) {
      // Save mouse position in canvas coordinates
      const canvasRect = canvasRef.value.getBoundingClientRect()
      rightClickPosition.value = {
        x: (event.clientX - canvasRect.left - canvasPosition.value.x) / canvasScale.value,
        y: (event.clientY - canvasRect.top - canvasPosition.value.y) / canvasScale.value
      }
      
      // Set menu position on screen
      emptyCanvasMenuPosition.value = {
        x: event.clientX,
        y: event.clientY
      }
      
      // Show menu
      showEmptyCanvasMenu.value = true
      
      // Add click event listener to close menu
      document.addEventListener('click', closeEmptyCanvasMenu)
    }
  }
  
  // Close empty canvas context menu
  const closeEmptyCanvasMenu = () => {
    showEmptyCanvasMenu.value = false
    document.removeEventListener('click', closeEmptyCanvasMenu)
  }
  
  // Handle adding node from menu
  const handleAddNodeFromMenu = () => {
    // Send create empty node event with right click position
    emit('create-empty-node', rightClickPosition.value)
    closeEmptyCanvasMenu()
  }
  
  // Handle paste from menu
  const handlePasteFromMenu = () => {
    // Implement paste functionality or send event to parent
    console.log('Paste at position:', rightClickPosition.value)
    closeEmptyCanvasMenu()
  }
  
  // Handle auto layout from menu
  const handleAutoLayoutFromMenu = (applyAutoLayout: Function) => {
    // Call auto layout function
    applyAutoLayout(true)
    closeEmptyCanvasMenu()
  }
  
  // Create empty node
  const createEmptyNode = () => {
    if (readonly) return
    
    // Calculate node position - use canvas center
    const position: Position = {
      x: (canvasRef.value?.clientWidth / 2 - canvasPosition.value.x) / canvasScale.value,
      y: (canvasRef.value?.clientHeight / 2 - canvasPosition.value.y) / canvasScale.value
    }
    
    // Send create empty node event
    emit('create-empty-node', position)
    
    // Close context menu if open
    if (showEmptyCanvasMenu.value) {
      closeEmptyCanvasMenu()
    }
  }
  
  return {
    showEmptyCanvasMenu,
    emptyCanvasMenuPosition,
    rightClickPosition,
    onMouseDown,
    onCanvasContextMenu,
    onEmptyCanvasRightClick,
    closeEmptyCanvasMenu,
    handleAddNodeFromMenu,
    handlePasteFromMenu,
    handleAutoLayoutFromMenu,
    createEmptyNode
  }
}
