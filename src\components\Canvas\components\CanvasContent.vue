<template>
  <div
    class="canvas-content"
    :style="{
      transform: `translate(${canvasPosition.x}px, ${canvasPosition.y}px) scale(${canvasScale})`,
      width: `${canvasDimensions.width}px`,
      height: `${canvasDimensions.height}px`,
      minWidth: '20000px',
      minHeight: '20000px'
    }"
  >
    <!-- 渲染所有连线 -->
    <Edge
      v-for="edge in edges"
      :key="edge.id"
      :edge="edge"
      :source-node="getNodeById(edge.source)"
      :target-node="getNodeById(edge.target)"
      :selected="edge.id === selectedEdgeId"
      :readonly="readonly"
      :highlighted="draggedNodeId && (edge.source === draggedNodeId || edge.target === draggedNodeId)"
      @select="selectEdge"
      @symbol-click="onSymbolClick"
      @connector-click="onConnectorClick"
    />

    <!-- 渲染所有节点 -->
    <Node
      v-for="node in nodes"
      :key="node.id"
      :node="node"
      :selected="node.id === selectedNodeId"
      :readonly="readonly"
      @select="selectNode"
      @move="moveNode"
      @connect-start="startConnecting"
      @connect-end="finishConnecting"
      @add-relation="handleAddRelation"
      @show-attribution="handleShowAttribution"
      @drag-start="handleDragStart"
      @drag-end="handleDragEnd"
      @copy-node="handleCopyNode"
      @delete-node="handleDeleteNode"
    />

    <!-- 绘制连线过程中的临时线 -->
    <TempEdge 
      v-if="isConnecting" 
      :path="getTempEdgePath()" 
    />
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue'
import Node from '../../Node/Node.vue'
import Edge from '../../Edge/Edge.vue'
import TempEdge from './TempEdge.vue'

export default defineComponent({
  name: 'CanvasContent',
  components: {
    Node,
    Edge,
    TempEdge
  },
  props: {
    canvasPosition: {
      type: Object,
      required: true
    },
    canvasScale: {
      type: Number,
      required: true
    },
    canvasDimensions: {
      type: Object,
      required: true
    },
    nodes: {
      type: Array,
      required: true
    },
    edges: {
      type: Array,
      required: true
    },
    selectedNodeId: {
      type: String,
      default: null
    },
    selectedEdgeId: {
      type: String,
      default: null
    },
    isConnecting: {
      type: Boolean,
      default: false
    },
    readonly: {
      type: Boolean,
      default: false
    },
    draggedNodeId: {
      type: String,
      default: null
    }
  },
  setup(props, { emit }) {
    // Pass all events up to parent
    const getNodeById = (id: string) => {
      return props.nodes.find(node => node.id === id)
    }

    const selectNode = (id: string) => {
      emit('select-node', id)
    }

    const moveNode = (id: string, position: any) => {
      emit('move-node', id, position)
    }

    const selectEdge = (id: string) => {
      emit('select-edge', id)
    }

    const startConnecting = (nodeId: string, position: any) => {
      emit('connect-start', nodeId, position)
    }

    const finishConnecting = (targetId: string) => {
      emit('connect-end', targetId)
    }

    const handleAddRelation = (nodeId: string, relation: any) => {
      emit('add-relation', nodeId, relation)
    }

    const handleShowAttribution = (nodeId: string) => {
      emit('show-attribution', nodeId)
    }

    const handleDragStart = (nodeId: string) => {
      emit('drag-start', nodeId)
    }

    const handleDragEnd = (nodeId: string) => {
      emit('drag-end', nodeId)
    }

    const handleCopyNode = (nodeId: string) => {
      emit('copy-node', nodeId)
    }

    const handleDeleteNode = (nodeId: string) => {
      emit('delete-node', nodeId)
    }

    const onSymbolClick = (edgeId: string, symbolInfo?: any) => {
      emit('symbol-click', edgeId, symbolInfo)
    }

    const onConnectorClick = (edgeId: string, position: any) => {
      emit('connector-click', edgeId, position)
    }

    const getTempEdgePath = () => {
      emit('get-temp-edge-path')
      return props.tempEdgePath
    }

    return {
      getNodeById,
      selectNode,
      moveNode,
      selectEdge,
      startConnecting,
      finishConnecting,
      handleAddRelation,
      handleShowAttribution,
      handleDragStart,
      handleDragEnd,
      handleCopyNode,
      handleDeleteNode,
      onSymbolClick,
      onConnectorClick,
      getTempEdgePath
    }
  }
})
</script>

<style lang="scss" scoped>
.canvas-content {
  position: absolute;
  transform-origin: top left;
  /* 移除过渡效果，使拖拽更加即时 */
  transition: none;
  /* 启用GPU加速但不使用translate3d，因为它可能导致大型SVG渲染问题 */
  transform: translate(0, 0);
  will-change: transform;
  /* 减少闪烁 */
  backface-visibility: hidden;
  /* 确保超出边界的内容不被裁剪 */
  overflow: visible !important;
  /* 确保画布内容足够大，包含所有可能的节点 */
  min-width: 20000px !important;
  min-height: 20000px !important;
}

/* 确保Edge组件的SVG元素不被裁剪 */
:deep(.edge) {
  overflow: visible !important;
  z-index: 10;
}

/* 确保所有SVG路径元素都能完整显示 */
:deep(svg), :deep(path) {
  overflow: visible !important;
}
</style>
