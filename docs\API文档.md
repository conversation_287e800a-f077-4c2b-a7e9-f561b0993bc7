# 可视化配置系统API文档

## 概述

可视化配置系统提供了一套完整的API，用于与系统进行交互、获取数据和控制系统行为。本文档详细介绍了系统的API接口、参数和返回值。

## 数据模型

### 节点（Node）

```typescript
interface Node {
  id: string;              // 节点唯一标识符
  type: string;            // 节点类型
  position: {              // 节点位置
    x: number;
    y: number;
  };
  size?: {                 // 节点尺寸（可选）
    width: number;
    height: number;
  };
  data: {                  // 节点数据
    label: string;         // 节点标签
    value: string;         // 节点值
    change: string;        // 变化趋势
    period: string;        // 统计周期
    color?: string;        // 节点颜色（可选）
    [key: string]: any;    // 其他自定义属性
  };
  [key: string]: any;      // 其他自定义属性
}
```

### 连线（Edge）

```typescript
interface Edge {
  id: string;              // 连线唯一标识符
  source: string;          // 源节点ID
  target: string;          // 目标节点ID
  label?: string;          // 连线标签（可选）
  type?: string;           // 连线类型（可选）
  color?: string;          // 连线颜色（可选）
  lineStyle?: string;      // 线条样式（可选）
  [key: string]: any;      // 其他自定义属性
}
```

### 画布（Canvas）

```typescript
interface Canvas {
  position: {              // 画布位置
    x: number;
    y: number;
  };
  scale: number;           // 画布缩放比例
  dimensions: {            // 画布尺寸
    width: number;
    height: number;
  };
}
```

## API接口

### 节点管理

#### 获取所有节点

```typescript
function getNodes(): Node[]
```

**描述**：获取画布中的所有节点。

**参数**：无

**返回值**：节点数组

**示例**：
```javascript
const nodes = getNodes();
console.log(nodes);
```

#### 获取指定节点

```typescript
function getNodeById(id: string): Node | null
```

**描述**：根据ID获取指定节点。

**参数**：
- `id`：节点ID

**返回值**：节点对象，如果不存在则返回null

**示例**：
```javascript
const node = getNodeById('node-1');
if (node) {
  console.log(node.data.label);
}
```

#### 添加节点

```typescript
function addNode(node: Node): string
```

**描述**：添加新节点到画布。

**参数**：
- `node`：节点对象

**返回值**：新节点的ID

**示例**：
```javascript
const newNodeId = addNode({
  type: 'default',
  position: { x: 100, y: 100 },
  data: {
    label: '新节点',
    value: '1000',
    change: '+10%',
    period: '同比上涨'
  }
});
console.log('新节点ID:', newNodeId);
```

#### 更新节点

```typescript
function updateNode(id: string, updates: Partial<Node>): void
```

**描述**：更新指定节点的属性。

**参数**：
- `id`：节点ID
- `updates`：要更新的属性

**返回值**：无

**示例**：
```javascript
updateNode('node-1', {
  data: {
    label: '更新后的节点',
    value: '2000'
  }
});
```

#### 移动节点

```typescript
function moveNode(id: string, position: { x: number, y: number }): void
```

**描述**：移动指定节点到新位置。

**参数**：
- `id`：节点ID
- `position`：新位置坐标

**返回值**：无

**示例**：
```javascript
moveNode('node-1', { x: 200, y: 300 });
```

#### 删除节点

```typescript
function removeNode(id: string): void
```

**描述**：删除指定节点。

**参数**：
- `id`：节点ID

**返回值**：无

**示例**：
```javascript
removeNode('node-1');
```

#### 选择节点

```typescript
function selectNode(id: string | null): void
```

**描述**：选择指定节点，传入null则取消选择。

**参数**：
- `id`：节点ID或null

**返回值**：无

**示例**：
```javascript
// 选择节点
selectNode('node-1');

// 取消选择
selectNode(null);
```

### 连线管理

#### 获取所有连线

```typescript
function getEdges(): Edge[]
```

**描述**：获取画布中的所有连线。

**参数**：无

**返回值**：连线数组

**示例**：
```javascript
const edges = getEdges();
console.log(edges);
```

#### 获取指定连线

```typescript
function getEdgeById(id: string): Edge | null
```

**描述**：根据ID获取指定连线。

**参数**：
- `id`：连线ID

**返回值**：连线对象，如果不存在则返回null

**示例**：
```javascript
const edge = getEdgeById('edge-1');
if (edge) {
  console.log(edge.source, '->', edge.target);
}
```

#### 获取节点的出边

```typescript
function getEdgesBySource(sourceId: string): Edge[]
```

**描述**：获取指定节点的所有出边（以该节点为源的连线）。

**参数**：
- `sourceId`：源节点ID

**返回值**：连线数组

**示例**：
```javascript
const outgoingEdges = getEdgesBySource('node-1');
console.log('出边数量:', outgoingEdges.length);
```

#### 获取节点的入边

```typescript
function getEdgesByTarget(targetId: string): Edge[]
```

**描述**：获取指定节点的所有入边（以该节点为目标的连线）。

**参数**：
- `targetId`：目标节点ID

**返回值**：连线数组

**示例**：
```javascript
const incomingEdges = getEdgesByTarget('node-1');
console.log('入边数量:', incomingEdges.length);
```

#### 添加连线

```typescript
function addEdge(edge: Edge): string
```

**描述**：添加新连线到画布。

**参数**：
- `edge`：连线对象

**返回值**：新连线的ID

**示例**：
```javascript
const newEdgeId = addEdge({
  source: 'node-1',
  target: 'node-2',
  type: 'add',
  label: '相加关系'
});
console.log('新连线ID:', newEdgeId);
```

#### 更新连线

```typescript
function updateEdge(id: string, updates: Partial<Edge>): void
```

**描述**：更新指定连线的属性。

**参数**：
- `id`：连线ID
- `updates`：要更新的属性

**返回值**：无

**示例**：
```javascript
updateEdge('edge-1', {
  type: 'multiply',
  label: '相乘关系'
});
```

#### 删除连线

```typescript
function removeEdge(id: string): void
```

**描述**：删除指定连线。

**参数**：
- `id`：连线ID

**返回值**：无

**示例**：
```javascript
removeEdge('edge-1');
```

#### 选择连线

```typescript
function selectEdge(id: string | null): void
```

**描述**：选择指定连线，传入null则取消选择。

**参数**：
- `id`：连线ID或null

**返回值**：无

**示例**：
```javascript
// 选择连线
selectEdge('edge-1');

// 取消选择
selectEdge(null);
```

### 画布操作

#### 获取画布状态

```typescript
function getCanvasState(): Canvas
```

**描述**：获取当前画布的状态。

**参数**：无

**返回值**：画布状态对象

**示例**：
```javascript
const canvas = getCanvasState();
console.log('缩放比例:', canvas.scale);
```

#### 设置画布位置

```typescript
function setCanvasPosition(position: { x: number, y: number }): void
```

**描述**：设置画布的位置。

**参数**：
- `position`：新位置坐标

**返回值**：无

**示例**：
```javascript
setCanvasPosition({ x: 0, y: 0 });
```

#### 设置画布缩放

```typescript
function setCanvasScale(scale: number): void
```

**描述**：设置画布的缩放比例。

**参数**：
- `scale`：缩放比例（0.1-2.0）

**返回值**：无

**示例**：
```javascript
setCanvasScale(1.5);
```

#### 画布缩放

```typescript
function zoomCanvas(delta: number, center: { x: number, y: number }): void
```

**描述**：以指定中心点缩放画布。

**参数**：
- `delta`：缩放增量
- `center`：缩放中心点坐标

**返回值**：无

**示例**：
```javascript
zoomCanvas(0.1, { x: 500, y: 300 });
```

#### 画布平移

```typescript
function panCanvas(deltaX: number, deltaY: number): void
```

**描述**：平移画布。

**参数**：
- `deltaX`：水平方向移动距离
- `deltaY`：垂直方向移动距离

**返回值**：无

**示例**：
```javascript
panCanvas(100, 50);
```

#### 应用自动布局

```typescript
function applyAutoLayout(forceLayout: boolean = false): void
```

**描述**：应用自动布局算法，调整节点位置。

**参数**：
- `forceLayout`：是否强制应用布局，即使自动布局功能已禁用

**返回值**：无

**示例**：
```javascript
// 应用自动布局
applyAutoLayout();

// 强制应用自动布局
applyAutoLayout(true);
```

### 配置管理

#### 保存配置

```typescript
function saveConfig(name: string): string
```

**描述**：保存当前配置。

**参数**：
- `name`：配置名称

**返回值**：配置ID

**示例**：
```javascript
const configId = saveConfig('我的配置');
console.log('配置已保存，ID:', configId);
```

#### 加载配置

```typescript
function loadConfig(id: string): void
```

**描述**：加载指定配置。

**参数**：
- `id`：配置ID

**返回值**：无

**示例**：
```javascript
loadConfig('config-1');
```

#### 获取配置列表

```typescript
function getConfigList(): { id: string, name: string, createTime: string }[]
```

**描述**：获取所有保存的配置列表。

**参数**：无

**返回值**：配置信息数组

**示例**：
```javascript
const configs = getConfigList();
configs.forEach(config => {
  console.log(`${config.name} (${config.id}) - ${config.createTime}`);
});
```

## 事件监听

系统提供了一系列事件，可以通过监听这些事件来响应用户操作和系统状态变化。

### 节点事件

#### 节点选择事件

```typescript
function onNodeSelect(callback: (nodeId: string | null) => void): () => void
```

**描述**：监听节点选择事件。

**参数**：
- `callback`：回调函数，接收选中的节点ID或null（取消选择）

**返回值**：取消监听的函数

**示例**：
```javascript
const unsubscribe = onNodeSelect(nodeId => {
  if (nodeId) {
    console.log('节点已选中:', nodeId);
  } else {
    console.log('已取消选择节点');
  }
});

// 取消监听
unsubscribe();
```

#### 节点添加事件

```typescript
function onNodeAdd(callback: (node: Node) => void): () => void
```

**描述**：监听节点添加事件。

**参数**：
- `callback`：回调函数，接收添加的节点对象

**返回值**：取消监听的函数

**示例**：
```javascript
const unsubscribe = onNodeAdd(node => {
  console.log('节点已添加:', node.id);
});
```

#### 节点更新事件

```typescript
function onNodeUpdate(callback: (nodeId: string, updates: Partial<Node>) => void): () => void
```

**描述**：监听节点更新事件。

**参数**：
- `callback`：回调函数，接收节点ID和更新的属性

**返回值**：取消监听的函数

**示例**：
```javascript
const unsubscribe = onNodeUpdate((nodeId, updates) => {
  console.log('节点已更新:', nodeId, updates);
});
```

#### 节点移动事件

```typescript
function onNodeMove(callback: (nodeId: string, position: { x: number, y: number }) => void): () => void
```

**描述**：监听节点移动事件。

**参数**：
- `callback`：回调函数，接收节点ID和新位置

**返回值**：取消监听的函数

**示例**：
```javascript
const unsubscribe = onNodeMove((nodeId, position) => {
  console.log('节点已移动:', nodeId, position);
});
```

#### 节点删除事件

```typescript
function onNodeRemove(callback: (nodeId: string) => void): () => void
```

**描述**：监听节点删除事件。

**参数**：
- `callback`：回调函数，接收被删除的节点ID

**返回值**：取消监听的函数

**示例**：
```javascript
const unsubscribe = onNodeRemove(nodeId => {
  console.log('节点已删除:', nodeId);
});
```

### 连线事件

#### 连线选择事件

```typescript
function onEdgeSelect(callback: (edgeId: string | null) => void): () => void
```

**描述**：监听连线选择事件。

**参数**：
- `callback`：回调函数，接收选中的连线ID或null（取消选择）

**返回值**：取消监听的函数

**示例**：
```javascript
const unsubscribe = onEdgeSelect(edgeId => {
  if (edgeId) {
    console.log('连线已选中:', edgeId);
  } else {
    console.log('已取消选择连线');
  }
});
```

#### 连线添加事件

```typescript
function onEdgeAdd(callback: (edge: Edge) => void): () => void
```

**描述**：监听连线添加事件。

**参数**：
- `callback`：回调函数，接收添加的连线对象

**返回值**：取消监听的函数

**示例**：
```javascript
const unsubscribe = onEdgeAdd(edge => {
  console.log('连线已添加:', edge.id);
});
```

#### 连线更新事件

```typescript
function onEdgeUpdate(callback: (edgeId: string, updates: Partial<Edge>) => void): () => void
```

**描述**：监听连线更新事件。

**参数**：
- `callback`：回调函数，接收连线ID和更新的属性

**返回值**：取消监听的函数

**示例**：
```javascript
const unsubscribe = onEdgeUpdate((edgeId, updates) => {
  console.log('连线已更新:', edgeId, updates);
});
```

#### 连线删除事件

```typescript
function onEdgeRemove(callback: (edgeId: string) => void): () => void
```

**描述**：监听连线删除事件。

**参数**：
- `callback`：回调函数，接收被删除的连线ID

**返回值**：取消监听的函数

**示例**：
```javascript
const unsubscribe = onEdgeRemove(edgeId => {
  console.log('连线已删除:', edgeId);
});
```

### 画布事件

#### 画布缩放事件

```typescript
function onCanvasZoom(callback: (scale: number) => void): () => void
```

**描述**：监听画布缩放事件。

**参数**：
- `callback`：回调函数，接收新的缩放比例

**返回值**：取消监听的函数

**示例**：
```javascript
const unsubscribe = onCanvasZoom(scale => {
  console.log('画布缩放比例:', scale);
});
```

#### 画布平移事件

```typescript
function onCanvasPan(callback: (position: { x: number, y: number }) => void): () => void
```

**描述**：监听画布平移事件。

**参数**：
- `callback`：回调函数，接收新的画布位置

**返回值**：取消监听的函数

**示例**：
```javascript
const unsubscribe = onCanvasPan(position => {
  console.log('画布位置:', position);
});
```

## 错误处理

API调用可能会抛出以下错误：

### NodeNotFoundError

当尝试操作不存在的节点时抛出。

```typescript
{
  name: 'NodeNotFoundError',
  message: '节点不存在: {nodeId}',
  nodeId: string
}
```

### EdgeNotFoundError

当尝试操作不存在的连线时抛出。

```typescript
{
  name: 'EdgeNotFoundError',
  message: '连线不存在: {edgeId}',
  edgeId: string
}
```

### DuplicateEdgeError

当尝试添加已存在的连线（相同源节点和目标节点）时抛出。

```typescript
{
  name: 'DuplicateEdgeError',
  message: '连线已存在: {sourceId} -> {targetId}',
  sourceId: string,
  targetId: string
}
```

### InvalidParameterError

当API参数无效时抛出。

```typescript
{
  name: 'InvalidParameterError',
  message: '参数无效: {paramName}',
  paramName: string,
  value: any
}
```

## 示例

### 创建简单的节点关系图

```javascript
// 添加节点
const rootNodeId = addNode({
  type: 'default',
  position: { x: 400, y: 100 },
  data: {
    label: '根节点',
    value: '10000',
    change: '+5%',
    period: '同比上涨'
  }
});

const childNode1Id = addNode({
  type: 'default',
  position: { x: 200, y: 300 },
  data: {
    label: '子节点1',
    value: '5000',
    change: '+10%',
    period: '同比上涨'
  }
});

const childNode2Id = addNode({
  type: 'default',
  position: { x: 600, y: 300 },
  data: {
    label: '子节点2',
    value: '5000',
    change: '+2%',
    period: '同比上涨'
  }
});

// 添加连线
addEdge({
  source: rootNodeId,
  target: childNode1Id,
  type: 'add',
  label: '相加关系'
});

addEdge({
  source: rootNodeId,
  target: childNode2Id,
  type: 'add',
  label: '相加关系'
});

// 应用自动布局
applyAutoLayout();
```

### 监听节点变化并更新UI

```javascript
// 监听节点选择事件
onNodeSelect(nodeId => {
  if (nodeId) {
    const node = getNodeById(nodeId);
    if (node) {
      // 更新UI显示选中节点的信息
      updateNodeInfoPanel(node);
    }
  } else {
    // 清空节点信息面板
    clearNodeInfoPanel();
  }
});

// 监听节点更新事件
onNodeUpdate((nodeId, updates) => {
  // 更新相关UI组件
  updateRelatedComponents(nodeId, updates);
});
```

## 版本历史

### v1.0.0 (2023-06-01)
- 初始版本发布
- 基本节点和连线管理功能
- 画布操作功能
- 自动布局算法

### v1.1.0 (2023-08-15)
- 添加事件监听系统
- 改进自动布局算法
- 修复已知问题

### v1.2.0 (2023-11-10)
- 添加配置管理功能
- 改进错误处理
- 性能优化
