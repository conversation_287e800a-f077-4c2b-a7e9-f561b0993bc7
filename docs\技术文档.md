# 可视化配置系统技术文档

## 1. 项目概述

可视化配置系统是一个基于Vue 3的前端应用，用于创建和管理节点关系图，支持节点参数配置、节点连接计算关系、自动布局等功能。系统提供了直观的可视化界面，使用户能够轻松创建、编辑和管理复杂的节点关系网络，特别适用于业务指标分析、数据流程建模和决策支持系统。

本文档详细介绍了系统的技术架构、核心组件和关键算法实现，旨在帮助开发者理解系统设计和实现细节，便于二次开发和功能扩展。

## 2. 技术架构

### 2.1 前端框架
- **Vue 3.5.13**: 采用组合式API（Composition API）开发，提供更好的代码组织和类型推断
- **TypeScript**: 提供静态类型检查，增强代码可维护性
- **Vite 6.3.4**: 现代前端构建工具，提供快速的开发体验和高效的构建过程

### 2.2 状态管理
- **Pinia 3.0.2**: Vue官方推荐的状态管理库，替代Vuex，提供更简洁的API和更好的TypeScript支持

### 2.3 路由
- **Vue Router 4.5.1**: Vue官方路由管理器，支持历史模式导航

### 2.4 样式
- **SCSS/SASS 1.87.0**: CSS预处理器，提供变量、嵌套、混合等高级功能

### 2.5 工具库
- **UUID 11.1.0**: 用于生成唯一标识符，确保节点和连线的唯一性

## 3. 项目结构

```
visual-config-system/
├── public/                 # 静态资源
├── src/                    # 源代码
│   ├── assets/             # 资源文件（图片、字体等）
│   ├── components/         # 组件
│   │   ├── AttributionPopup/   # 归因分析弹窗组件
│   │   ├── Canvas/         # 画布组件
│   │   ├── CanvasThumbnail/ # 画布缩略图组件
│   │   ├── ConfigPanel/    # 配置面板组件
│   │   ├── Edge/           # 连线组件
│   │   ├── IndicatorSelector/ # 指标选择器组件
│   │   ├── Node/           # 节点组件
│   │   └── RelationTypeSelector/ # 关系类型选择器组件
│   ├── stores/             # Pinia状态管理
│   │   ├── canvas.ts       # 画布状态
│   │   ├── edge.ts         # 连线状态
│   │   └── node.ts         # 节点状态
│   ├── types/              # TypeScript类型定义
│   ├── views/              # 页面视图
│   │   ├── Editor.vue      # 编辑器页面
│   │   └── Preview.vue     # 预览页面
│   ├── App.vue             # 根组件
│   ├── main.ts             # 入口文件
│   ├── router.ts           # 路由配置
│   └── style.css           # 全局样式
├── index.html              # HTML模板
├── tsconfig.json           # TypeScript配置
├── vite.config.ts          # Vite配置
└── package.json            # 项目依赖和脚本
```

## 4. 核心模块

### 4.1 画布模块（Canvas）

画布模块是系统的核心，负责节点和连线的渲染、交互和布局。

#### 4.1.1 主要功能
- 节点渲染与交互
- 连线渲染与交互
- 画布缩放与平移
- 自动布局算法
- 拖拽操作处理
- 连线创建与管理
- 缩略图显示

#### 4.1.2 关键组件
- `Canvas.vue`: 主画布组件，管理所有节点和连线
- `CanvasThumbnail.vue`: 画布缩略图组件，提供全局视图

#### 4.1.3 技术实现
画布使用CSS transform实现缩放和平移，通过Vue的响应式系统实时更新节点和连线的位置。画布支持以下交互：

- 鼠标拖拽：平移画布
- 鼠标滚轮：缩放画布
- 节点拖拽：移动节点
- 节点连接：创建节点间的连线

```typescript
// 画布缩放
const onWheel = (event: WheelEvent) => {
  event.preventDefault()

  // 计算缩放中心
  const canvasRect = canvasRef.value?.getBoundingClientRect()
  if (canvasRect) {
    const center = {
      x: event.clientX - canvasRect.left,
      y: event.clientY - canvasRect.top
    }

    // 执行缩放
    canvasStore.zoom(event.deltaY, center)
  }
}

// 画布平移
const onMouseMove = (event: MouseEvent) => {
  if (isDraggingCanvas.value) {
    // 计算鼠标移动的距离
    const deltaX = event.clientX - lastMousePosition.value.x
    const deltaY = event.clientY - lastMousePosition.value.y

    // 更新画布位置
    canvasStore.pan(deltaX, deltaY)

    // 更新最后的鼠标位置
    lastMousePosition.value = { x: event.clientX, y: event.clientY }
  }
}
```

### 4.2 节点模块（Node）

节点模块负责节点的渲染、交互和数据管理。

#### 4.2.1 主要功能
- 节点渲染
- 节点拖拽
- 节点选择
- 节点连接
- 节点样式配置
- 归因分析支持

#### 4.2.2 关键组件
- `Node.vue`: 节点组件，负责节点的渲染和交互
- `NodeStore`: 节点状态管理，负责节点数据的CRUD操作

#### 4.2.3 技术实现
节点使用绝对定位实现在画布中的自由放置，支持自定义样式和内容。节点组件实现了以下功能：

- 拖拽移动：通过mousedown/mousemove/mouseup事件实现
- 连接点：用于创建节点间的连线
- 样式自定义：支持边框样式、颜色、阴影等配置
- 归因分析：支持显示归因分析弹窗

```typescript
// 节点拖拽实现
const onNodeMouseDown = (event: MouseEvent) => {
  if (props.readonly) return

  // 防止事件冒泡
  event.stopPropagation()

  // 选中节点
  emit('select', props.node.id)

  // 开始拖拽
  isDragging.value = true

  // 计算鼠标点击位置与节点左上角的偏移
  dragOffset.value = {
    x: event.clientX - props.node.position.x,
    y: event.clientY - props.node.position.y
  }

  // 发送拖拽开始事件
  emit('drag-start', props.node.id)

  // 添加全局鼠标事件监听
  document.addEventListener('mousemove', onMouseMove)
  document.addEventListener('mouseup', onMouseUp)
}
```

### 4.3 连线模块（Edge）

连线模块负责节点间连线的渲染、交互和数据管理。

#### 4.3.1 主要功能
- 连线渲染
- 连线样式配置
- 计算关系表示
- 连线交互

#### 4.3.2 关键组件
- `Edge.vue`: 连线组件，负责连线的渲染和交互
- `EdgeStore`: 连线状态管理，负责连线数据的CRUD操作

#### 4.3.3 技术实现
连线使用SVG实现，支持贝塞尔曲线和直线两种形式。连线由三部分组成：

1. 计算符号：显示节点间的计算关系（+、-、×、÷、C）
2. 连接器：从源节点到计算符号的实线部分
3. 目标线：从计算符号到目标节点的虚线部分

```typescript
// 生成连接器路径
const getConnectorPath = () => {
  const start = {
    x: sourceNode.value.position.x + (sourceNode.value.size?.width || 240),
    y: sourceNode.value.position.y + (sourceNode.value.size?.height || 90) / 2
  }

  // 计算符号位置
  const symbolX = start.x + CONNECTOR_LENGTH

  return `M ${start.x} ${start.y} L ${symbolX} ${start.y}`
}

// 生成目标线路径
const getTargetPath = () => {
  const start = connectorEndPoint.value
  const end = {
    x: targetNode.value.position.x,
    y: targetNode.value.position.y + (targetNode.value.size?.height || 90) / 2
  }

  // 使用贝塞尔曲线创建平滑路径
  const controlPoint1 = {
    x: start.x + Math.min(100, (end.x - start.x) * 0.4),
    y: start.y
  }

  const controlPoint2 = {
    x: end.x - Math.min(100, (end.x - start.x) * 0.4),
    y: end.y
  }

  return `M ${start.x} ${start.y} C ${controlPoint1.x} ${controlPoint1.y}, ${controlPoint2.x} ${controlPoint2.y}, ${end.x} ${end.y}`
}
```

### 4.4 配置面板模块（ConfigPanel）

配置面板模块负责节点和连线属性的配置和编辑。

#### 4.4.1 主要功能
- 节点基础属性配置
- 节点样式配置
- 节点数据配置
- 节点高级配置
- 连线属性配置
- 归因分析配置

#### 4.4.2 关键组件
- `ConfigPanel.vue`: 配置面板组件，负责属性的显示和编辑

#### 4.4.3 技术实现
配置面板使用选项卡结构组织不同类别的配置项，通过双向绑定实现属性的实时更新。主要包括以下选项卡：

1. 基础属性：标签、位置、类型等
2. 样式配置：颜色、边框、字体等
3. 数据配置：数据源、格式等
4. 高级配置：公式、归因分析等

```typescript
// 监听选中节点变化，更新表单数据
watch(selectedNode, (node) => {
  if (node) {
    nodeLabel.value = node.data.label || ''
    nodeType.value = node.type || 'kpi'
    nodePositionX.value = node.position.x
    nodePositionY.value = node.position.y
    calculationPeriod.value = node.data.calculationPeriod || 'day'
    compareIndicator.value = node.data.compareIndicator || false
    nodeColor.value = node.data.color || '#1890ff'
    nodeBorderStyle.value = node.data.borderStyle || 'solid'
    nodeBorderWidth.value = node.data.borderWidth || 4
    // ... 其他属性
  }
}, { immediate: true })
```

### 4.5 指标选择器模块（IndicatorSelector）

指标选择器模块负责指标的搜索、选择和管理。

#### 4.5.1 主要功能
- 指标搜索
- 指标选择
- 指标分类管理

#### 4.5.2 关键组件
- `IndicatorSelector.vue`: 指标选择器组件，负责指标的显示和选择

#### 4.5.3 技术实现
指标选择器提供搜索框和指标列表，支持按名称搜索指标。选择指标后，将指标数据应用到节点上。

```typescript
// 过滤指标
const filteredIndicators = computed(() => {
  if (!searchText.value) return indicators

  return indicators.filter(indicator =>
    indicator.label.toLowerCase().includes(searchText.value.toLowerCase())
  )
})

// 选择指标
const selectIndicator = (indicator: any) => {
  emit('select', {
    sourceNodeId: props.sourceNodeId,
    relationType: props.relationType,
    indicator
  })
}
```

### 4.6 关系类型选择器模块（RelationTypeSelector）

关系类型选择器模块负责节点间关系类型的选择和管理。

#### 4.6.1 主要功能
- 关系类型选择
- 关系图标显示

#### 4.6.2 关键组件
- `RelationTypeSelector.vue`: 关系类型选择器组件，负责关系类型的显示和选择

#### 4.6.3 技术实现
关系类型选择器提供多种计算关系类型（相加、相减、相乘、相除、相关），用户可以选择适合的关系类型应用到连线上。

```typescript
const relationTypes = [
  { id: 'add', name: '相加关系', icon: '+' },
  { id: 'subtract', name: '相减关系', icon: '-' },
  { id: 'multiply', name: '相乘关系', icon: '×' },
  { id: 'divide', name: '相除关系', icon: '÷' },
  { id: 'related', name: '相关关系', icon: 'C' }
]

const selectRelation = (relation: { id: string, name: string, icon: string }) => {
  currentRelationType.value = relation.id
  emit('select', {
    edgeId: props.edgeId,
    relationType: relation
  })
}
```

### 4.7 归因分析弹窗模块（AttributionPopup）

归因分析弹窗模块负责显示节点的归因分析数据。

#### 4.7.1 主要功能
- 归因数据显示
- 归因比例可视化

#### 4.7.2 关键组件
- `AttributionPopup.vue`: 归因分析弹窗组件，负责归因数据的显示

#### 4.7.3 技术实现
归因分析弹窗使用Teleport将弹窗内容传送到body元素，确保弹窗在视觉上位于最上层。弹窗显示归因项列表，包括名称、值、百分比和贡献度。

```typescript
<teleport to="body">
  <div class="attribution-popup" v-if="visible">
    <div class="popup-backdrop" @click="close"></div>
    <div class="popup-content">
      <div class="popup-header">
        <h3>{{ title }}</h3>
        <button class="close-btn" @click="close">×</button>
      </div>
      <div class="popup-body">
        <div v-if="attributionData && attributionData.length > 0">
          <div class="attribution-summary">
            <div class="summary-title">归因分析</div>
            <div class="summary-description">
              {{ description }}
            </div>
          </div>

          <div class="attribution-list">
            <div v-for="item in attributionData" :key="item.id" class="attribution-item">
              <!-- 归因项内容 -->
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</teleport>
```

### 4.8 画布缩略图模块（CanvasThumbnail）

画布缩略图模块负责显示画布的缩略视图，帮助用户了解当前视口在整个画布中的位置。

#### 4.8.1 主要功能
- 画布缩略视图显示
- 视口位置指示
- 快速导航

#### 4.8.2 关键组件
- `CanvasThumbnail.vue`: 画布缩略图组件，负责缩略视图的显示和交互

#### 4.8.3 技术实现
缩略图使用缩放比例将画布内容按比例缩小显示，并使用视口指示器显示当前可见区域。用户可以通过点击缩略图快速导航到画布的不同区域。

```typescript
// 计算缩略图的缩放比例
const thumbnailScale = computed(() => {
  // 缩略图的尺寸
  const thumbnailWidth = 220
  const thumbnailHeight = 120

  // 计算缩放比例，使其与主画布保持相同的比例
  const scaleX = thumbnailWidth / props.containerSize.width
  const scaleY = thumbnailHeight / props.containerSize.height

  // 取较小的缩放比例，确保整个视口内容都能显示
  return Math.min(scaleX, scaleY) * 0.7 // 稍微缩小一点，留出边距
})
```

## 自动布局算法

自动布局算法是系统的核心功能之一，确保节点在画布中的合理分布。

### 算法原则
1. 当节点只有一个子节点时，子节点与父节点水平中心对齐
2. 当节点有多个子节点时，子节点对称分布，与父节点形成局部对称效果
3. 新增节点时，确保不与现有节点重叠
4. 子节点与父节点之间保持固定的水平间距
5. 父节点与叔叔节点（同级节点）之间的垂直间距大于子节点之间的垂直间距

### 算法实现

```typescript
// 构建节点关系树
const buildNodeTree = () => {
  const nodeMap = new Map<string, {
    node: NodeType,
    children: string[],
    parent: string | null,
    depth: number,
    siblingIndex: number,
    hasSingleChild: boolean,
    totalSiblings: number,
    parentGroup: string,
    siblingNodes: string[]
  }>()

  // 初始化节点映射
  nodes.value.forEach(node => {
    nodeMap.set(node.id, {
      node,
      children: [],
      parent: null,
      depth: 0,
      siblingIndex: 0,
      hasSingleChild: false,
      totalSiblings: 0,
      parentGroup: '',
      siblingNodes: []
    })
  })

  // 构建父子关系
  edges.value.forEach(edge => {
    const sourceInfo = nodeMap.get(edge.source)
    const targetInfo = nodeMap.get(edge.target)

    if (sourceInfo && targetInfo) {
      sourceInfo.children.push(edge.target)
      targetInfo.parent = edge.source
    }
  })

  // 计算节点深度和兄弟节点索引
  const calculateDepth = (nodeId: string, depth: number) => {
    const nodeInfo = nodeMap.get(nodeId)
    if (!nodeInfo) return

    nodeInfo.depth = depth

    // 递归计算子节点深度
    nodeInfo.children.forEach(childId => {
      calculateDepth(childId, depth + 1)
    })
  }

  // 找出根节点并计算深度
  const rootNodes = Array.from(nodeMap.values())
    .filter(info => info.parent === null)
    .map(info => info.node.id)

  rootNodes.forEach(rootId => {
    calculateDepth(rootId, 0)
  })

  return nodeMap
}

// 应用自动布局
const applyAutoLayout = (forceLayout = false) => {
  if (!autoLayoutEnabled.value && !forceLayout) return

  // 构建节点关系树
  const nodeMap = buildNodeTree()

  // 水平布局：确保子节点与父节点的水平间距
  const applyHorizontalLayout = () => {
    // 按深度从小到大排序节点
    const nodesByDepth = Array.from(nodeMap.values())
      .sort((a, b) => a.depth - b.depth)

    // 从根节点开始布局
    nodesByDepth.forEach(nodeInfo => {
      const { node, children, parent } = nodeInfo

      // 跳过没有父节点的节点
      if (!parent) return

      const parentNode = nodeStore.getNodeById(parent)
      if (!parentNode) return

      // 计算父节点中心点
      const parentCenterX = parentNode.position.x + (parentNode.size?.width || NODE_WIDTH) / 2

      // 计算子节点的X坐标
      const childNodeWidth = node.size?.width || NODE_WIDTH
      const childX = parentCenterX - childNodeWidth / 2 + HORIZONTAL_SPACING

      // 更新节点位置
      nodeStore.moveNode(node.id, {
        x: childX,
        y: node.position.y
      })
    })
  }

  // 垂直布局：确保子节点的垂直分布
  const applyVerticalLayout = () => {
    // 处理每个父节点
    Array.from(nodeMap.values()).forEach(nodeInfo => {
      const { node, children } = nodeInfo

      // 跳过没有子节点的节点
      if (children.length === 0) return

      // 获取子节点
      const childNodes = children
        .map(id => nodeStore.getNodeById(id))
        .filter(node => node !== null) as NodeType[]

      // 计算父节点中心点
      const parentCenterY = node.position.y + (node.size?.height || NODE_HEIGHT) / 2

      // 单个子节点：水平对齐
      if (childNodes.length === 1) {
        nodeStore.moveNode(childNodes[0].id, {
          x: childNodes[0].position.x,
          y: parentCenterY - (childNodes[0].size?.height || NODE_HEIGHT) / 2
        })
        return
      }

      // 多个子节点：对称分布
      const totalChildHeight = (childNodes.length - 1) * VERTICAL_SPACING +
        childNodes.reduce((sum, node) => sum + (node.size?.height || NODE_HEIGHT), 0)

      let currentY = parentCenterY - totalChildHeight / 2

      childNodes.forEach(childNode => {
        nodeStore.moveNode(childNode.id, {
          x: childNode.position.x,
          y: currentY
        })

        currentY += (childNode.size?.height || NODE_HEIGHT) + VERTICAL_SPACING
      })
    })
  }

  // 确保父节点与叔叔节点之间的间距
  const ensureParentUncleSpacing = () => {
    // 按深度从小到大排序节点
    const nodesByDepth = Array.from(nodeMap.values())
      .sort((a, b) => a.depth - b.depth)

    // 从根节点开始处理
    nodesByDepth.forEach(nodeInfo => {
      const { node, children, parent } = nodeInfo

      // 跳过没有父节点或子节点的节点
      if (!parent || children.length === 0) return

      const parentInfo = nodeMap.get(parent)
      if (!parentInfo) return

      // 获取叔叔节点（父节点的兄弟节点）
      const uncleNodes = parentInfo.siblingNodes
        .map(id => nodeStore.getNodeById(id))
        .filter(node => node !== null) as NodeType[]

      // 跳过没有叔叔节点的情况
      if (uncleNodes.length === 0) return

      // 计算子节点占用的空间
      const childrenCount = children.length
      let requiredSpace = 0

      if (childrenCount > 1) {
        // 如果有多个子节点，计算它们占用的垂直空间
        requiredSpace = (childrenCount - 1) * VERTICAL_SPACING * 3.5 + NODE_HEIGHT
      } else if (childrenCount === 1) {
        // 即使只有一个子节点，也预留一定空间
        requiredSpace = NODE_HEIGHT * 4.0
      }

      // 计算表兄弟节点（叔叔节点的子节点）
      let additionalSpaceForCousins = 0
      let cousinCount = 0

      uncleNodes.forEach(uncleNode => {
        const uncleInfo = nodeMap.get(uncleNode.id)
        if (uncleInfo) {
          cousinCount += uncleInfo.children.length
        }
      })

      // 根据表兄弟节点数量增加额外间距
      if (cousinCount > 1) {
        additionalSpaceForCousins = Math.max(
          additionalSpaceForCousins,
          (cousinCount - 1) * VERTICAL_SPACING * 2.5
        )
      } else {
        additionalSpaceForCousins = Math.max(
          additionalSpaceForCousins,
          NODE_HEIGHT * 3.0
        )
      }

      // 计算所需的最小间距
      const minRequiredGap = PARENT_UNCLE_VERTICAL_SPACING * 1.5 +
        requiredSpace + additionalSpaceForCousins

      // 检查当前间距
      const parentNode = nodeStore.getNodeById(parent)
      if (!parentNode) return

      uncleNodes.forEach(uncleNode => {
        const currentGap = Math.abs(parentNode.position.y - uncleNode.position.y)

        // 如果当前间距小于所需间距，调整位置
        if (currentGap < minRequiredGap) {
          // 确定移动方向
          const moveDirection = parentNode.position.y > uncleNode.position.y ? 1 : -1
          const moveDistance = (minRequiredGap - currentGap) / 2

          // 移动父节点
          nodeStore.moveNode(parentNode.id, {
            x: parentNode.position.x,
            y: parentNode.position.y + moveDirection * moveDistance
          })

          // 移动叔叔节点
          nodeStore.moveNode(uncleNode.id, {
            x: uncleNode.position.x,
            y: uncleNode.position.y - moveDirection * moveDistance
          })

          // 递归处理：移动父节点后，需要同步移动其子节点
          const updatedParentY = parentNode.position.y + moveDirection * moveDistance

          // 处理父节点的子节点位置
          const childNodes = children
            .map(id => nodeStore.getNodeById(id))
            .filter(node => node !== null) as NodeType[]

          if (childrenCount === 1) {
            // 如果只有一个子节点，确保与父节点水平对齐
            nodeStore.moveNode(childNodes[0].id, {
              x: childNodes[0].position.x,
              y: updatedParentY
            })
          } else if (childrenCount > 1) {
            // 如果有多个子节点，确保它们对称分布
            const parentCenterY = updatedParentY + NODE_HEIGHT / 2
            const totalChildHeight = (childNodes.length - 1) * VERTICAL_SPACING + NODE_HEIGHT
            let firstChildY = parentCenterY - totalChildHeight / 2

            childNodes.forEach((childNode, index) => {
              nodeStore.moveNode(childNode.id, {
                x: childNode.position.x,
                y: firstChildY + index * VERTICAL_SPACING
              })
            })
          }
        }
      })
    })
  }

  // 应用布局
  applyHorizontalLayout()
  applyVerticalLayout()
  ensureParentUncleSpacing()
}
```

## 数据流

系统采用单向数据流模式，通过Pinia状态管理库管理全局状态。

### 主要状态模块

1. **节点状态（NodeStore）**
   - 管理所有节点数据
   - 处理节点的添加、更新、删除操作
   - 管理节点选择状态

2. **连线状态（EdgeStore）**
   - 管理所有连线数据
   - 处理连线的添加、更新、删除操作
   - 管理连线选择状态

3. **画布状态（CanvasStore）**
   - 管理画布位置、缩放和尺寸
   - 处理画布交互操作

## 性能优化

系统实现了多项性能优化措施，确保在处理大量节点和连线时保持流畅的用户体验。

### 主要优化措施

1. **计算属性缓存**
   - 使用Vue的计算属性缓存复杂计算结果
   - 只在依赖变化时重新计算

2. **虚拟滚动**
   - 在大型列表中使用虚拟滚动技术
   - 只渲染可见区域的项目

3. **延迟加载**
   - 非关键组件和功能采用延迟加载
   - 减少初始加载时间

4. **事件节流与防抖**
   - 对频繁触发的事件（如鼠标移动、滚轮缩放）应用节流和防抖技术
   - 减少不必要的计算和渲染

## 部署与维护

### 构建与部署

系统使用Vite构建工具进行构建，生成静态文件后可部署到任何Web服务器。

#### 构建命令
```bash
pnpm build
```

#### 部署方式
1. **传统Web服务器（Nginx/Apache）**
2. **Docker容器**
3. **静态网站托管服务（Netlify、Vercel等）**

### 配置管理

系统支持通过环境变量进行配置管理，可以针对不同环境（开发、测试、生产）设置不同的配置。

#### 环境变量文件
- `.env`：默认环境变量
- `.env.production`：生产环境变量

## 未来扩展

系统设计考虑了未来扩展的可能性，主要扩展方向包括：

1. **更多节点类型**：支持更多类型的节点，如图表节点、表格节点等
2. **更多关系类型**：支持更复杂的节点关系类型
3. **数据导入导出**：支持从外部系统导入数据，以及导出配置结果
4. **协作功能**：支持多用户同时编辑同一配置
5. **历史记录与回滚**：支持操作历史记录和回滚功能

## 结论

可视化配置系统是一个功能强大、易于使用的节点关系图编辑工具，通过Vue 3和TypeScript的结合，提供了良好的开发体验和用户体验。系统的核心自动布局算法确保了节点在画布中的合理分布，为用户提供了直观、高效的配置体验。
