<template>
  <div class="relation-type-selector">
    <div class="selector-header">
      <h3>选择计算关系</h3>
    </div>
    <div class="relation-list">
      <div
        v-for="relation in relationTypes"
        :key="relation.id"
        class="relation-item"
        :class="{ active: relation.id === currentRelationType }"
        @click="selectRelation(relation)"
      >
        <div class="relation-icon">{{ relation.icon }}</div>
        <div class="relation-name">{{ relation.name }}</div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue'

export default defineComponent({
  name: 'RelationTypeSelector',
  props: {
    edgeId: {
      type: String,
      required: true
    },
    currentType: {
      type: String,
      default: 'related'
    }
  },
  emits: ['select'],
  setup(props, { emit }) {
    const currentRelationType = ref(props.currentType)

    const relationTypes = [
      { id: 'add', name: '相加关系', icon: '+' },
      { id: 'subtract', name: '相减关系', icon: '-' },
      { id: 'multiply', name: '相乘关系', icon: '×' },
      { id: 'divide', name: '相除关系', icon: '÷' },
      { id: 'related', name: '相关关系', icon: 'C' }
    ]

    const selectRelation = (relation: { id: string, name: string, icon: string }) => {
      currentRelationType.value = relation.id
      emit('select', {
        edgeId: props.edgeId,
        relationType: relation
      })
    }

    return {
      relationTypes,
      currentRelationType,
      selectRelation
    }
  }
})
</script>

<style lang="scss" scoped>
.relation-type-selector {
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  width: 200px;
  margin-left: 0; /* 移除左侧边距，使弹窗更贴近计算符号 */

  .selector-header {
    padding: 12px 16px;
    border-bottom: 1px solid #f0f0f0;

    h3 {
      margin: 0;
      font-size: 14px;
      font-weight: 500;
      color: #333;
    }
  }

  .relation-list {
    padding: 8px 0;

    .relation-item {
      display: flex;
      align-items: center;
      padding: 8px 16px;
      cursor: pointer;

      &:hover {
        background-color: #f5f5f5;
      }

      &.active {
        background-color: #e6f7ff;
        color: #1890ff;
      }

      .relation-icon {
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 8px;
        font-weight: bold;
        font-size: 16px;
      }

      .relation-name {
        font-size: 14px;
      }
    }
  }
}
</style>