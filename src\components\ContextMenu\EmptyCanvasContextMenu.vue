<template>
  <teleport to="body">
    <div
      v-if="visible"
      class="context-menu"
      :style="{
        left: `${position.x}px`,
        top: `${position.y}px`
      }"
      @click.stop
    >
      <div class="menu-item" @click="$emit('add-node')">
        <span class="menu-icon">+</span>
        <span class="menu-text">添加节点</span>
      </div>
      <div class="menu-item" @click="$emit('paste')">
        <span class="menu-icon">📋</span>
        <span class="menu-text">粘贴</span>
      </div>
      <div class="menu-item" @click="$emit('auto-layout')">
        <span class="menu-icon">⟲</span>
        <span class="menu-text">自动布局</span>
      </div>
    </div>
  </teleport>
</template>

<script lang="ts">
import { defineComponent } from 'vue'
import type { Position } from '../../types'

export default defineComponent({
  name: 'EmptyCanvasContextMenu',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    position: {
      type: Object as () => Position,
      required: true
    }
  },
  emits: ['add-node', 'paste', 'auto-layout']
})
</script>

<style lang="scss" scoped>
.context-menu {
  position: fixed;
  z-index: 1000;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  min-width: 180px;
  padding: 8px 0;
  border: 1px solid rgba(24, 144, 255, 0.1);
  animation: fadeIn 0.2s ease-out;

  .menu-item {
    padding: 10px 16px;
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: all 0.2s;

    &:hover {
      background-color: rgba(24, 144, 255, 0.05);
      color: #1890ff;
    }

    .menu-icon {
      margin-right: 12px;
      font-size: 18px;
      width: 20px;
      text-align: center;
    }

    .menu-text {
      font-size: 14px;
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
