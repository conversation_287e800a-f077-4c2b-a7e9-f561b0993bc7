import { ref, computed } from 'vue'
import { useEdgeStore } from '../../stores/edge'
import { useNodeStore } from '../../stores/node'
import type { Position } from '../../types'

export function useEdgeOperations(canvasRef: any, canvasPosition: any, canvasScale: any, readonly = false, emit: Function) {
  const edgeStore = useEdgeStore()
  const nodeStore = useNodeStore()
  
  // Edge state
  const edges = computed(() => edgeStore.edges)
  const selectedEdgeId = computed(() => edgeStore.selectedEdgeId)
  
  // Connection state
  const isConnecting = ref(false)
  const connectingSourceId = ref<string | null>(null)
  const connectingPosition = ref<Position>({ x: 0, y: 0 })
  
  // Get node by ID (from node store)
  const getNodeById = (id: string) => nodeStore.getNodeById(id)
  
  // Select an edge
  const selectEdge = (id: string | null) => {
    if (readonly) return

    edgeStore.selectEdge(id)
    nodeStore.selectNode(null)
  }
  
  // Start connecting nodes
  const startConnecting = (nodeId: string, position: Position) => {
    if (readonly) return
    
    isConnecting.value = true
    connectingSourceId.value = nodeId
    connectingPosition.value = position
  }
  
  // Finish connecting nodes
  const finishConnecting = (targetId: string) => {
    if (readonly || !isConnecting.value || !connectingSourceId.value) return
    
    // Don't connect a node to itself
    if (connectingSourceId.value !== targetId) {
      edgeStore.addEdge({
        id: '',
        source: connectingSourceId.value,
        target: targetId,
        label: 'C'
      })
    }
    
    // Reset connection state
    isConnecting.value = false
    connectingSourceId.value = null
  }
  
  // Update connecting line position during mouse move
  const updateConnectingPosition = (event: MouseEvent) => {
    if (!canvasRef.value || !isConnecting.value || readonly) return
    
    const canvasRect = canvasRef.value.getBoundingClientRect()
    
    connectingPosition.value = {
      x: (event.clientX - canvasRect.left - canvasPosition.value.x) / canvasScale.value,
      y: (event.clientY - canvasRect.top - canvasPosition.value.y) / canvasScale.value
    }
  }
  
  // Get temporary edge path for drawing
  const getTempEdgePath = () => {
    if (!isConnecting.value || !connectingSourceId.value) return ''
    
    const sourceNode = getNodeById(connectingSourceId.value)
    if (!sourceNode) return ''
    
    // Source node's right middle point
    const startX = sourceNode.position.x + (sourceNode.size?.width || 240)
    const startY = sourceNode.position.y + (sourceNode.size?.height || 90) / 2
    
    // Calculate control points for curved path
    const dx = Math.abs(connectingPosition.value.x - startX)
    const dy = connectingPosition.value.y - startY
    
    // Check if source and target are on the same level
    const sameLevel = Math.abs(dy) < 5
    
    if (sameLevel) {
      // Simple bezier curve for same level
      const offsetX = Math.min(dx * 0.15, 30) // 与Edge.vue保持一致，缩短虚线
      
      const controlPoint1 = {
        x: startX + offsetX,
        y: startY
      }
      
      const controlPoint2 = {
        x: connectingPosition.value.x - offsetX,
        y: connectingPosition.value.y
      }
      
      return `M ${startX} ${startY} C ${controlPoint1.x} ${controlPoint1.y}, ${controlPoint2.x} ${controlPoint2.y}, ${connectingPosition.value.x} ${connectingPosition.value.y}`
    }
    
    // More complex curve for different levels
    let offsetX = Math.min(dx * 0.15, 40) // 与Edge.vue保持一致，缩短虚线
    let verticalCurveFactor = 0.25

    // Adjust curve parameters based on distances
    if (dx < 100) {
      offsetX = Math.max(offsetX, 25) // 与Edge.vue保持一致，调整最小偏移
    }

    if (Math.abs(dy) > 150) {
      offsetX = Math.min(dx * 0.15, 50) // 与Edge.vue保持一致，缩短虚线
      verticalCurveFactor = 0.2
    }
    
    if (Math.abs(dy) < 50 && Math.abs(dy) >= 5) {
      verticalCurveFactor = 0.1
    }
    
    // Calculate control points based on direction
    let controlPoint1, controlPoint2
    
    if (dy < 0) {
      // Target above source
      controlPoint1 = {
        x: startX + offsetX,
        y: startY + (dy * verticalCurveFactor)
      }
      
      controlPoint2 = {
        x: connectingPosition.value.x - offsetX,
        y: connectingPosition.value.y - (dy * verticalCurveFactor)
      }
    } else {
      // Target below source
      controlPoint1 = {
        x: startX + offsetX,
        y: startY + (dy * verticalCurveFactor)
      }
      
      controlPoint2 = {
        x: connectingPosition.value.x - offsetX,
        y: connectingPosition.value.y - (dy * verticalCurveFactor)
      }
    }
    
    return `M ${startX} ${startY} C ${controlPoint1.x} ${controlPoint1.y}, ${controlPoint2.x} ${controlPoint2.y}, ${connectingPosition.value.x} ${connectingPosition.value.y}`
  }
  
  // Handle symbol click (calculation symbol)
  const onSymbolClick = (edgeId: string, symbolInfo?: any) => {
    if (readonly) return
    
    // Send close event first
    emit('close-relation-selector')
    
    // Select the edge
    edgeStore.selectEdge(edgeId)
    nodeStore.selectNode(null)
    
    // Get the edge
    const edge = edgeStore.getEdgeById(edgeId)
    if (!edge) return
    
    // Adjust symbol position with canvas transformation
    let adjustedSymbolInfo = symbolInfo
    
    if (symbolInfo && canvasRef.value) {
      const canvasRect = canvasRef.value.getBoundingClientRect()
      
      // Calculate actual screen position
      const screenX = symbolInfo.x * canvasScale.value + canvasPosition.value.x + canvasRect.left
      const screenY = symbolInfo.y * canvasScale.value + canvasPosition.value.y + canvasRect.top
      
      adjustedSymbolInfo = {
        ...symbolInfo,
        screenX,
        screenY,
        canvasScale: canvasScale.value,
        canvasPosition: canvasPosition.value,
        canvasRect: {
          left: canvasRect.left,
          top: canvasRect.top,
          width: canvasRect.width,
          height: canvasRect.height
        }
      }
    }
    
    // Send event to parent
    emit('add-relation', edge.source, {
      id: edge.type || 'related',
      name: '修改关系',
      icon: edge.label || 'C',
      symbolInfo: adjustedSymbolInfo
    })
  }
  
  // Handle connector click
  const onConnectorClick = (edgeId: string, position: Position) => {
    if (readonly) return
    
    // Select the edge
    edgeStore.selectEdge(edgeId)
    nodeStore.selectNode(null)
    
    // Get the edge
    const edge = edgeStore.getEdgeById(edgeId)
    if (!edge) return
    
    // Send event to parent
    emit('connector-click', edge.source, edge.type || 'related', position)
  }
  
  return {
    edges,
    selectedEdgeId,
    isConnecting,
    connectingSourceId,
    connectingPosition,
    selectEdge,
    startConnecting,
    finishConnecting,
    updateConnectingPosition,
    getTempEdgePath,
    onSymbolClick,
    onConnectorClick
  }
}
