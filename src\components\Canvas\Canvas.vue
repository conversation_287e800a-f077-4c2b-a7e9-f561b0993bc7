<template>
  <div
    ref="canvasRef"
    class="canvas"
    @mousedown="onMouseDown"
    @mousemove="onMouseMove"
    @mouseup="onMouseUp"
    @mouseleave="onMouseUp"
    @wheel="handleZoom"
    @contextmenu="onCanvasContextMenu"
  >
    <!-- 空画布状态 -->
    <CanvasEmptyState
      :readonly="readonly"
      :nodes="nodes"
      @create-empty-node="createEmptyNode"
      @empty-canvas-right-click="onEmptyCanvasRightClick"
    />

    <!-- 画布内容 -->
    <CanvasContent
      :canvas-position="canvasPosition"
      :canvas-scale="canvasScale"
      :canvas-dimensions="canvasDimensions"
      :nodes="nodes"
      :edges="edges"
      :selected-node-id="selectedNodeId"
      :selected-edge-id="selectedEdgeId"
      :is-connecting="isConnecting"
      :readonly="readonly"
      :dragged-node-id="draggedNodeId"
      :temp-edge-path="tempEdgePath"
      @select-node="selectNode"
      @move-node="moveNode"
      @select-edge="selectEdge"
      @connect-start="startConnecting"
      @connect-end="finishConnecting"
      @add-relation="handleAddRelation"
      @show-attribution="handleShowAttribution"
      @drag-start="handleDragStart"
      @drag-end="handleDragEnd"
      @copy-node="handleCopyNode"
      @delete-node="handleDeleteNode"
      @symbol-click="onSymbolClick"
      @connector-click="onConnectorClick"
      @toggle-children="onToggleChildren"
      @get-temp-edge-path="getTempEdgePath"
    />

    <!-- 实时缩略图 -->
    <CanvasThumbnail
      v-if="!readonly && showThumbnail"
      :nodes="nodesWithDragState"
      :edges="edges"
      :canvas-position="canvasPosition"
      :canvas-scale="canvasScale"
      :canvas-dimensions="canvasDimensions"
      :container-size="containerSize"
      :selected-node-id="selectedNodeId"
    />

    <!-- 缩略图切换按钮 -->
    <CanvasThumbnailControl
      :readonly="readonly"
      :show-thumbnail="showThumbnail"
      @toggle-thumbnail="toggleThumbnail"
    />

    <!-- 空画布右键菜单 -->
    <EmptyCanvasContextMenu
      :visible="showEmptyCanvasMenu"
      :position="emptyCanvasMenuPosition"
      @add-node="handleAddNodeFromMenu"
      @paste="handlePasteFromMenu"
      @auto-layout="handleAutoLayoutFromMenu"
    />
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed, watch, nextTick } from 'vue'
import CanvasContent from './components/CanvasContent.vue'
import CanvasEmptyState from './components/CanvasEmptyState.vue'
import CanvasThumbnailControl from './components/CanvasThumbnailControl.vue'
import CanvasThumbnail from '../CanvasThumbnail/CanvasThumbnail.vue'
import EmptyCanvasContextMenu from '../ContextMenu/EmptyCanvasContextMenu.vue'

// Composables
import { useCanvasInteraction } from '../../composables/canvas/useCanvasInteraction'
import { useNodeOperations } from '../../composables/canvas/useNodeOperations'
import { useEdgeOperations } from '../../composables/canvas/useEdgeOperations'
import { useNodeTree } from '../../composables/canvas/useNodeTree'
import { useCanvasEvents } from '../../composables/canvas/useCanvasEvents'
import { useLayoutCalculation } from '../../composables/canvas/useLayoutCalculation'

// Stores
import { useCanvasStore } from '../../stores/canvas'

export default defineComponent({
  name: 'Canvas',
  components: {
    CanvasContent,
    CanvasEmptyState,
    CanvasThumbnailControl,
    CanvasThumbnail,
    EmptyCanvasContextMenu
  },
  props: {
    readonly: {
      type: Boolean,
      default: false
    }
  },
  emits: ['add-relation', 'create-node', 'connector-click', 'node-removed', 'show-attribution', 'auto-layout', 'close-relation-selector', 'create-empty-node'],
  setup(props, { emit }) {
    // Canvas ref
    const canvasRef = ref<HTMLElement | null>(null)
    
    // Thumbnail state
    const showThumbnail = ref(true)
    
    // Canvas store
    const canvasStore = useCanvasStore()
    
    // Canvas interaction
    const {
      canvasScale,
      canvasPosition,
      canvasDimensions,
      containerSize,
      isDraggingCanvas,
      startCanvasDrag,
      dragCanvas,
      endCanvasDrag,
      handleZoom,
      centerOnPosition
    } = useCanvasInteraction(canvasRef)
    
    // Node operations
    const {
      nodes,
      selectedNodeId,
      draggedNodeId,
      nodesWithDragState,
      autoLayoutEnabled,
      getNodeById,
      selectNode,
      moveNode,
      createEmptyNode: createNode,
      handleDragStart,
      handleDragEnd,
      handleCopyNode,
      handleDeleteNode,
      handleAddRelation,
      handleShowAttribution
    } = useNodeOperations(props.readonly, emit)
    
    // Edge operations
    const {
      edges,
      selectedEdgeId,
      isConnecting,
      connectingSourceId,
      connectingPosition,
      selectEdge,
      startConnecting,
      finishConnecting,
      updateConnectingPosition,
      getTempEdgePath,
      onSymbolClick,
      onConnectorClick
    } = useEdgeOperations(canvasRef, canvasPosition, canvasScale, props.readonly, emit)
    
    // Node tree
    const {
      buildNodeTree,
      optimizeNodeSpacing
    } = useNodeTree(nodes, edges, getNodeById)
    
    // Layout calculation
    const {
      applyAutoLayout,
      forceAdjustParentUncleSpacing
    } = useLayoutCalculation(buildNodeTree, getNodeById)
    
    // Canvas events
    const {
      showEmptyCanvasMenu,
      emptyCanvasMenuPosition,
      rightClickPosition,
      onCanvasContextMenu,
      onEmptyCanvasRightClick,
      closeEmptyCanvasMenu,
      handleAddNodeFromMenu,
      handlePasteFromMenu,
      handleAutoLayoutFromMenu: handleAutoLayout
    } = useCanvasEvents(canvasRef, canvasPosition, canvasScale, props.readonly, emit)
    
    // Mouse event handlers
    const onMouseDown = (event: MouseEvent) => {
      // Get the target element
      const target = event.target as HTMLElement
      
      // Check if clicked directly on the canvas or a background element
      // We need to exclude node-related elements and only allow background elements
      const isNode = target.closest('.node') !== null
      const isEdge = target.closest('.edge') !== null
      const isContextMenu = target.closest('.context-menu') !== null
      
      // Allow canvas dragging only if we clicked on the background
      if (!isNode && !isEdge && !isContextMenu) {
        // Send close relation selector event
        if (!props.readonly) {
          emit('close-relation-selector')
        }
        
        startCanvasDrag(event)
        
        // Prevent default
        event.preventDefault()
      }
    }
    
    const onMouseMove = (event: MouseEvent) => {
      // Handle canvas dragging
      if (isDraggingCanvas.value) {
        dragCanvas(event)
      }
      
      // Handle connecting nodes
      if (!props.readonly && isConnecting.value) {
        updateConnectingPosition(event)
      }
    }
    
    const onMouseUp = () => {
      endCanvasDrag()
    }
    
    // Toggle thumbnail visibility
    const toggleThumbnail = () => {
      showThumbnail.value = !showThumbnail.value
    }
    
    // Create empty node with proper position
    const createEmptyNode = () => {
      if (props.readonly) return

      console.log('创建新节点', nodes.value.length) // 添加日志帮助调试

      // 定义节点位置
      let position: { x: number; y: number }

      if (nodes.value.length === 0) {
        // 第一个节点 - 偏向左侧，为后续子节点预留空间
        // 参考自动布局中的ROOT_X和ROOT_Y常量
        position = {
          x: 200, // 偏向左侧，原来是400
          y: 300
        }

        // 重置画布位置和缩放，确保节点可见
        canvasStore.setPosition({ x: 0, y: 0 })
        canvasStore.setScale(1.0)
      } else {
        // 后续节点 - 使用当前视图中心计算位置
        const containerWidth = containerSize.value.width || 800
        const containerHeight = containerSize.value.height || 600

        position = {
          x: (containerWidth / 2 - canvasPosition.value.x) / canvasScale.value,
          y: (containerHeight / 2 - canvasPosition.value.y) / canvasScale.value
        }
      }

      // 发送创建节点事件
      emit('create-empty-node', position)

      // 关闭菜单（如果打开）
      if (showEmptyCanvasMenu.value) {
        closeEmptyCanvasMenu()
      }

      // 对于第一个节点，不应用自动布局，避免闪跳
      if (nodes.value.length === 0) {
        // 第一个节点不居中，保持偏左位置
        // 只需要确保节点在可视区域内即可
        setTimeout(() => {
          // 调整画布位置，使第一个节点在左侧1/4位置可见
          const containerWidth = containerSize.value.width || 800
          const containerHeight = containerSize.value.height || 600

          // 计算画布偏移，使节点显示在左侧1/4位置
          const targetCanvasX = -(position.x * canvasScale.value - containerWidth * 0.25)
          const targetCanvasY = -(position.y * canvasScale.value - containerHeight * 0.5)

          canvasStore.setPosition({
            x: targetCanvasX,
            y: targetCanvasY
          })
        }, 50)
      } else {
        // 后续节点才应用自动布局
        nextTick(() => {
          applyAutoLayout(true)

          setTimeout(() => {
            centerOnPosition(position)
          }, 100)
        })
      }
    }
    
    // Handle auto layout from menu
    const handleAutoLayoutFromMenu = () => {
      applyAutoLayout(true)
      closeEmptyCanvasMenu()
    }
    
    // Handle toggle children event
    const onToggleChildren = (edgeId: string) => {
      if (props.readonly) return

      console.log('切换子节点显示状态:', edgeId)

      // 获取连线信息
      const edge = edges.value.find(e => e.id === edgeId)
      if (!edge) return

      // 获取目标节点的所有子节点
      const targetNodeId = edge.target
      const childEdges = edges.value.filter(e => e.source === targetNodeId)

      if (childEdges.length > 0) {
        // 如果有子节点，将连线转换为临时状态（收缩）
        // 这里可以实现隐藏子节点的逻辑
        console.log('收缩子节点:', childEdges.map(e => e.target))

        // 暂时简单地将连线标记为临时状态
        // 实际实现中可能需要更复杂的状态管理
        // TODO: 实现完整的展开/收缩逻辑
      } else {
        // 如果没有子节点，可能需要创建新的子节点
        console.log('没有子节点，可能需要创建')
      }
    }

    // Compute the temp edge path
    const tempEdgePath = computed(() => getTempEdgePath())
    
    // Watch for node deletions
    watch(() => nodes.value.length, (newCount, oldCount) => {
      if (newCount < oldCount) {
        // Find the deleted node ID
        const oldNodeIds = new Set(nodes.value.map(node => node.id))
        const deletedNodeId = Array.from(oldNodeIds).find(id => !oldNodeIds.has(id))
        
        if (deletedNodeId) {
          // Notify parent
          emit('node-removed', deletedNodeId)
        }
      }
    })
    
    // Expose methods for external use
    const publicForceAdjustParentUncleSpacing = (parentId: string, childCount: number) => {
      console.log('外部调用强制调整父叔节点间距:', parentId, childCount)
      
      // Build tree
      const { nodeMap } = buildNodeTree()
      
      // Call internal method
      return forceAdjustParentUncleSpacing(nodeMap, parentId, childCount)
    }
    
    return {
      // Refs
      canvasRef,
      
      // Canvas state
      canvasScale,
      canvasPosition,
      canvasDimensions,
      containerSize,
      
      // Node state
      nodes,
      nodesWithDragState,
      selectedNodeId,
      draggedNodeId,
      
      // Edge state
      edges,
      selectedEdgeId,
      isConnecting,
      
      // UI state
      showThumbnail,
      showEmptyCanvasMenu,
      emptyCanvasMenuPosition,
      
      // Event handlers
      onMouseDown,
      onMouseMove,
      onMouseUp,
      handleZoom,
      onCanvasContextMenu,
      
      // Node operations
      getNodeById,
      selectNode,
      moveNode,
      handleDragStart,
      handleDragEnd,
      handleCopyNode,
      handleDeleteNode,
      
      // Edge operations
      selectEdge,
      startConnecting,
      finishConnecting,
      onSymbolClick,
      onConnectorClick,
      onToggleChildren,
      getTempEdgePath,
      tempEdgePath,
      
      // Relation handling
      handleAddRelation,
      handleShowAttribution,
      
      // Empty canvas handling
      createEmptyNode,
      onEmptyCanvasRightClick,
      closeEmptyCanvasMenu,
      handleAddNodeFromMenu,
      handlePasteFromMenu,
      handleAutoLayoutFromMenu,
      
      // Layout operations
      applyAutoLayout,
      optimizeNodeSpacing,
      
      // UI operations
      toggleThumbnail,
      
      // Public methods
      forceAdjustParentUncleSpacing: publicForceAdjustParentUncleSpacing
    }
  }
})
</script>

<style lang="scss" scoped>
.canvas {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
  background-color: #fff;
  background-image:
    linear-gradient(rgba(0, 0, 0, 0.05) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 0, 0, 0.05) 1px, transparent 1px);
  background-size: 20px 20px;
}
</style>
