# 画布节点创建和布局优化日志

## 优化目标
1. 优化第一个节点创建位置，偏向画布左侧为后续子节点预留空间
2. 修复节点创建时的闪动问题
3. 确保子节点布局满足对称要求（单子节点与父节点水平对齐，多子节点对称分布）
4. 解决同级节点重叠问题

## 优化策略
参考当前手动自动布局功能的逻辑和算法，将其应用到节点创建时的布局计算中。手动自动布局功能基本满足对称和不重叠要求，是之前优化的正面成果。

## 修改记录

### 2024-12-19 开始优化

### 第二轮优化 - 解决闪跳和间距问题

#### 1. 解决第一个节点闪跳问题 (Canvas.vue)
- 修改 `createEmptyNode` 函数逻辑，对第一个节点不应用自动布局
- 第一个节点直接居中到指定位置，避免位置重新计算导致的闪跳
- 保持第一个节点 x 坐标为 200，偏向左侧为后续子节点预留空间

#### 2. 减少同级节点垂直间距 (useLayoutCalculation.ts)
- SIBLING_VERTICAL_SPACING: 220 → 120
- COUSIN_VERTICAL_SPACING: 150 → 100
- MIN_VERTICAL_GAP: 120 → 80
- VERTICAL_GAP: 160 → 100
- TERMINAL_NODE_SPACING: 200 → 120
- CHILD_NODE_SPACING: 200 → 120

#### 3. 减少父子节点水平间距 (useLayoutCalculation.ts)
- FIXED_HORIZONTAL_SPACING: 380 → 280
- PARENT_CHILD_SPACING: 380 → 280
- 添加 nextTick 导入，优化布局应用时序，减少闪动效果
- 调整居中延迟时间从 50ms 改为 100ms，减少闪动

#### 2. 大幅简化子节点创建逻辑 (Editor.vue)
- 简化 `createRelatedNode` 函数，移除复杂的位置计算逻辑
- 使用与自动布局相同的 PARENT_CHILD_SPACING 常量 (380px，与FIXED_HORIZONTAL_SPACING一致)
- 移除所有手动重叠检测和位置调整代码（约200行复杂逻辑）
- 改为使用临时位置创建节点，然后调用 `applyAutoLayout(true)` 进行精确布局
- 延迟时间从 200ms 改为 100ms，提高响应速度

#### 3. 统一布局常量 (useLayoutCalculation.ts)
- 调整 ROOT_X 从 100 改为 200，与Canvas.vue中第一个节点位置保持一致
- 调整 ROOT_Y 从 150 改为 300，与Canvas.vue中第一个节点位置保持一致
- 确保手动自动布局与节点创建时的位置计算完全一致

## 优化总结

### 核心改进
1. **统一布局逻辑**：将节点创建时的布局计算统一到手动自动布局功能中，避免了两套不同的布局算法导致的不一致问题
2. **简化代码复杂度**：移除了约200行复杂的手动位置计算和重叠检测代码，大幅降低了维护成本
3. **提升用户体验**：减少了节点创建时的闪动效果，优化了第一个节点的默认位置

### 技术优势
- **代码复用**：充分利用了已经优化好的自动布局算法
- **一致性保证**：确保所有节点布局都遵循相同的对称和不重叠原则
- **性能优化**：减少了重复的位置计算，提高了响应速度

### 预期效果
- 第一个节点创建在画布左侧，为后续子节点预留充足空间
- 单子节点与父节点完美水平中心对齐
- 多子节点以父节点为中心对称分布
- 消除节点创建时的闪动问题
- 解决同级节点重叠问题

## 技术细节

### 修改的文件
1. **src/components/Canvas/Canvas.vue**
   - 优化 `createEmptyNode` 函数
   - 调整第一个节点位置从 (400, 300) 到 (200, 300)
   - 优化布局应用时序，减少闪动

2. **src/views/Editor.vue**
   - 大幅简化 `createRelatedNode` 函数
   - 移除约200行复杂的手动布局计算代码
   - 统一使用 PARENT_CHILD_SPACING = 380px
   - 改为依赖自动布局算法

3. **src/composables/canvas/useLayoutCalculation.ts**
   - 调整 ROOT_X 和 ROOT_Y 常量与节点创建位置保持一致
   - 确保布局算法的一致性

### 关键优化策略
- **统一布局逻辑**：所有节点布局都使用同一套自动布局算法
- **简化创建流程**：节点创建时使用临时位置，然后调用自动布局进行精确定位
- **减少代码重复**：移除重复的位置计算和重叠检测逻辑
- **提升性能**：减少不必要的计算和DOM操作

### 测试建议
1. 创建第一个节点，验证位置是否偏向左侧
2. 为第一个节点添加单个子节点，验证是否水平对齐
3. 为第一个节点添加多个子节点，验证是否对称分布
4. 创建多层级节点结构，验证是否无重叠
5. 观察节点创建过程，验证是否减少了闪动效果
