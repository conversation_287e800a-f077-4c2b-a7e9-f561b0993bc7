# 树形节点布局规则文档

**日期**: 2025年5月26日

## 1. 概述

本文档详细描述了树形可视化系统中节点布局的规则和优化策略。这些规则旨在确保节点布局清晰、美观、符合逻辑，并避免节点重叠和布局混乱。

## 2. 核心布局原则

### 2.1 水平对齐原则

1. **父子节点水平对齐** - 子节点的中心点应与其父节点的中心点水平对齐，确保视觉上的连贯性。
2. **固定水平间距** - 子节点应位于父节点右侧，并保持固定的水平间距（`FIXED_HORIZONTAL_SPACING`），确保布局的一致性。

### 2.2 垂直分布原则

1. **多子节点对称分布** - 当父节点有多个子节点时，这些子节点应在父节点的垂直中心线两侧对称分布。
2. **垂直间距一致** - 同级子节点之间应保持一致的垂直间距（`VERTICAL_GAP`），确保布局的规律性。
3. **终端节点间距** - 终端节点（叶子节点）之间应保持适当的垂直间距（`TERMINAL_NODE_SPACING`），避免视觉拥挤。

### 2.3 避免重叠原则

1. **节点安全边距** - 每个节点周围应有安全边距，确保节点之间不会过于接近。
2. **重叠检测与解决** - 系统应能检测并自动解决节点重叠问题，确保所有节点都清晰可见。
3. **最小间距保证** - 节点之间应保持最小水平间距（`MIN_HORIZONTAL_GAP`）和最小垂直间距（`MIN_VERTICAL_DISTANCE`）。

### 2.4 层级一致性原则

1. **同深度节点水平对齐** - 同一深度的节点应尽量保持相同的水平位置，形成清晰的层级结构。
2. **层级间距递增** - 随着树的深度增加，层级之间的水平间距可适当增加，以适应更复杂的结构。

### 2.5 全局协调原则

1. **全局优化** - 在局部优化后，应进行全局布局检查和调整，确保整体布局的和谐。
2. **连锁反应处理** - 当移动一个节点时，应考虑并处理可能的连锁反应，确保不会导致新的布局问题。

## 3. 具体实现策略

### 3.1 自动布局算法

1. **`applyAutoLayout`函数** - 实现自动布局的主函数，协调各种布局策略的应用。
2. **布局触发条件** - 在节点添加、移动或连接关系改变时触发自动布局。
3. **强制布局选项** - 提供`force`参数，允许在必要时强制应用自动布局。

### 3.2 子树平衡

1. **`balanceSubtrees`函数** - 确保子树在垂直方向上居中对齐。
2. **递归平衡** - 从根节点开始递归地平衡每个子树，确保整体结构的平衡。
3. **子节点位置计算** - 基于子树的高度和节点数量计算最佳位置。

### 3.3 节点间距优化

1. **`optimizeNodeSpacing`函数** - 检测并解决节点重叠问题。
2. **重叠检测算法** - 使用精确的边界计算检测节点重叠。
3. **自适应调整** - 根据节点关系（父子关系或无关系）应用不同的间距规则。
4. **迭代优化** - 多次迭代检查和调整，确保所有重叠都被解决。

### 3.4 边缘路径优化

1. **`optimizeEdgePaths`函数** - 优化连接线的路径，确保视觉清晰。
2. **曲线控制点** - 根据节点之间的距离和相对位置计算最佳的贝塞尔曲线控制点。
3. **特殊情况处理** - 为水平对齐、垂直对齐等特殊情况提供专门的路径计算。

### 3.5 全局协调机制

1. **`applyGlobalCoordination`函数** - 应用全局协调机制，确保整体布局的一致性。
2. **深度分组** - 将节点按深度分组，确保同深度节点的水平一致性。
3. **父叔节点间距** - 特别关注父节点和叔叔节点之间的间距，确保布局的清晰度。

## 4. 布局常量

以下是系统中使用的关键布局常量：

| 常量名称 | 值 | 描述 |
|---------|-----|------|
| `NODE_WIDTH` | 240 | 节点的标准宽度（像素） |
| `NODE_HEIGHT` | 90 | 节点的标准高度（像素） |
| `VERTICAL_GAP` | 40 | 节点之间的标准垂直间距（像素） |
| `FIXED_HORIZONTAL_SPACING` | 300 | 父子节点之间的固定水平间距（像素） |
| `MIN_HORIZONTAL_GAP` | 80 | 节点之间的最小水平间距（像素） |
| `TERMINAL_NODE_SPACING` | 40 | 终端节点之间的最小垂直间距（像素） |
| `CHILD_NODE_SPACING` | 60 | 子节点之间的最小垂直间距（像素） |
| `MIN_VERTICAL_DISTANCE` | 20 | 节点之间的最小垂直距离（像素） |

## 5. 优化功能

### 5.1 重叠检测与解决

1. **安全边距计算** - 为每个节点添加安全边距，增强重叠检测的准确性。
2. **同列节点处理** - 特别关注X坐标接近的节点，应用垂直间距规则。
3. **不同列节点处理** - 对于不在同一列但仍有重叠的节点，应用水平间距规则。

### 5.2 爷孙节点对称

1. **爷爷节点居中** - 确保爷爷节点位于其所有子节点的垂直中心。
2. **孙子节点水平对齐** - 确保孙子节点与其父节点水平对齐，并保持适当的水平间距。
3. **曾孙节点处理** - 递归地应用相同的规则到更深层次的节点。

### 5.3 动态间距调整

1. **基于子节点数量** - 根据子节点的数量动态调整父节点与其兄弟节点之间的间距。
2. **预留空间** - 为可能添加的新节点预留空间，减少未来布局变化的幅度。
3. **连锁反应传导** - 当调整一个节点位置时，将变化向上传导到祖先节点，确保整体布局的一致性。

## 6. 使用说明

### 6.1 自动布局控制

1. **启用/禁用** - 通过`autoLayoutEnabled`标志控制自动布局功能。
2. **强制应用** - 在需要时可以通过`applyAutoLayout(true)`强制应用自动布局。
3. **重叠优化控制** - 通过`AUTO_OPTIMIZE_OVERLAPS`标志控制是否自动优化节点重叠。

### 6.2 手动调整后的处理

1. **保存原始位置** - 在布局调整前保存节点的原始位置，以便计算相对变化。
2. **检测变化** - 通过`checkNodePositionChanges`函数检测节点位置的变化。
3. **最小化干扰** - 尽量保持用户手动调整的相对位置关系，减少自动布局的干扰。

## 7. 未来优化方向

1. **性能优化** - 对大型树结构进行性能优化，减少布局计算的时间复杂度。
2. **自适应间距** - 根据树的大小和复杂度自动调整间距参数，实现更智能的布局。
3. **用户自定义规则** - 允许用户定义自己的布局规则和参数，增强系统的灵活性。
4. **布局动画** - 添加平滑的过渡动画，使布局变化更加直观和美观。
5. **布局模板** - 提供多种预设布局模板，适应不同类型的树形结构。

---

本文档将随着系统的发展和优化而更新，确保布局规则始终与最新的实现保持一致。
