<template>
  <div class="editor-container">
    <!-- 顶部导航栏 -->
    <div class="header-bar">
      <div class="left-section">
        <button class="back-btn">
          <i class="icon">←</i>
          <span>返回</span>
        </button>
        <span class="title">模型名称</span>
        <i class="edit-icon">✎</i>
      </div>
      <div class="right-section">
        <button class="btn-default" @click="togglePreviewMode">预览</button>
        <button class="btn-primary" @click="saveModel">保存</button>
      </div>
    </div>

    <!-- 工具栏 -->
    <div class="toolbar">
      <div class="date-picker">
        <span class="label">数据时间:</span>
        <input type="text" class="date-input" value="2025-04-26" />
      </div>
      <div class="date-picker">
        <span class="label">对比时间:</span>
        <input type="text" class="date-input" value="2025-03-27" />
      </div>
      <button class="btn-default batch-config">批量设置</button>

      <!-- 添加自动布局按钮 -->
      <div class="layout-tools">
        <button class="btn-default" @click="applyAutoLayout">
          <span class="icon">⟲</span>
          自动布局
        </button>
      </div>
    </div>

    <div class="main-content">
      <!-- 中间画布区域 -->
      <div class="canvas-container">
        <Canvas
          ref="canvasRef"
          @add-relation="showIndicatorSelector"
          @create-node="createRelatedNode"
          @connector-click="showConnectorIndicatorSelector"
          @node-removed="handleNodeRemoved"
          @close-relation-selector="() => showRelationSelector = false"
          @create-empty-node="createEmptyNode"
        />
      </div>

      <!-- 右侧配置面板 - 只在选中节点或连线时显示 -->
      <div class="config-panel" v-if="selectedNodeId || selectedEdgeId">
        <ConfigPanel @node-removed="handleNodeRemoved" />
      </div>
    </div>

    <!-- 指标选择弹窗 -->
    <div class="modal" v-if="showSelector">
      <div class="modal-backdrop" @click="closeSelector"></div>
      <div class="modal-content">
        <div class="modal-header">
          <h3>选择指标</h3>
          <button class="close-btn" @click="closeSelector">×</button>
        </div>
        <div class="modal-body">
          <IndicatorSelector
            :source-node-id="currentSourceNodeId"
            :relation-type="currentRelationType"
            @select="createRelatedNode"
          />
        </div>
      </div>
    </div>

    <!-- 计算关系选择弹窗 -->
    <div class="relation-selector-popup" v-if="showRelationSelector" :style="{ top: relationSelectorPosition.top, left: relationSelectorPosition.left }">
      <RelationTypeSelector
        :edge-id="currentEdgeId"
        :current-type="currentEdgeType"
        @select="updateEdgeRelation"
      />
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, computed, ref } from 'vue'
import { useRouter } from 'vue-router'
import { useNodeStore } from '../stores/node'
import { useEdgeStore } from '../stores/edge'
import type { Position } from '../types'
import Canvas from '../components/Canvas/Canvas.vue'
import ConfigPanel from '../components/ConfigPanel/ConfigPanel.vue'
import IndicatorSelector from '../components/IndicatorSelector/IndicatorSelector.vue'
import RelationTypeSelector from '../components/RelationTypeSelector/RelationTypeSelector.vue'

export default defineComponent({
  name: 'Editor',
  components: {
    Canvas,
    ConfigPanel,
    IndicatorSelector,
    RelationTypeSelector
  },
  setup() {
    const router = useRouter()
    const nodeStore = useNodeStore()
    const edgeStore = useEdgeStore()
    const canvasRef = ref(null)

    // 初始化CSS变量
    document.documentElement.style.setProperty('--relation-popup-triangle-top', '115px')

    const selectedNodeId = computed(() => nodeStore.selectedNodeId)
    const selectedEdgeId = computed(() => edgeStore.selectedEdgeId)
    const modelName = ref('销售业绩分析')

    // 指标选择器状态
    const showSelector = ref(false)
    const currentSourceNodeId = ref('')
    const currentRelationType = ref<any>(null)
    const connectorPosition = ref<any>(null)

    // 计算关系选择器状态
    const showRelationSelector = ref(false)
    const relationSelectorPosition = ref<{ top: string; left: string; position?: string }>({ top: '0px', left: '0px' })
    const currentEdgeId = ref('')
    const currentEdgeType = ref('')

    // 初始化一个根节点
    const initRootNode = () => {
      if (nodeStore.nodes.length === 0) {
        // 添加主节点 - 位置更靠近画布上方
        const rootNodeId = nodeStore.addNode({
          id: '',
          type: 'kpi',
          position: { x: 100, y: 80 }, // 减少Y坐标，使节点更靠近顶部
          data: {
            label: '零售价销售额',
            value: '49,194.473916',
            change: '+2.71%',
            period: '对比上周',
            color: '#1890ff',
            hasAttribution: true,
            attributionData: [
              {
                id: '1',
                name: '线上销售',
                value: 28563.25,
                percentage: 58,
                contribution: 1.8
              },
              {
                id: '2',
                name: '线下门店',
                value: 15642.75,
                percentage: 32,
                contribution: 0.6
              },
              {
                id: '3',
                name: '第三方渠道',
                value: 4988.48,
                percentage: 10,
                contribution: 0.3
              }
            ]
          }
        })

        // 添加子节点
        // 计算父节点中心点X坐标
        const rootNodeWidth = 240 // 根节点默认宽度
        const childNodeWidth = 240 // 子节点默认宽度
        const rootCenterX = 100 + rootNodeWidth / 2 // 根节点中心X坐标
        const horizontalOffset = 350 // 水平偏移量

        // 计算子节点位置，使其中心与父节点中心水平对齐，然后向右偏移
        // 确保子节点的中心点与父节点的中心点水平对齐
        // 子节点X坐标 = 父节点中心点X坐标 - 子节点宽度/2 + 水平偏移量
        const childNodeX = rootCenterX - childNodeWidth / 2 + horizontalOffset

        // 使用与父节点相同的Y坐标，确保完全水平对齐
        const childNodeY = 80 // 与父节点使用相同的Y坐标，保持在画布上方

        const childNodeId = nodeStore.addNode({
          id: '',
          type: 'input',
          position: { x: childNodeX, y: childNodeY }, // 使用计算后的X坐标，与父节点在同一水平线上
          data: {
            label: '线上销售额',
            value: '28,563.25',
            change: '+3.42%',
            period: '对比上周',
            color: '#52c41a'
          }
        })

        // 创建连线
        edgeStore.addEdge({
          id: '',
          source: rootNodeId,
          target: childNodeId,
          label: '+',
          type: 'add'
        })
      }
    }

    // 显示指标选择器
    const showIndicatorSelector = (nodeId: string, relationType: any) => {
      // 如果是从计算符号点击过来的，显示计算关系选择器
      if (relationType && relationType.name === '修改关系') {
        // 获取连线信息
        const edge = edgeStore.edges.find(e => e.source === nodeId && e.label === relationType.icon)
        if (edge) {
          // 使用传递的精确符号位置和画布信息
          const symbolInfo = relationType.symbolInfo
          showRelationSelectorForEdge(edge.id, edge.type || 'related', symbolInfo)
          return
        }
      }

      // 如果是从节点的加号按钮点击过来的，创建一个只有计算关系符号的连线
      if (relationType && relationType.name !== '修改关系') {
        // 保存当前选中的源节点和关系类型，以便后续创建完整的连线
        currentSourceNodeId.value = nodeId
        currentRelationType.value = relationType

        // 获取源节点
        const sourceNode = nodeStore.getNodeById(nodeId)
        if (!sourceNode) return

        // 创建一个临时的目标节点ID，用于创建只有计算关系符号的连线
        // 这个ID不会对应到实际的节点，只是为了创建连线
        const tempTargetId = 'temp-target-' + Date.now()

        // 创建连线
        const edgeId = edgeStore.addEdge({
          id: '',
          source: nodeId,
          target: tempTargetId, // 使用临时ID
          label: relationType.icon,
          type: relationType.id,
          isTemporary: true // 标记为临时连线
        })

        // 不显示指标选择器，等待用户点击连线
        return
      }

      // 否则显示指标选择器（这种情况不应该发生在新的流程中）
      currentSourceNodeId.value = nodeId
      currentRelationType.value = relationType
      connectorPosition.value = null
      showSelector.value = true
    }

    // 显示连接器指标选择器
    const showConnectorIndicatorSelector = (nodeId: string, relationType: string, position: any) => {
      // 获取源节点的所有连线
      const edges = edgeStore.getEdgesBySource(nodeId)

      // 查找临时连线
      const tempEdge = edges.find(edge => edge.isTemporary)

      // 设置当前源节点和关系类型
      currentSourceNodeId.value = nodeId
      currentRelationType.value = {
        id: relationType,
        name: getRelationName(relationType),
        icon: getRelationIcon(relationType)
      }
      connectorPosition.value = position

      // 显示指标选择器
      showSelector.value = true
    }

    // 显示计算关系选择器
    const showRelationSelectorForEdge = (edgeId: string, edgeType: string, symbolInfo?: any) => {
      currentEdgeId.value = edgeId
      currentEdgeType.value = edgeType

      // 弹窗尺寸常量
      const POPUP_TRIANGLE_POSITION = 115; // 弹窗三角形位置

      // 如果传入了符号信息，使用屏幕坐标直接定位弹窗
      if (symbolInfo && symbolInfo.screenX !== undefined && symbolInfo.screenY !== undefined) {
        // 使用屏幕坐标直接定位弹窗
        const popupTop = symbolInfo.screenY - POPUP_TRIANGLE_POSITION;
        // 增加水平间距，使弹窗往右移
        const horizontalOffset = 25; // 增加25px的水平间距
        const popupLeft = symbolInfo.screenX + horizontalOffset;

        // 设置弹窗位置 - 使用屏幕坐标，确保弹窗始终出现在符号右侧
        relationSelectorPosition.value = {
          top: `${popupTop}px`,
          left: `${popupLeft}px`
        };

        // 使用CSS变量设置定位方式
        document.documentElement.style.setProperty('--relation-popup-position', 'fixed');

        // 更新弹窗三角形位置，使其指向符号中心
        document.documentElement.style.setProperty('--relation-popup-triangle-top', `${POPUP_TRIANGLE_POSITION}px`);
      } else {
        // 兼容旧代码，使用画布坐标计算
        const edge = edgeStore.getEdgeById(edgeId)
        if (!edge) return

        const sourceNode = nodeStore.getNodeById(edge.source)
        const targetNode = nodeStore.getNodeById(edge.target)
        if (!sourceNode || !targetNode) return

        // 计算计算符号的位置 - 与Edge.vue组件中的symbolPosition计算保持一致
        const symbolX = sourceNode.position.x + (sourceNode.size?.width || 240) + 60
        const symbolY = sourceNode.position.y + (sourceNode.size?.height || 90) / 2

        // 增加水平间距，使弹窗往右移
        const horizontalOffset = 25; // 增加25px的水平间距

        // 设置选择器位置 - 调整位置使其出现在计算符号的右侧，稍微右移一点
        relationSelectorPosition.value = {
          top: `${symbolY - POPUP_TRIANGLE_POSITION}px`,
          left: `${symbolX + horizontalOffset}px`
        }

        // 使用CSS变量设置定位方式
        document.documentElement.style.setProperty('--relation-popup-position', 'absolute')
      }

      // 显示选择器
      showRelationSelector.value = true

      // 添加点击外部关闭选择器的事件
      setTimeout(() => {
        document.addEventListener('click', closeRelationSelector)
      }, 0)
    }

    // 关闭计算关系选择器
    const closeRelationSelector = (event?: MouseEvent) => {
      // 如果有事件参数，检查点击是否在弹窗内部
      if (event) {
        const target = event.target as HTMLElement
        // 如果点击在弹窗内部，不关闭弹窗
        if (target.closest('.relation-selector-popup')) return
      }

      // 关闭弹窗并移除事件监听器
      showRelationSelector.value = false
      document.removeEventListener('click', closeRelationSelector)
    }

    // 更新连线关系类型
    const updateEdgeRelation = (data: { edgeId: string, relationType: { id: string, name: string, icon: string } }) => {
      const edge = edgeStore.getEdgeById(data.edgeId)
      if (!edge) return

      // 更新连线类型和标签
      edgeStore.updateEdge(data.edgeId, {
        ...edge,
        type: data.relationType.id,
        label: data.relationType.icon
      })

      // 关闭选择器
      closeRelationSelector()
    }

    // 获取关系名称
    const getRelationName = (relationType: string) => {
      const relationMap: Record<string, string> = {
        'add': '相加关系',
        'subtract': '相减关系',
        'multiply': '相乘关系',
        'divide': '相除关系',
        'related': '相关关系'
      }
      return relationMap[relationType] || '相关关系'
    }

    // 获取关系图标
    const getRelationIcon = (relationType: string) => {
      const iconMap: Record<string, string> = {
        'add': '+',
        'subtract': '-',
        'multiply': '×',
        'divide': '÷',
        'related': 'C'
      }
      return iconMap[relationType] || 'C'
    }

    // 关闭指标选择器
    const closeSelector = () => {
      showSelector.value = false
    }

    // 创建空节点
    const createEmptyNode = (position: Position) => {
      // 创建一个新的根节点
      const rootNodeId = nodeStore.addNode({
        id: '',
        type: 'kpi',
        position: position,
        data: {
          label: '新建指标',
          value: '0',
          change: '0%',
          period: '对比上周',
          color: '#1890ff'
        }
      })

      // 选中新创建的节点
      nodeStore.selectNode(rootNodeId)
    }

    // 创建关联节点
    const createRelatedNode = (data: {
      sourceNodeId: string,
      relationType: { id: string, name: string, icon: string },
      indicator: any
    }) => {
      // 获取源节点
      const sourceNode = nodeStore.getNodeById(data.sourceNodeId)
      if (!sourceNode) return

      // 节点尺寸常量
      const NODE_WIDTH = sourceNode.size?.width || 240
      const NODE_HEIGHT = sourceNode.size?.height || 90
      const CHILD_NODE_WIDTH = 240 // 子节点默认宽度
      const VERTICAL_SPACING = 70 // 子节点之间的垂直间距 (大幅减少间距，从90改为70)
      const MIN_VERTICAL_GAP = 40 // 节点之间的最小垂直间距 (大幅减少间距，从60改为40)

      // 计算父节点中心点X坐标
      const parentCenterX = sourceNode.position.x + NODE_WIDTH / 2

      // 水平偏移量 - 父节点到子节点的水平距离
      const horizontalOffset = 350

      // 计算所有子节点应该共用的X坐标（水平对齐）
      // 确保子节点的中心点与父节点的中心点水平对齐
      // 子节点X坐标 = 父节点中心点X坐标 - 子节点宽度/2 + 水平偏移量
      const childrenX = parentCenterX - CHILD_NODE_WIDTH / 2 + horizontalOffset

      // 构建节点关系树，用于全局布局调整
      const buildNodeTree = () => {
        const nodeMap = new Map<string, { node: any, children: string[], parent: string | null }>()

        // 初始化节点映射
        nodeStore.nodes.forEach(node => {
          nodeMap.set(node.id, { node, children: [], parent: null })
        })

        // 构建父子关系
        edgeStore.edges.forEach(edge => {
          const sourceNode = nodeMap.get(edge.source)
          const targetNode = nodeMap.get(edge.target)

          if (sourceNode && targetNode) {
            sourceNode.children.push(edge.target)
            targetNode.parent = edge.source
          }
        })

        // 找出根节点（没有父节点的节点）
        const rootNodes: string[] = []
        nodeStore.nodes.forEach(node => {
          const nodeInfo = nodeMap.get(node.id)
          if (nodeInfo && nodeInfo.parent === null) {
            rootNodes.push(node.id)
          }
        })

        return { nodeMap, rootNodes }
      }

      // 获取父节点的所有现有子节点
      const existingChildNodes = edgeStore.edges
        .filter(edge => edge.source === data.sourceNodeId)
        .map(edge => nodeStore.getNodeById(edge.target))
        .filter(Boolean)

      // 计算新节点位置
      let newPosition: Position = { x: 0, y: 0 }

      if (connectorPosition.value) {
        // 如果是从连接器创建的节点，使用连接器位置作为参考
        newPosition = {
          x: childrenX,
          y: connectorPosition.value.y
        }
      } else {
        // 如果父节点没有子节点（当前要添加的是第一个子节点）
        if (existingChildNodes.length <= 0) {
          // 如果是第一个子节点，直接与父节点水平中心对齐
          newPosition = {
            x: childrenX,
            y: sourceNode.position.y // 与父节点在同一水平线上
          }
        } else {
          // 如果已经有子节点，需要计算合适的垂直位置，确保对称分布

          // 1. 收集所有现有子节点的Y坐标
          const childrenYPositions = existingChildNodes.map(node => node.position.y)

          // 2. 计算现有子节点的垂直范围
          const minY = Math.min(...childrenYPositions)
          const maxY = Math.max(...childrenYPositions)

          // 3. 计算新节点的Y坐标
          let newY: number = 0

          // 优化节点位置计算，使节点更靠近画布上方
          if (existingChildNodes.length % 2 === 1) {
            // 如果现有子节点数量为奇数，优先放在上方
            const potentialTopY = minY - VERTICAL_SPACING * 0.5 // 大幅减少向下偏移量

            // 检查上方位置是否有足够空间（不会超出画布上边界）
            if (potentialTopY >= 30) { // 进一步减少上边距，允许节点更靠近顶部
              newY = potentialTopY
            } else {
              // 如果上方空间不足，则放在下方，但极大减少垂直间距
              newY = maxY + VERTICAL_SPACING * 0.4 // 大幅减少向下偏移量
            }
          } else {
            // 如果现有子节点数量为偶数，优先放在上方，保持对称
            newY = minY - VERTICAL_SPACING * 0.5 // 大幅减少向下偏移量
          }

          // 4. 检查新位置是否与其他节点重叠
          const allNodes = nodeStore.nodes
          let isOverlapping = true
          let offsetMultiplier = 1

          while (isOverlapping) {
            isOverlapping = allNodes.some(node =>
              node.id !== data.sourceNodeId && // 不是父节点
              Math.abs(node.position.x - childrenX) < CHILD_NODE_WIDTH && // X方向接近
              Math.abs(node.position.y - newY) < NODE_HEIGHT // Y方向接近
            )

            if (isOverlapping) {
              // 如果重叠，优先尝试向上移动
              const potentialTopY = minY - VERTICAL_SPACING * 0.5 * offsetMultiplier // 大幅减少向下偏移量

              // 检查上方位置是否有足够空间且不与其他节点重叠
              if (potentialTopY >= 30 && !allNodes.some(node => // 进一步减少上边距限制
                  node.id !== data.sourceNodeId && // 不是父节点
                  Math.abs(node.position.x - childrenX) < CHILD_NODE_WIDTH && // X方向接近
                  Math.abs(node.position.y - potentialTopY) < NODE_HEIGHT // Y方向接近
                )) {
                newY = potentialTopY
              } else {
                // 如果上方不可行，则向下移动，但极大减少垂直间距
                newY = maxY + VERTICAL_SPACING * 0.4 * offsetMultiplier // 大幅减少向下偏移量
              }
              offsetMultiplier++
            }
          }

          newPosition = {
            x: childrenX,
            y: newY
          }
        }
      }

      // 最后检查：确保新节点不与任何现有节点重叠
      const allNodes = nodeStore.nodes
      let finalPosition = { ...newPosition }
      let isOverlapping = true
      let verticalOffset = 0
      let tryUpFirst = true // 首先尝试向上移动，更积极地向上放置节点

      // 首先尝试将节点放在父节点的正上方，如果可能的话
      if (sourceNode.position.y >= NODE_HEIGHT + 30) { // 确保有足够空间
        const aboveParentY = sourceNode.position.y - NODE_HEIGHT - 30;
        const aboveParentPosition = {
          x: finalPosition.x,
          y: aboveParentY
        };

        // 检查此位置是否有重叠
        const hasOverlap = allNodes.some(node =>
          node.id !== data.sourceNodeId && // 不是父节点
          Math.abs(node.position.x - aboveParentPosition.x) < CHILD_NODE_WIDTH &&
          Math.abs(node.position.y - aboveParentPosition.y) < NODE_HEIGHT
        );

        if (!hasOverlap) {
          // 如果没有重叠，直接使用此位置
          finalPosition = aboveParentPosition;
          isOverlapping = false;
        }
      }

      // 如果不能放在父节点上方，继续常规检查
      while (isOverlapping) {
        isOverlapping = allNodes.some(node =>
          node.id !== data.sourceNodeId && // 不是父节点
          Math.abs(node.position.x - finalPosition.x) < CHILD_NODE_WIDTH &&
          Math.abs(node.position.y - finalPosition.y) < NODE_HEIGHT
        )

        if (isOverlapping) {
          if (tryUpFirst && newPosition.y - NODE_HEIGHT >= 30) { // 进一步减少上边距限制
            // 首先尝试向上移动，使用更小的偏移量
            verticalOffset -= NODE_HEIGHT * 0.7 // 减小向上移动的步长，使节点更靠近
            finalPosition.y = newPosition.y + verticalOffset

            // 检查向上移动后是否仍然重叠
            const stillOverlapping = allNodes.some(node =>
              node.id !== data.sourceNodeId && // 不是父节点
              Math.abs(node.position.x - finalPosition.x) < CHILD_NODE_WIDTH &&
              Math.abs(node.position.y - finalPosition.y) < NODE_HEIGHT
            )

            if (stillOverlapping) {
              // 如果向上移动后仍然重叠，尝试更多次向上移动
              // 只有在尝试了多次向上移动后仍然重叠，才改为向下移动
              if (verticalOffset <= -NODE_HEIGHT * 3.5) { // 增加向上尝试次数到4次
                // 恢复位置并改为向下移动
                verticalOffset = 0
                finalPosition.y = newPosition.y
                tryUpFirst = false
              }
              // 否则继续下一次循环，尝试更多向上移动
            } else {
              // 如果向上移动后不再重叠，跳出循环
              isOverlapping = false
            }
          } else {
            // 如果不能向上移动或已尝试过向上移动，则向下移动，但使用更小的步长
            verticalOffset += NODE_HEIGHT * 0.4 // 极大减少垂直间距
            finalPosition.y = newPosition.y + verticalOffset
          }
        }
      }

      // 检查父节点是否有父节点（即当前要添加的是第三级或更深的节点）
      const { nodeMap } = buildNodeTree()
      const parentInfo = nodeMap.get(data.sourceNodeId)

      // 全局布局调整函数 - 递归调整节点位置
      const adjustNodePositions = (nodeId: string, offsetY: number) => {
        // 移动当前节点
        const node = nodeStore.getNodeById(nodeId)
        if (!node) return

        nodeStore.moveNode(nodeId, {
          x: node.position.x,
          y: node.position.y + offsetY
        })

        // 移动所有子节点
        const nodeInfo = nodeMap.get(nodeId)
        if (!nodeInfo) return

        nodeInfo.children.forEach(childId => {
          adjustNodePositions(childId, offsetY)
        })
      }

      // 检查节点是否与其他节点重叠
      const checkNodeOverlap = (position: { x: number, y: number }, excludeNodeId: string) => {
        return nodeStore.nodes.some(node =>
          node.id !== excludeNodeId &&
          Math.abs(node.position.x - position.x) < CHILD_NODE_WIDTH &&
          Math.abs(node.position.y - position.y) < NODE_HEIGHT
        )
      }

      // 计算节点的深度
      const calculateNodeDepth = (nodeId: string): number => {
        const nodeInfo = nodeMap.get(nodeId)
        if (!nodeInfo || !nodeInfo.parent) return 0

        return 1 + calculateNodeDepth(nodeInfo.parent)
      }

      // 如果是第三级或更深的节点，需要特别处理
      if (parentInfo && parentInfo.parent) {
        // 获取祖父节点信息
        const grandparentInfo = nodeMap.get(parentInfo.parent)

        if (grandparentInfo) {
          // 获取父节点的所有兄弟节点（叔叔节点）
          const uncleNodeIds = grandparentInfo.children.filter(id => id !== data.sourceNodeId)

          // 获取所有表兄弟节点（叔叔的子节点）
          const cousinNodes: any[] = []

          uncleNodeIds.forEach(uncleId => {
            const uncleInfo = nodeMap.get(uncleId)
            if (uncleInfo) {
              uncleInfo.children.forEach(cousinId => {
                const cousinNode = nodeStore.getNodeById(cousinId)
                if (cousinNode) cousinNodes.push(cousinNode)
              })
            }
          })

          // 检查新节点位置是否与任何表兄弟节点重叠
          const overlappingNodes = cousinNodes.filter(cousinNode =>
            Math.abs(cousinNode.position.x - finalPosition.x) < CHILD_NODE_WIDTH &&
            Math.abs(cousinNode.position.y - finalPosition.y) < NODE_HEIGHT
          )

          // 如果有重叠，需要全局调整节点位置
          if (overlappingNodes.length > 0) {
            // 计算需要的垂直偏移量
            const maxOverlapY = Math.max(...overlappingNodes.map(node => node.position.y + NODE_HEIGHT))
            const minNewY = maxOverlapY + MIN_VERTICAL_GAP

            // 如果新位置需要更低，调整父节点及其所有子节点
            if (minNewY > finalPosition.y) {
              // 计算需要的偏移量
              const offsetY = minNewY - finalPosition.y

              // 移动父节点及其所有子节点
              adjustNodePositions(data.sourceNodeId, offsetY)

              // 更新新节点的最终位置
              finalPosition.y += offsetY
            }

            // 检查调整后是否仍有重叠
            if (checkNodeOverlap(finalPosition, data.sourceNodeId)) {
              // 如果仍有重叠，尝试调整祖父节点及其所有子节点
              const grandparentNode = nodeStore.getNodeById(parentInfo.parent)
              if (grandparentNode) {
                // 计算新的偏移量
                const additionalOffset = NODE_HEIGHT + MIN_VERTICAL_GAP

                // 移动祖父节点及其所有子节点
                adjustNodePositions(parentInfo.parent, additionalOffset)

                // 更新新节点的最终位置
                finalPosition.y += additionalOffset
              }
            }
          }
        }
      }

      // 对于所有节点，确保新节点不与任何现有节点重叠
      // 首先尝试将节点放在父节点的正上方或附近
      const parentY = sourceNode.position.y;

      // 尝试在父节点上方找到合适位置
      if (parentY >= NODE_HEIGHT + 20) { // 确保有足够空间
        // 尝试多个位置，从父节点正上方开始
        const potentialPositions = [
          { x: finalPosition.x, y: parentY - NODE_HEIGHT - 20 }, // 正上方
          { x: finalPosition.x, y: parentY - NODE_HEIGHT - 40 }, // 再上一点
          { x: finalPosition.x, y: parentY - NODE_HEIGHT - 60 }, // 更上一点
          { x: finalPosition.x, y: parentY - NODE_HEIGHT * 0.5 - 10 }, // 稍微靠近一点
        ];

        // 检查这些位置是否有合适的
        for (const pos of potentialPositions) {
          if (!checkNodeOverlap(pos, data.sourceNodeId)) {
            finalPosition = pos;
            // 找到合适位置，跳出循环
            break;
          }
        }
      }

      // 如果上方没有合适位置，尝试向上移动几次
      let upAttempts = 0;
      const maxUpAttempts = 5; // 增加尝试次数

      // 先尝试向上移动几次
      while (checkNodeOverlap(finalPosition, data.sourceNodeId) && upAttempts < maxUpAttempts) {
        // 向上移动，但确保不会超出画布上边界
        if (finalPosition.y - NODE_HEIGHT * 0.6 >= 30) { // 减少步长和边界限制
          finalPosition.y -= NODE_HEIGHT * 0.6;
          upAttempts++;
        } else {
          // 如果向上移动会超出边界，跳出循环
          break;
        }
      }

      // 如果向上移动后仍有重叠，则向下移动，但使用极小的步长
      while (checkNodeOverlap(finalPosition, data.sourceNodeId)) {
        finalPosition.y += NODE_HEIGHT * 0.4; // 大幅减少向下移动的幅度
      }

      // 创建新节点
      const newNodeId = nodeStore.addNode({
        id: '',
        type: 'kpi',
        position: finalPosition,
        data: {
          label: data.indicator.label,
          value: data.indicator.value,
          change: data.indicator.change,
          period: data.indicator.period,
          color: data.indicator.color || '#1890ff'
        }
      })

      // 查找是否存在临时连线
      const tempEdges = edgeStore.edges.filter(edge =>
        edge.source === data.sourceNodeId && edge.isTemporary
      )

      // 如果存在临时连线，更新它
      if (tempEdges.length > 0) {
        // 更新第一个临时连线
        edgeStore.updateEdge(tempEdges[0].id, {
          target: newNodeId,
          isTemporary: false // 移除临时标记
        })
      } else {
        // 如果不存在临时连线，创建新连线
        edgeStore.addEdge({
          id: '',
          source: data.sourceNodeId,
          target: newNodeId,
          label: data.relationType.icon,
          type: data.relationType.id
        })
      }

      // 全局布局调整 - 完全重写版本，直接强制设置节点位置并禁用自动布局
      setTimeout(() => {
        if (canvasRef.value) {
          console.log('开始执行强制节点位置调整');

          // 首先禁用自动布局，防止我们的修改被覆盖
          if (canvasRef.value.autoLayoutEnabled) {
            console.log('禁用自动布局');
            canvasRef.value.autoLayoutEnabled = false;
          }

          // 获取所有节点和边
          const allNodes = nodeStore.nodes;
          const allEdges = edgeStore.edges;

          // 构建节点关系树
          const nodeMap = new Map();
          const childrenMap = new Map();
          const parentMap = new Map();

          // 初始化映射
          allNodes.forEach(node => {
            nodeMap.set(node.id, node);
            childrenMap.set(node.id, []);
          });

          // 构建父子关系
          allEdges.forEach(edge => {
            const sourceId = edge.source;
            const targetId = edge.target;

            // 添加到子节点列表
            if (childrenMap.has(sourceId)) {
              childrenMap.get(sourceId).push(targetId);
            }

            // 记录父节点
            parentMap.set(targetId, sourceId);
          });

          console.log('节点关系树构建完成');

          // 特别处理新添加的节点
          const newNode = nodeStore.getNodeById(newNodeId);
          const parentNode = nodeStore.getNodeById(data.sourceNodeId);

          if (newNode && parentNode) {
            console.log('处理新添加的节点:', newNodeId);

            // 获取父节点的父节点（祖父节点）
            const grandparentId = parentMap.get(data.sourceNodeId);

            if (grandparentId) {
              console.log('找到祖父节点:', grandparentId);

              // 获取叔叔节点（父节点的兄弟节点）
              const uncleIds = childrenMap.get(grandparentId).filter((id: string) => id !== data.sourceNodeId);

              if (uncleIds && uncleIds.length > 0) {
                console.log('找到叔叔节点:', uncleIds);

                // 获取父节点的所有子节点数量
                const siblingCount = childrenMap.get(data.sourceNodeId).length;
                console.log('父节点子节点数量:', siblingCount);

                // 使用累积的间距增量 - 确保每次新增子节点都增加间距
                // 获取父节点的历史间距记录
                const parentId = data.sourceNodeId;

                // 使用一个简单的方法来累积间距
                // 每次新增子节点时增加固定间距，使用与终端子节点间距相同的值
                const FIXED_SPACING_INCREMENT = 60; // 每次新增子节点增加60单位的间距，与终端子节点间距的1/3

                // 基于子节点数量计算累积间距
                // 子节点数量越多，间距越大
                const cumulativeSpacing = FIXED_SPACING_INCREMENT * siblingCount;

                // 总间距使用累积值
                const totalSpacing = cumulativeSpacing;

                console.log(`父节点 ${parentId} 子节点数量: ${siblingCount}, 累积间距: ${cumulativeSpacing}`);

                // 获取所有叔叔节点
                const uncleNodes = uncleIds.map((id: string) => nodeStore.getNodeById(id)).filter(Boolean);

                // 按Y坐标排序
                const sortedUncleNodes = [...uncleNodes].sort((a, b) => a.position.y - b.position.y);

                // 确定移动方向
                if (parentNode.position.y <= sortedUncleNodes[0].position.y) {
                  // 父节点在上方，向下移动所有叔叔节点
                  console.log('父节点在上方，向下移动所有叔叔节点');

                  // 移动所有叔叔节点
                  sortedUncleNodes.forEach(uncleNode => {
                    // 直接设置新位置，而不是增量移动
                    const newY = uncleNode.position.y + totalSpacing;

                    console.log(`移动叔叔节点 ${uncleNode.id} 从 Y=${uncleNode.position.y} 到 Y=${newY}`);

                        // 直接更新store中的位置，不使用DOM操作
                    nodeStore.moveNode(uncleNode.id, {
                      x: uncleNode.position.x,
                      y: newY
                    });

                    // 递归移动所有子节点
                    moveAllDescendants(uncleNode.id, 0, totalSpacing);
                  });
                } else {
                  // 父节点在下方，向下移动父节点
                  console.log('父节点在下方，向下移动父节点');

                  // 直接设置新位置，而不是增量移动
                  const newY = parentNode.position.y + totalSpacing;

                  console.log(`移动父节点 ${parentNode.id} 从 Y=${parentNode.position.y} 到 Y=${newY}`);

                  // 直接更新store中的位置，不使用DOM操作
                  nodeStore.moveNode(parentNode.id, {
                    x: parentNode.position.x,
                    y: newY
                  });

                  // 递归移动所有子节点
                  moveAllDescendants(parentNode.id, 0, totalSpacing);
                }

                // 禁用自动布局，确保我们的修改不会被覆盖
                if (canvasRef.value) {
                  canvasRef.value.autoLayoutEnabled = false;
                }

                // 一次性设置节点位置，不使用多次延迟执行
                console.log('一次性设置节点位置');

                // 禁用所有可能的动画效果
                const disableAnimations = () => {
                  // 检查是否已经存在禁用动画的样式标签
                  if (document.getElementById('disable-animations')) {
                    return; // 已经存在，不需要重复添加
                  }

                  // 添加一个样式标签，禁用所有过渡和动画
                  const styleElement = document.createElement('style');
                  styleElement.id = 'disable-animations';
                  styleElement.textContent = `
                    .vue-flow__node {
                      transition: none !important;
                      animation: none !important;
                    }
                    .vue-flow__edge {
                      transition: none !important;
                      animation: none !important;
                    }
                  `;
                  document.head.appendChild(styleElement);

                  // 5秒后移除样式标签，恢复动画效果
                  setTimeout(() => {
                    const element = document.getElementById('disable-animations');
                    if (element) {
                      element.remove();
                    }
                  }, 5000);

                  console.log('已禁用节点动画效果');
                };

                // 禁用动画
                disableAnimations();

                // 直接设置最终位置
                if (parentNode.position.y <= sortedUncleNodes[0].position.y) {
                  // 父节点在上方，一次性移动所有叔叔节点
                  sortedUncleNodes.forEach(uncleNode => {
                    // 计算最终位置
                    const finalY = uncleNode.position.y + totalSpacing;

                    console.log(`一次性移动叔叔节点 ${uncleNode.id} 到 Y=${finalY}`);

                    // 直接设置最终位置
                    nodeStore.moveNode(uncleNode.id, {
                      x: uncleNode.position.x,
                      y: finalY
                    });

                    // 递归移动所有子节点
                    moveAllDescendants(uncleNode.id, 0, totalSpacing);
                  });
                } else {
                  // 父节点在下方，一次性移动父节点
                  const finalY = parentNode.position.y + totalSpacing;

                  console.log(`一次性移动父节点 ${parentNode.id} 到 Y=${finalY}`);

                  // 直接设置最终位置
                  nodeStore.moveNode(parentNode.id, {
                    x: parentNode.position.x,
                    y: finalY
                  });

                  // 递归移动所有子节点
                  moveAllDescendants(parentNode.id, 0, totalSpacing);
                }
              }
            }
          }

          // 递归移动所有后代节点 - 优化版本
          function moveAllDescendants(nodeId: string, deltaX: number, deltaY: number) {
            // 获取该节点的所有子节点
            const children = childrenMap.get(nodeId) || [];

            // 如果没有子节点，直接返回
            if (children.length === 0) {
              return;
            }

            // 批量处理所有子节点
            const childrenToMove: {id: string, x: number, y: number}[] = [];

            // 收集所有需要移动的子节点信息
            const collectDescendants = (parentId: string, dx: number, dy: number) => {
              const childIds = childrenMap.get(parentId) || [];

              childIds.forEach((childId: string) => {
                const childNode = nodeStore.getNodeById(childId);
                if (childNode) {
                  // 添加到待移动列表
                  childrenToMove.push({
                    id: childId,
                    x: childNode.position.x + dx,
                    y: childNode.position.y + dy
                  });

                  // 递归收集子节点的子节点
                  collectDescendants(childId, dx, dy);
                }
              });
            };

            // 收集所有后代节点
            collectDescendants(nodeId, deltaX, deltaY);

            // 一次性批量移动所有后代节点
            if (childrenToMove.length > 0) {
              console.log(`批量移动 ${nodeId} 的 ${childrenToMove.length} 个后代节点，Y偏移: ${deltaY}`);

              // 移动所有收集到的节点
              childrenToMove.forEach(item => {
                nodeStore.moveNode(item.id, {
                  x: item.x,
                  y: item.y
                });
              });
            }
          }
        }
      }, 200)

      // 关闭选择器
      closeSelector()
    }

    // 处理节点删除事件
    const handleNodeRemoved = (nodeId: string) => {
      // 递归删除与节点相关的所有连线和子节点
      removeAllRelatedEdges(nodeId)
    }

    // 递归删除与节点相关的所有连线和子节点
    const removeAllRelatedEdges = (nodeId: string) => {
      // 获取以该节点为源节点的所有连线
      const outgoingEdges = edgeStore.edges.filter(edge => edge.source === nodeId)

      // 对于每条出边，递归删除目标节点及其相关连线
      outgoingEdges.forEach(edge => {
        const targetNodeId = edge.target

        // 删除连线
        edgeStore.removeEdge(edge.id)

        // 递归删除目标节点及其相关连线
        removeAllRelatedEdges(targetNodeId)

        // 删除目标节点
        nodeStore.removeNode(targetNodeId)
      })

      // 删除以该节点为目标节点的所有连线
      const incomingEdges = edgeStore.edges.filter(edge => edge.target === nodeId)
      incomingEdges.forEach(edge => {
        edgeStore.removeEdge(edge.id)
      })
    }

    const togglePreviewMode = () => {
      router.push('/preview')
    }

    const saveModel = () => {
      // 保存模型逻辑
      alert('模型已保存')
    }

    // 应用自动布局
    const applyAutoLayout = () => {
      if (canvasRef.value) {
        // 显示加载提示
        const loadingMessage = '正在优化布局...'
        console.log(loadingMessage)

        // 第一步：应用基本布局
        canvasRef.value?.applyAutoLayout(true)

        // 添加延迟，确保布局完全应用后再次检查和优化
        setTimeout(() => {
          // 第二步：优化节点间距和连线
          canvasRef.value?.applyAutoLayout(true)

          // 第三步：特别关注子节点与父节点的对齐
          setTimeout(() => {
            canvasRef.value?.applyAutoLayout(true)

            // 第四步：最终确保连线路径合理
            setTimeout(() => {
              // 最后一次应用布局，确保所有优化都已应用
              canvasRef.value?.applyAutoLayout(true)

              // 第五步：特别处理第三级节点重叠问题
              setTimeout(() => {
                // 构建节点关系树
                const buildNodeTree = () => {
                  const nodeMap = new Map<string, { node: any, children: string[], parent: string | null }>()

                  // 初始化节点映射
                  nodeStore.nodes.forEach(node => {
                    nodeMap.set(node.id, { node, children: [], parent: null })
                  })

                  // 构建父子关系
                  edgeStore.edges.forEach(edge => {
                    const sourceNode = nodeMap.get(edge.source)
                    const targetNode = nodeMap.get(edge.target)

                    if (sourceNode && targetNode) {
                      sourceNode.children.push(edge.target)
                      targetNode.parent = edge.source
                    }
                  })

                  // 找出根节点（没有父节点的节点）
                  const rootNodes: string[] = []
                  nodeStore.nodes.forEach(node => {
                    const nodeInfo = nodeMap.get(node.id)
                    if (nodeInfo && nodeInfo.parent === null) {
                      rootNodes.push(node.id)
                    }
                  })

                  return { nodeMap, rootNodes }
                }

                const { nodeMap, rootNodes } = buildNodeTree()

                // 获取所有节点的深度信息
                const nodeDepths = new Map<string, number>()

                // 计算节点深度的递归函数
                const calculateDepth = (nodeId: string, depth: number = 0) => {
                  nodeDepths.set(nodeId, depth)

                  const nodeInfo = nodeMap.get(nodeId)
                  if (!nodeInfo) return

                  nodeInfo.children.forEach(childId => {
                    calculateDepth(childId, depth + 1)
                  })
                }

                // 从根节点开始计算深度
                rootNodes.forEach(rootId => {
                  calculateDepth(rootId)
                })

                // 获取所有第三级或更深的节点
                const deepNodes = Array.from(nodeDepths.entries())
                  .filter(([_, depth]) => depth >= 2)
                  .map(([nodeId, _]) => nodeId)

                // 检查每个深层节点是否与其"叔叔"节点的子节点（表兄弟）重叠
                let needsAnotherLayout = false

                deepNodes.forEach(nodeId => {
                  const node = nodeStore.getNodeById(nodeId)
                  if (!node) return

                  const nodeInfo = nodeMap.get(nodeId)
                  if (!nodeInfo || !nodeInfo.parent) return

                  // 获取父节点信息
                  const parentInfo = nodeMap.get(nodeInfo.parent)
                  if (!parentInfo || !parentInfo.parent) return

                  // 获取祖父节点信息
                  const grandparentInfo = nodeMap.get(parentInfo.parent)
                  if (!grandparentInfo) return

                  // 获取父节点的所有兄弟节点（叔叔节点）
                  const uncleNodeIds = grandparentInfo.children.filter(id => id !== nodeInfo.parent)

                  // 获取所有表兄弟节点（叔叔的子节点）
                  const cousinNodes: any[] = []

                  uncleNodeIds.forEach(uncleId => {
                    const uncleInfo = nodeMap.get(uncleId)
                    if (uncleInfo) {
                      uncleInfo.children.forEach(cousinId => {
                        const cousinNode = nodeStore.getNodeById(cousinId)
                        if (cousinNode) cousinNodes.push(cousinNode)
                      })
                    }
                  })

                  // 检查是否与任何表兄弟节点重叠
                  const overlappingNodes = cousinNodes.filter(cousinNode =>
                    Math.abs(cousinNode.position.x - node.position.x) < 240 &&
                    Math.abs(cousinNode.position.y - node.position.y) < 90
                  )

                  // 如果有重叠，标记需要再次应用布局
                  if (overlappingNodes.length > 0) {
                    needsAnotherLayout = true
                  }
                })

                // 如果检测到重叠，再次应用布局
                if (needsAnotherLayout) {
                  canvasRef.value?.applyAutoLayout(true)
                }

                // 通知用户布局已完成
                console.log('自动布局已应用')
              }, 400)
            }, 400)
          }, 400)
        }, 400)
      }
    }


    // 控制是否自动创建初始节点
    const autoCreateInitialNode = ref(false) // 设置为false，不自动创建初始节点

    // 只有在需要时才初始化根节点
    if (autoCreateInitialNode.value) {
      initRootNode()
    }

    return {
      selectedNodeId,
      selectedEdgeId,
      modelName,
      showSelector,
      currentSourceNodeId,
      currentRelationType,
      showRelationSelector,
      relationSelectorPosition,
      currentEdgeId,
      currentEdgeType,
      showIndicatorSelector,
      showConnectorIndicatorSelector,
      closeSelector,
      createRelatedNode,
      createEmptyNode,
      updateEdgeRelation,
      togglePreviewMode,
      saveModel,
      handleNodeRemoved,
      applyAutoLayout,
      canvasRef
    }
  }
})
</script>

<style lang="scss" scoped>
.editor-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f7fa;

  .header-bar {
    height: 56px;
    background-color: #fff;
    border-bottom: 1px solid #e8e8e8;
    display: flex;
    align-items: center;
    padding: 0 16px;
    justify-content: space-between;

    .left-section {
      display: flex;
      align-items: center;

      .back-btn {
        display: flex;
        align-items: center;
        color: #666;
        margin-right: 16px;

        .icon {
          margin-right: 4px;
        }

        &:hover {
          color: #1890ff;
        }
      }

      .title {
        font-size: 16px;
        font-weight: 500;
        color: #333;
        margin-right: 8px;
      }

      .edit-icon {
        color: #999;
        cursor: pointer;

        &:hover {
          color: #1890ff;
        }
      }
    }

    .right-section {
      display: flex;
      align-items: center;

      button {
        margin-left: 8px;
      }
    }
  }

  .toolbar {
    height: 48px;
    background-color: #fff;
    border-bottom: 1px solid #e8e8e8;
    display: flex;
    align-items: center;
    padding: 0 16px;
    margin-bottom: 16px;

    .date-picker {
      display: flex;
      align-items: center;
      margin-right: 16px;

      .label {
        color: #666;
        margin-right: 8px;
      }

      .date-input {
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        padding: 4px 8px;
        width: 120px;
      }
    }

    .batch-config {
      margin-right: auto;
    }

    .layout-tools {
      display: flex;
      align-items: center;
      margin-right: 16px;

      button {
        display: flex;
        align-items: center;

        .icon {
          margin-right: 4px;
          font-size: 14px;
        }
      }
    }

    .search-tools {
      display: flex;

      .tool-btn {
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 8px;
        border-radius: 4px;

        &:hover {
          background-color: #f0f0f0;
        }
      }
    }
  }

  .main-content {
    display: flex;
    flex: 1;
    overflow: hidden;
    padding: 0 0 16px 16px;

    .canvas-container {
      flex: 1;
      position: relative;
      overflow: hidden;
      background-color: #fff;
      border-radius: 4px 0 0 4px;
      box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
    }

    .config-panel {
      width: 320px;
      background-color: #fff;
      display: flex;
      flex-direction: column;
      overflow: hidden;
      box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);

      .empty-state {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #999;
        height: 100%;
      }
    }
  }

  .modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;

    .modal-backdrop {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
    }

    .modal-content {
      position: relative;
      width: 500px;
      max-height: 80vh;
      background-color: #fff;
      border-radius: 4px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      display: flex;
      flex-direction: column;

      .modal-header {
        padding: 16px;
        border-bottom: 1px solid #f0f0f0;
        display: flex;
        align-items: center;
        justify-content: space-between;

        h3 {
          margin: 0;
          font-size: 16px;
          font-weight: 500;
        }

        .close-btn {
          background: none;
          border: none;
          font-size: 20px;
          color: #999;
          cursor: pointer;

          &:hover {
            color: #666;
          }
        }
      }

      .modal-body {
        flex: 1;
        overflow-y: auto;
        padding: 16px;
        max-height: 500px;
      }
    }
  }

  .relation-selector-popup {
    position: var(--relation-popup-position, absolute); /* 使用CSS变量控制定位方式 */
    z-index: 1000;
    filter: drop-shadow(0 2px 8px rgba(0, 0, 0, 0.15));

    /* 添加一个小三角形指向计算符号 */
    &::before {
      content: '';
      position: absolute;
      left: -8px;
      top: var(--relation-popup-triangle-top, 115px); /* 使用CSS变量控制三角形位置 */
      width: 0;
      height: 0;
      border-top: 8px solid transparent;
      border-bottom: 8px solid transparent;
      border-right: 8px solid white;
      z-index: 1; /* 确保三角形在弹窗内容之上 */
      filter: drop-shadow(-1px 0 1px rgba(0, 0, 0, 0.1)); /* 添加阴影效果，使三角形更明显 */
    }
  }
}

.btn-default {
  background-color: #fff;
  color: #666;
  padding: 5px 12px;
  border-radius: 4px;
  border: 1px solid #d9d9d9;

  &:hover {
    color: #40a9ff;
    border-color: #40a9ff;
  }
}

.btn-primary {
  background-color: #1890ff;
  color: white;
  padding: 5px 12px;
  border-radius: 4px;

  &:hover {
    background-color: #40a9ff;
  }
}
</style>