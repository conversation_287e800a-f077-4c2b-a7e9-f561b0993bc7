<template>
  <div class="attribution-popup" v-if="visible">
    <div class="popup-backdrop" @click="close"></div>
    <div class="popup-content">
      <div class="popup-header">
        <h3>{{ title }}</h3>
        <button class="close-btn" @click="close">×</button>
      </div>
      <div class="popup-body">
        <div v-if="attributionData && attributionData.length > 0">
          <div class="attribution-summary">
            <div class="summary-title">归因分析</div>
            <div class="summary-description">
              {{ description }}
            </div>
          </div>

          <div class="attribution-list">
            <div v-for="item in attributionData" :key="item.id" class="attribution-item">
              <div class="item-header">
                <div class="item-name">{{ item.name }}</div>
                <div class="item-value">{{ item.value }}</div>
              </div>
              <div class="item-bar-container">
                <div class="item-bar" :style="{ width: `${item.percentage}%` }"></div>
              </div>
              <div class="item-footer">
                <div class="item-percentage">{{ item.percentage }}%</div>
                <div class="item-contribution">贡献度: {{ item.contribution }}</div>
              </div>
            </div>
          </div>
        </div>
        <div v-else class="empty-state">
          <p>暂无归因数据</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue'
import type { AttributionItem } from '../../types'

export default defineComponent({
  name: 'AttributionPopup',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '归因分析'
    },
    description: {
      type: String,
      default: '以下是影响该指标的主要因素及其贡献度'
    },
    attributionData: {
      type: Array as () => AttributionItem[],
      default: () => []
    }
  },
  emits: ['close'],
  setup(_, { emit }) {
    const close = () => {
      emit('close')
    }

    return {
      close
    }
  }
})
</script>

<style lang="scss" scoped>
.attribution-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 2000; /* 提高z-index确保弹窗在最上层 */
  display: flex;
  align-items: center;
  justify-content: center;

  .popup-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
  }

  .popup-content {
    position: relative;
    width: 500px;
    max-width: 90%;
    max-height: 80vh;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .popup-header {
      padding: 16px;
      border-bottom: 1px solid #f0f0f0;
      display: flex;
      align-items: center;
      justify-content: space-between;

      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 500;
        color: #333;
      }

      .close-btn {
        background: none;
        border: none;
        font-size: 20px;
        color: #999;
        cursor: pointer;

        &:hover {
          color: #666;
        }
      }
    }

    .popup-body {
      padding: 16px;
      overflow-y: auto;

      .attribution-summary {
        margin-bottom: 20px;

        .summary-title {
          font-size: 16px;
          font-weight: 500;
          color: #333;
          margin-bottom: 8px;
        }

        .summary-description {
          font-size: 14px;
          color: #666;
          line-height: 1.5;
        }
      }

      .attribution-list {
        .attribution-item {
          margin-bottom: 16px;
          padding: 12px;
          border: 1px solid #f0f0f0;
          border-radius: 4px;
          background-color: #fafafa;

          .item-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;

            .item-name {
              font-weight: 500;
              color: #333;
            }

            .item-value {
              font-weight: 500;
              color: #1890ff;
            }
          }

          .item-bar-container {
            height: 8px;
            background-color: #f0f0f0;
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 8px;

            .item-bar {
              height: 100%;
              background-color: #1890ff;
              border-radius: 4px;
            }
          }

          .item-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
            color: #666;

            .item-percentage {
              font-weight: 500;
            }
          }
        }
      }

      .empty-state {
        padding: 24px;
        text-align: center;
        color: #999;
      }
    }
  }
}
</style>
