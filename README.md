# 可视化配置系统 (Visual Configuration System)

一个基于Vue 3的可视化配置系统，用于创建和管理节点关系图，支持节点参数配置、节点连接计算关系、自动布局等功能。

## 技术栈

### 前端框架
- **Vue 3.5.13**: 使用最新的Vue 3组合式API开发
- **TypeScript**: 提供类型安全和更好的开发体验
- **Vite 6.3.4**: 现代前端构建工具，提供快速的开发体验

### 状态管理
- **Pinia 3.0.2**: Vue官方推荐的状态管理库，替代Vuex

### 路由
- **Vue Router 4.5.1**: Vue官方路由管理器

### 样式
- **SCSS/SASS 1.87.0**: CSS预处理器，提供更强大的样式编写能力

### 工具库
- **UUID 11.1.0**: 用于生成唯一标识符

## 项目结构

```
visual-config-system/
├── public/                 # 静态资源
├── src/                    # 源代码
│   ├── assets/             # 资源文件（图片、字体等）
│   ├── components/         # 组件
│   │   ├── AttributionPopup/   # 归因分析弹窗组件
│   │   ├── Canvas/         # 画布组件
│   │   ├── ConfigPanel/    # 配置面板组件
│   │   ├── Edge/           # 连线组件
│   │   ├── IndicatorSelector/ # 指标选择器组件
│   │   ├── Node/           # 节点组件
│   │   └── RelationTypeSelector/ # 关系类型选择器组件
│   ├── stores/             # Pinia状态管理
│   │   ├── canvas.ts       # 画布状态
│   │   ├── edge.ts         # 连线状态
│   │   └── node.ts         # 节点状态
│   ├── types/              # TypeScript类型定义
│   ├── views/              # 页面视图
│   │   ├── Editor.vue      # 编辑器页面
│   │   └── Preview.vue     # 预览页面
│   ├── App.vue             # 根组件
│   ├── main.ts             # 入口文件
│   ├── router.ts           # 路由配置
│   └── style.css           # 全局样式
├── index.html              # HTML模板
├── tsconfig.json           # TypeScript配置
├── vite.config.ts          # Vite配置
└── package.json            # 项目依赖和脚本
```

## 核心功能

### 1. 节点管理
- 创建、编辑、删除节点
- 节点拖拽定位
- 节点参数配置（基础属性、样式配置、数据配置、高级配置）

### 2. 连线管理
- 创建节点之间的连接关系
- 支持多种关系类型（相加、相减、相乘、相除、相关）
- 连线样式配置

### 3. 画布操作
- 缩放、平移画布
- 自动布局功能
- 节点选择与高亮

### 4. 指标配置
- 指标选择与搜索
- 指标数据展示
- 指标变化趋势显示

### 5. 归因分析
- 节点归因分析配置
- 归因数据可视化

## 自动布局算法

系统实现了一套复杂的自动布局算法，确保节点在画布中的合理分布：

1. 当节点只有一个子节点时，子节点与父节点水平中心对齐
2. 当节点有多个子节点时，子节点对称分布，与父节点形成局部对称效果
3. 新增节点时，确保不与现有节点重叠
4. 子节点与父节点之间保持固定的水平间距
5. 父节点与叔叔节点（同级节点）之间的垂直间距大于子节点之间的垂直间距

## 安装与运行

### 环境要求
- Node.js 18.0.0 或更高版本
- pnpm 8.0.0 或更高版本（推荐）或 npm

### 安装依赖
```bash
# 使用pnpm（推荐）
pnpm install

# 或使用npm
npm install
```

### 开发模式运行
```bash
# 使用pnpm
pnpm start
# 或
pnpm dev

# 或使用npm
npm run dev
```

### 构建生产版本
```bash
# 使用pnpm
pnpm build

# 或使用npm
npm run build
```

### 预览生产版本
```bash
# 使用pnpm
pnpm preview

# 或使用npm
npm run preview
```

## 部署

### 方法1：使用传统Web服务器（Nginx/Apache）

1. 构建项目：
   ```bash
   pnpm build
   ```

2. 将`dist`目录中的文件上传到Web服务器

3. 配置Nginx（示例）：
   ```nginx
   server {
       listen 80;
       server_name your-domain.com;

       root /path/to/dist;
       index index.html;

       location / {
           try_files $uri $uri/ /index.html;
       }
   }
   ```

### 方法2：使用Docker

1. 创建Dockerfile：
   ```dockerfile
   FROM nginx:alpine
   COPY dist/ /usr/share/nginx/html/
   COPY nginx.conf /etc/nginx/conf.d/default.conf
   EXPOSE 80
   CMD ["nginx", "-g", "daemon off;"]
   ```

2. 构建并运行Docker容器：
   ```bash
   docker build -t visual-config-system .
   docker run -d -p 80:80 visual-config-system
   ```

## 自定义域名访问

如果需要通过自定义域名访问开发服务器，请修改`vite.config.ts`文件中的`allowedHosts`配置：

```typescript
server: {
  host: true,
  port: 5173,
  allowedHosts: [
    'localhost',
    'your-domain.com', // 添加您的域名
  ],
}
```

## 许可证

[MIT](LICENSE)
