import { defineStore } from 'pinia'
import type { CanvasState, Position, Size } from '../types'

export const useCanvasStore = defineStore('canvas', {
  state: (): CanvasState => ({
    scale: 1,
    position: { x: 0, y: 0 },
    dimensions: { width: 5000, height: 5000 }
  }),

  actions: {
    // 设置画布缩放
    setScale(scale: number) {
      // 限制缩放范围
      this.scale = Math.min(Math.max(0.1, scale), 2)
    },

    // 设置画布位置
    setPosition(position: Position) {
      this.position = position
    },

    // 设置画布尺寸
    setDimensions(dimensions: Size) {
      this.dimensions = dimensions
    },

    // 画布缩放
    zoom(delta: number, center: Position) {
      const newScale = this.scale + delta * 0.001
      this.setScale(newScale)

      // 调整位置以保持缩放中心
      const scaleFactor = newScale / this.scale
      const newPosition = {
        x: center.x - (center.x - this.position.x) * scaleFactor,
        y: center.y - (center.y - this.position.y) * scaleFactor
      }

      this.setPosition(newPosition)
    },

    // 画布平移 - 优化版本
    pan(deltaX: number, deltaY: number) {
      // 直接修改属性，避免创建新对象
      this.position.x += deltaX;
      this.position.y += deltaY;
    },

    // 重置画布
    resetView() {
      this.scale = 1
      this.position = { x: 0, y: 0 }
    }
  }
})