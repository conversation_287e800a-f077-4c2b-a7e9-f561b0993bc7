# 画布节点创建和布局优化日志

## 优化目标
1. 优化第一个节点创建位置，偏向画布左侧为后续子节点预留空间
2. 修复节点创建时的闪动问题
3. 确保子节点布局满足对称要求（单子节点与父节点水平对齐，多子节点对称分布）
4. 解决同级节点重叠问题

## 优化策略
参考当前手动自动布局功能的逻辑和算法，将其应用到节点创建时的布局计算中。手动自动布局功能基本满足对称和不重叠要求，是之前优化的正面成果。

## 修改记录

### 2024-12-19 开始优化

### 第二轮优化 - 解决闪跳和间距问题

#### 1. 解决第一个节点闪跳问题 (Canvas.vue)
- 修改 `createEmptyNode` 函数逻辑，对第一个节点不应用自动布局
- 第一个节点直接居中到指定位置，避免位置重新计算导致的闪跳
- 保持第一个节点 x 坐标为 200，偏向左侧为后续子节点预留空间

#### 2. 减少同级节点垂直间距 (useLayoutCalculation.ts)
- SIBLING_VERTICAL_SPACING: 220 → 120
- COUSIN_VERTICAL_SPACING: 150 → 100
- MIN_VERTICAL_GAP: 120 → 80
- VERTICAL_GAP: 160 → 100
- TERMINAL_NODE_SPACING: 200 → 120
- CHILD_NODE_SPACING: 200 → 120

#### 3. 减少父子节点水平间距 (useLayoutCalculation.ts)
- FIXED_HORIZONTAL_SPACING: 380 → 280
- PARENT_CHILD_SPACING: 380 → 280

#### 4. 修复第一个节点位置问题 (Canvas.vue)
- 修改第一个节点的画布定位逻辑
- 不使用 centerOnPosition，而是将节点定位到左侧1/4位置
- 避免节点居中显示，保持偏左布局

#### 5. 缩短连线虚线长度 (Edge.vue & useEdgeOperations.ts)
- 水平偏移系数：0.4 → 0.3 → 0.2
- 最大水平偏移：60/70/80 → 40/50/60 → 20/25/30
- 最小水平偏移：50 → 30 → 15
- 同步修改临时连线参数，保持一致性
- 添加 nextTick 导入，优化布局应用时序，减少闪动效果
- 调整居中延迟时间从 50ms 改为 100ms，减少闪动

#### 6. 修复临时连线长度问题 (Edge.vue)
- **问题发现**：临时连线（从父节点创建子节点的中间状态）长度硬编码为30px，未应用虚线缩短优化
- **根本原因**：getTargetPath()函数中临时连线处理逻辑独立，没有使用与正常连线相同的缩短参数
- **第一次修复**：
  - 临时连线长度：30px → 15px
  - 强制目标点距离：30px → 15px
  - 曲线控制点偏移：30px → 15px
  - 直线判断阈值：30px → 15px

#### 7. 进一步优化连线长度一致性 (Edge.vue & useEdgeOperations.ts)
- **问题发现**：临时连线过短(15px)，完整连线仍然过长，两者视觉不一致
- **优化策略**：统一连线长度标准，确保临时连线和完整连线视觉一致
- **具体调整**：
  - 临时连线长度：15px → 50px（适中长度）
  - 强制目标点距离：15px → 50px
  - 曲线控制点偏移：15px → 25px
  - 直线判断阈值：15px → 60px
  - 水平偏移系数：0.2 → 0.15（进一步缩短）
  - 同级节点最大偏移：20px → 30px
  - 不同级节点最大偏移：25/30px → 40/50px
  - 最小水平偏移：15px → 25px（确保曲线自然）

#### 8. 重构连线结构为4部分设计 (Edge.vue)
- **设计目标**：将连线重构为统一的4个部分，提高视觉一致性和交互体验
- **连线结构**：
  1. **第1部分**：从父节点右侧到计算符号的实线
  2. **第2部分**：计算关系符号（加减乘除、相关关系等5种）
  3. **第3部分**：从计算符号右侧出发的虚线（带箭头，可交互）
  4. **第4部分**：从虚线箭头到目标子节点的连接线（仅在有目标节点时显示）
- **关键特性**：
  - 前3部分始终保持一条直线
  - 第3部分虚线长度固定为80px
  - 有目标节点时，第3部分箭头消失，第4部分显示箭头
  - 临时连线只显示前3部分
- **交互功能**：
  - 第3部分虚线支持点击交互
  - 临时连线点击创建子节点
  - 完整连线点击展开/收缩子节点（预留功能）
- **技术实现**：
  - 重构路径计算函数：`getLeftSolidPath`、`getRightDashedPath`、`getTargetConnectionPath`
  - 新增箭头控制逻辑：`getRightDashedMarker`
  - 添加新事件：`toggle-children`
  - 更新样式和交互区域

#### 2. 大幅简化子节点创建逻辑 (Editor.vue)
- 简化 `createRelatedNode` 函数，移除复杂的位置计算逻辑
- 使用与自动布局相同的 PARENT_CHILD_SPACING 常量 (380px，与FIXED_HORIZONTAL_SPACING一致)
- 移除所有手动重叠检测和位置调整代码（约200行复杂逻辑）
- 改为使用临时位置创建节点，然后调用 `applyAutoLayout(true)` 进行精确布局
- 延迟时间从 200ms 改为 100ms，提高响应速度

#### 3. 统一布局常量 (useLayoutCalculation.ts)
- 调整 ROOT_X 从 100 改为 200，与Canvas.vue中第一个节点位置保持一致
- 调整 ROOT_Y 从 150 改为 300，与Canvas.vue中第一个节点位置保持一致
- 确保手动自动布局与节点创建时的位置计算完全一致

## 优化总结

### 核心改进
1. **统一布局逻辑**：将节点创建时的布局计算统一到手动自动布局功能中，避免了两套不同的布局算法导致的不一致问题
2. **简化代码复杂度**：移除了约200行复杂的手动位置计算和重叠检测代码，大幅降低了维护成本
3. **提升用户体验**：减少了节点创建时的闪动效果，优化了第一个节点的默认位置

### 技术优势
- **代码复用**：充分利用了已经优化好的自动布局算法
- **一致性保证**：确保所有节点布局都遵循相同的对称和不重叠原则
- **性能优化**：减少了重复的位置计算，提高了响应速度

### 预期效果
- 第一个节点创建在画布左侧，为后续子节点预留充足空间
- 单子节点与父节点完美水平中心对齐
- 多子节点以父节点为中心对称分布
- 消除节点创建时的闪动问题
- 解决同级节点重叠问题

## 技术细节

### 修改的文件
1. **src/components/Canvas/Canvas.vue**
   - 优化 `createEmptyNode` 函数
   - 调整第一个节点位置从 (400, 300) 到 (200, 300)
   - 优化布局应用时序，减少闪动

2. **src/views/Editor.vue**
   - 大幅简化 `createRelatedNode` 函数
   - 移除约200行复杂的手动布局计算代码
   - 统一使用 PARENT_CHILD_SPACING = 380px
   - 改为依赖自动布局算法

3. **src/composables/canvas/useLayoutCalculation.ts**
   - 调整 ROOT_X 和 ROOT_Y 常量与节点创建位置保持一致
   - 确保布局算法的一致性

### 关键优化策略
- **统一布局逻辑**：所有节点布局都使用同一套自动布局算法
- **简化创建流程**：节点创建时使用临时位置，然后调用自动布局进行精确定位
- **减少代码重复**：移除重复的位置计算和重叠检测逻辑
- **提升性能**：减少不必要的计算和DOM操作

### 测试建议
1. 创建第一个节点，验证位置是否偏向左侧
2. 为第一个节点添加单个子节点，验证是否水平对齐
3. 为第一个节点添加多个子节点，验证是否对称分布
4. 创建多层级节点结构，验证是否无重叠
5. 观察节点创建过程，验证是否减少了闪动效果
