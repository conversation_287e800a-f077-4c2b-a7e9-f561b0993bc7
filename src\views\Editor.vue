<template>
  <div class="editor-container">
    <!-- 顶部导航栏 -->
    <div class="header-bar">
      <div class="left-section">
        <button class="back-btn">
          <i class="icon">←</i>
          <span>返回</span>
        </button>
        <span class="title">模型名称</span>
        <i class="edit-icon">✎</i>
      </div>
      <div class="right-section">
        <button class="btn-default" @click="togglePreviewMode">预览</button>
        <button class="btn-primary" @click="saveModel">保存</button>
      </div>
    </div>

    <!-- 工具栏 -->
    <div class="toolbar">
      <div class="date-picker">
        <span class="label">数据时间:</span>
        <input type="text" class="date-input" value="2025-04-26" />
      </div>
      <div class="date-picker">
        <span class="label">对比时间:</span>
        <input type="text" class="date-input" value="2025-03-27" />
      </div>
      <button class="btn-default batch-config">批量设置</button>

      <!-- 添加自动布局按钮 -->
      <div class="layout-tools">
        <button class="btn-default" @click="applyAutoLayout">
          <span class="icon">⟲</span>
          自动布局
        </button>
      </div>
    </div>

    <div class="main-content">
      <!-- 中间画布区域 -->
      <div class="canvas-container">
        <Canvas
          ref="canvasRef"
          @add-relation="showIndicatorSelector"
          @create-node="createRelatedNode"
          @connector-click="showConnectorIndicatorSelector"
          @node-removed="handleNodeRemoved"
          @close-relation-selector="() => showRelationSelector = false"
          @create-empty-node="createEmptyNode"
        />
      </div>

      <!-- 右侧配置面板 - 只在选中节点或连线时显示 -->
      <div class="config-panel" v-if="selectedNodeId || selectedEdgeId">
        <ConfigPanel @node-removed="handleNodeRemoved" />
      </div>
    </div>

    <!-- 指标选择弹窗 -->
    <div class="modal" v-if="showSelector">
      <div class="modal-backdrop" @click="closeSelector"></div>
      <div class="modal-content">
        <div class="modal-header">
          <h3>选择指标</h3>
          <button class="close-btn" @click="closeSelector">×</button>
        </div>
        <div class="modal-body">
          <IndicatorSelector
            :source-node-id="currentSourceNodeId"
            :relation-type="currentRelationType"
            @select="createRelatedNode"
          />
        </div>
      </div>
    </div>

    <!-- 计算关系选择弹窗 -->
    <div class="relation-selector-popup" v-if="showRelationSelector" :style="{ top: relationSelectorPosition.top, left: relationSelectorPosition.left }">
      <RelationTypeSelector
        :edge-id="currentEdgeId"
        :current-type="currentEdgeType"
        @select="updateEdgeRelation"
      />
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, computed, ref } from 'vue'
import { useRouter } from 'vue-router'
import { useNodeStore } from '../stores/node'
import { useEdgeStore } from '../stores/edge'
import type { Position } from '../types'
import Canvas from '../components/Canvas/Canvas.vue'
import ConfigPanel from '../components/ConfigPanel/ConfigPanel.vue'
import IndicatorSelector from '../components/IndicatorSelector/IndicatorSelector.vue'
import RelationTypeSelector from '../components/RelationTypeSelector/RelationTypeSelector.vue'

export default defineComponent({
  name: 'Editor',
  components: {
    Canvas,
    ConfigPanel,
    IndicatorSelector,
    RelationTypeSelector
  },
  setup() {
    const router = useRouter()
    const nodeStore = useNodeStore()
    const edgeStore = useEdgeStore()
    const canvasRef = ref(null)

    // 初始化CSS变量
    document.documentElement.style.setProperty('--relation-popup-triangle-top', '115px')

    const selectedNodeId = computed(() => nodeStore.selectedNodeId)
    const selectedEdgeId = computed(() => edgeStore.selectedEdgeId)
    const modelName = ref('销售业绩分析')

    // 指标选择器状态
    const showSelector = ref(false)
    const currentSourceNodeId = ref('')
    const currentRelationType = ref<any>(null)
    const connectorPosition = ref<any>(null)

    // 计算关系选择器状态
    const showRelationSelector = ref(false)
    const relationSelectorPosition = ref<{ top: string; left: string; position?: string }>({ top: '0px', left: '0px' })
    const currentEdgeId = ref('')
    const currentEdgeType = ref('')

    // 初始化一个根节点
    const initRootNode = () => {
      if (nodeStore.nodes.length === 0) {
        // 添加主节点 - 位置更靠近画布上方
        const rootNodeId = nodeStore.addNode({
          id: '',
          type: 'kpi',
          position: { x: 100, y: 80 }, // 减少Y坐标，使节点更靠近顶部
          data: {
            label: '零售价销售额',
            value: '49,194.473916',
            change: '+2.71%',
            period: '对比上周',
            color: '#1890ff',
            hasAttribution: true,
            attributionData: [
              {
                id: '1',
                name: '线上销售',
                value: 28563.25,
                percentage: 58,
                contribution: 1.8
              },
              {
                id: '2',
                name: '线下门店',
                value: 15642.75,
                percentage: 32,
                contribution: 0.6
              },
              {
                id: '3',
                name: '第三方渠道',
                value: 4988.48,
                percentage: 10,
                contribution: 0.3
              }
            ]
          }
        })

        // 添加子节点
        // 计算父节点中心点X坐标
        const rootNodeWidth = 240 // 根节点默认宽度
        const childNodeWidth = 240 // 子节点默认宽度
        const rootCenterX = 100 + rootNodeWidth / 2 // 根节点中心X坐标
        const horizontalOffset = 350 // 水平偏移量

        // 计算子节点位置，使其中心与父节点中心水平对齐，然后向右偏移
        // 确保子节点的中心点与父节点的中心点水平对齐
        // 子节点X坐标 = 父节点中心点X坐标 - 子节点宽度/2 + 水平偏移量
        const childNodeX = rootCenterX - childNodeWidth / 2 + horizontalOffset

        // 使用与父节点相同的Y坐标，确保完全水平对齐
        const childNodeY = 80 // 与父节点使用相同的Y坐标，保持在画布上方

        const childNodeId = nodeStore.addNode({
          id: '',
          type: 'input',
          position: { x: childNodeX, y: childNodeY }, // 使用计算后的X坐标，与父节点在同一水平线上
          data: {
            label: '线上销售额',
            value: '28,563.25',
            change: '+3.42%',
            period: '对比上周',
            color: '#52c41a'
          }
        })

        // 创建连线
        edgeStore.addEdge({
          id: '',
          source: rootNodeId,
          target: childNodeId,
          label: '+',
          type: 'add'
        })
      }
    }

    // 显示指标选择器
    const showIndicatorSelector = (nodeId: string, relationType: any) => {
      // 如果是从计算符号点击过来的，显示计算关系选择器
      if (relationType && relationType.name === '修改关系') {
        // 获取连线信息
        const edge = edgeStore.edges.find(e => e.source === nodeId && e.label === relationType.icon)
        if (edge) {
          // 使用传递的精确符号位置和画布信息
          const symbolInfo = relationType.symbolInfo
          showRelationSelectorForEdge(edge.id, edge.type || 'related', symbolInfo)
          return
        }
      }

      // 如果是从节点的加号按钮点击过来的，创建一个只有计算关系符号的连线
      if (relationType && relationType.name !== '修改关系') {
        // 保存当前选中的源节点和关系类型，以便后续创建完整的连线
        currentSourceNodeId.value = nodeId
        currentRelationType.value = relationType

        // 获取源节点
        const sourceNode = nodeStore.getNodeById(nodeId)
        if (!sourceNode) return

        // 创建一个临时的目标节点ID，用于创建只有计算关系符号的连线
        // 这个ID不会对应到实际的节点，只是为了创建连线
        const tempTargetId = 'temp-target-' + Date.now()

        // 创建连线
        edgeStore.addEdge({
          id: '',
          source: nodeId,
          target: tempTargetId, // 使用临时ID
          label: relationType.icon,
          type: relationType.id,
          isTemporary: true // 标记为临时连线
        })

        // 不显示指标选择器，等待用户点击连线
        return
      }

      // 否则显示指标选择器（这种情况不应该发生在新的流程中）
      currentSourceNodeId.value = nodeId
      currentRelationType.value = relationType
      connectorPosition.value = null
      showSelector.value = true
    }

    // 显示连接器指标选择器
    const showConnectorIndicatorSelector = (nodeId: string, relationType: string, position: any) => {
      // 获取源节点的所有连线（暂时不使用，但保留逻辑）
      // const edges = edgeStore.getEdgesBySource(nodeId)
      // const tempEdge = edges.find(edge => edge.isTemporary)

      // 设置当前源节点和关系类型
      currentSourceNodeId.value = nodeId
      currentRelationType.value = {
        id: relationType,
        name: getRelationName(relationType),
        icon: getRelationIcon(relationType)
      }
      connectorPosition.value = position

      // 显示指标选择器
      showSelector.value = true
    }

    // 显示计算关系选择器
    const showRelationSelectorForEdge = (edgeId: string, edgeType: string, symbolInfo?: any) => {
      currentEdgeId.value = edgeId
      currentEdgeType.value = edgeType

      // 弹窗尺寸常量
      const POPUP_TRIANGLE_POSITION = 115; // 弹窗三角形位置

      // 如果传入了符号信息，使用屏幕坐标直接定位弹窗
      if (symbolInfo && symbolInfo.screenX !== undefined && symbolInfo.screenY !== undefined) {
        // 使用屏幕坐标直接定位弹窗
        const popupTop = symbolInfo.screenY - POPUP_TRIANGLE_POSITION;
        // 增加水平间距，使弹窗往右移
        const horizontalOffset = 25; // 增加25px的水平间距
        const popupLeft = symbolInfo.screenX + horizontalOffset;

        // 设置弹窗位置 - 使用屏幕坐标，确保弹窗始终出现在符号右侧
        relationSelectorPosition.value = {
          top: `${popupTop}px`,
          left: `${popupLeft}px`
        };

        // 使用CSS变量设置定位方式
        document.documentElement.style.setProperty('--relation-popup-position', 'fixed');

        // 更新弹窗三角形位置，使其指向符号中心
        document.documentElement.style.setProperty('--relation-popup-triangle-top', `${POPUP_TRIANGLE_POSITION}px`);
      } else {
        // 兼容旧代码，使用画布坐标计算
        const edge = edgeStore.getEdgeById(edgeId)
        if (!edge) return

        const sourceNode = nodeStore.getNodeById(edge.source)
        const targetNode = nodeStore.getNodeById(edge.target)
        if (!sourceNode || !targetNode) return

        // 计算计算符号的位置 - 与Edge.vue组件中的symbolPosition计算保持一致
        const symbolX = sourceNode.position.x + (sourceNode.size?.width || 240) + 60
        const symbolY = sourceNode.position.y + (sourceNode.size?.height || 90) / 2

        // 增加水平间距，使弹窗往右移
        const horizontalOffset = 25; // 增加25px的水平间距

        // 设置选择器位置 - 调整位置使其出现在计算符号的右侧，稍微右移一点
        relationSelectorPosition.value = {
          top: `${symbolY - POPUP_TRIANGLE_POSITION}px`,
          left: `${symbolX + horizontalOffset}px`
        }

        // 使用CSS变量设置定位方式
        document.documentElement.style.setProperty('--relation-popup-position', 'absolute')
      }

      // 显示选择器
      showRelationSelector.value = true

      // 添加点击外部关闭选择器的事件
      setTimeout(() => {
        document.addEventListener('click', closeRelationSelector)
      }, 0)
    }

    // 关闭计算关系选择器
    const closeRelationSelector = (event?: MouseEvent) => {
      // 如果有事件参数，检查点击是否在弹窗内部
      if (event) {
        const target = event.target as HTMLElement
        // 如果点击在弹窗内部，不关闭弹窗
        if (target.closest('.relation-selector-popup')) return
      }

      // 关闭弹窗并移除事件监听器
      showRelationSelector.value = false
      document.removeEventListener('click', closeRelationSelector)
    }

    // 更新连线关系类型
    const updateEdgeRelation = (data: { edgeId: string, relationType: { id: string, name: string, icon: string } }) => {
      const edge = edgeStore.getEdgeById(data.edgeId)
      if (!edge) return

      // 更新连线类型和标签
      edgeStore.updateEdge(data.edgeId, {
        ...edge,
        type: data.relationType.id,
        label: data.relationType.icon
      })

      // 关闭选择器
      closeRelationSelector()
    }

    // 获取关系名称
    const getRelationName = (relationType: string) => {
      const relationMap: Record<string, string> = {
        'add': '相加关系',
        'subtract': '相减关系',
        'multiply': '相乘关系',
        'divide': '相除关系',
        'related': '相关关系'
      }
      return relationMap[relationType] || '相关关系'
    }

    // 获取关系图标
    const getRelationIcon = (relationType: string) => {
      const iconMap: Record<string, string> = {
        'add': '+',
        'subtract': '-',
        'multiply': '×',
        'divide': '÷',
        'related': 'C'
      }
      return iconMap[relationType] || 'C'
    }

    // 关闭指标选择器
    const closeSelector = () => {
      showSelector.value = false
    }

    // 创建空节点
    const createEmptyNode = (position: Position) => {
      // 创建一个新的根节点
      const rootNodeId = nodeStore.addNode({
        id: '',
        type: 'kpi',
        position: position,
        data: {
          label: '新建指标',
          value: '0',
          change: '0%',
          period: '对比上周',
          color: '#1890ff'
        }
      })

      // 选中新创建的节点
      nodeStore.selectNode(rootNodeId)
    }

    // 创建关联节点
    const createRelatedNode = (data: {
      sourceNodeId: string,
      relationType: { id: string, name: string, icon: string },
      indicator: any
    }) => {
      // 获取源节点
      const sourceNode = nodeStore.getNodeById(data.sourceNodeId)
      if (!sourceNode) return

      // 使用与自动布局相同的常量
      const PARENT_CHILD_SPACING = 380 // 与useLayoutCalculation.ts中的FIXED_HORIZONTAL_SPACING保持一致
      const NODE_WIDTH = sourceNode.size?.width || 240
      // const NODE_HEIGHT = sourceNode.size?.height || 90 // 暂时不使用

      // 计算子节点的基本X位置（与自动布局逻辑一致）
      const childX = sourceNode.position.x + NODE_WIDTH + PARENT_CHILD_SPACING

      // 简化的位置计算 - 使用临时位置，让自动布局来处理精确定位
      let newPosition: Position

      if (connectorPosition.value) {
        // 如果是从连接器创建的节点，使用连接器位置作为参考
        newPosition = {
          x: childX,
          y: connectorPosition.value.y
        }
      } else {
        // 使用简单的临时位置，让自动布局来处理精确的对称分布
        // 这里只需要确保X位置正确，Y位置由自动布局优化
        newPosition = {
          x: childX,
          y: sourceNode.position.y // 临时使用父节点的Y位置
        }
      }

      // 创建新节点 - 使用临时位置，让自动布局处理精确定位
      const newNodeId = nodeStore.addNode({
        id: '',
        type: 'kpi',
        position: newPosition, // 使用简单的临时位置
        data: {
          label: data.indicator.label,
          value: data.indicator.value,
          change: data.indicator.change,
          period: data.indicator.period,
          color: data.indicator.color || '#1890ff'
        }
      })

      // 查找是否存在临时连线
      const tempEdges = edgeStore.edges.filter(edge =>
        edge.source === data.sourceNodeId && edge.isTemporary
      )

      // 如果存在临时连线，更新它
      if (tempEdges.length > 0) {
        // 更新第一个临时连线
        edgeStore.updateEdge(tempEdges[0].id, {
          target: newNodeId,
          isTemporary: false // 移除临时标记
        })
      } else {
        // 如果不存在临时连线，创建新连线
        edgeStore.addEdge({
          id: '',
          source: data.sourceNodeId,
          target: newNodeId,
          label: data.relationType.icon,
          type: data.relationType.id
        })
      }

      // 应用自动布局 - 使用优化过的布局算法
      // 延迟执行，确保节点和连线都已创建完成
      setTimeout(() => {
        if (canvasRef.value) {
          console.log('新节点创建完成，应用自动布局')
          // 使用与手动自动布局相同的逻辑
          canvasRef.value.applyAutoLayout(true)
        }
      }, 100)

      // 关闭选择器
      closeSelector()
    }

    // 处理节点删除事件
    const handleNodeRemoved = (nodeId: string) => {
      // 递归删除与节点相关的所有连线和子节点
      removeAllRelatedEdges(nodeId)
    }

    // 递归删除与节点相关的所有连线和子节点
    const removeAllRelatedEdges = (nodeId: string) => {
      // 获取以该节点为源节点的所有连线
      const outgoingEdges = edgeStore.edges.filter(edge => edge.source === nodeId)

      // 对于每条出边，递归删除目标节点及其相关连线
      outgoingEdges.forEach(edge => {
        const targetNodeId = edge.target

        // 删除连线
        edgeStore.removeEdge(edge.id)

        // 递归删除目标节点及其相关连线
        removeAllRelatedEdges(targetNodeId)

        // 删除目标节点
        nodeStore.removeNode(targetNodeId)
      })

      // 删除以该节点为目标节点的所有连线
      const incomingEdges = edgeStore.edges.filter(edge => edge.target === nodeId)
      incomingEdges.forEach(edge => {
        edgeStore.removeEdge(edge.id)
      })
    }

    const togglePreviewMode = () => {
      router.push('/preview')
    }

    const saveModel = () => {
      // 保存模型逻辑
      alert('模型已保存')
    }

    // 应用自动布局
    const applyAutoLayout = () => {
      if (canvasRef.value) {
        // 显示加载提示
        const loadingMessage = '正在优化布局...'
        console.log(loadingMessage)

        // 第一步：应用基本布局
        canvasRef.value?.applyAutoLayout(true)

        // 添加延迟，确保布局完全应用后再次检查和优化
        setTimeout(() => {
          // 第二步：优化节点间距和连线
          canvasRef.value?.applyAutoLayout(true)

          // 第三步：特别关注子节点与父节点的对齐
          setTimeout(() => {
            canvasRef.value?.applyAutoLayout(true)

            // 第四步：最终确保连线路径合理
            setTimeout(() => {
              // 最后一次应用布局，确保所有优化都已应用
              canvasRef.value?.applyAutoLayout(true)

              // 第五步：特别处理第三级节点重叠问题
              setTimeout(() => {
                // 构建节点关系树
                const buildNodeTree = () => {
                  const nodeMap = new Map<string, { node: any, children: string[], parent: string | null }>()

                  // 初始化节点映射
                  nodeStore.nodes.forEach(node => {
                    nodeMap.set(node.id, { node, children: [], parent: null })
                  })

                  // 构建父子关系
                  edgeStore.edges.forEach(edge => {
                    const sourceNode = nodeMap.get(edge.source)
                    const targetNode = nodeMap.get(edge.target)

                    if (sourceNode && targetNode) {
                      sourceNode.children.push(edge.target)
                      targetNode.parent = edge.source
                    }
                  })

                  // 找出根节点（没有父节点的节点）
                  const rootNodes: string[] = []
                  nodeStore.nodes.forEach(node => {
                    const nodeInfo = nodeMap.get(node.id)
                    if (nodeInfo && nodeInfo.parent === null) {
                      rootNodes.push(node.id)
                    }
                  })

                  return { nodeMap, rootNodes }
                }

                const { nodeMap, rootNodes } = buildNodeTree()

                // 获取所有节点的深度信息
                const nodeDepths = new Map<string, number>()

                // 计算节点深度的递归函数
                const calculateDepth = (nodeId: string, depth: number = 0) => {
                  nodeDepths.set(nodeId, depth)

                  const nodeInfo = nodeMap.get(nodeId)
                  if (!nodeInfo) return

                  nodeInfo.children.forEach(childId => {
                    calculateDepth(childId, depth + 1)
                  })
                }

                // 从根节点开始计算深度
                rootNodes.forEach(rootId => {
                  calculateDepth(rootId)
                })

                // 获取所有第三级或更深的节点
                const deepNodes = Array.from(nodeDepths.entries())
                  .filter(([_, depth]) => depth >= 2)
                  .map(([nodeId, _]) => nodeId)

                // 检查每个深层节点是否与其"叔叔"节点的子节点（表兄弟）重叠
                let needsAnotherLayout = false

                deepNodes.forEach(nodeId => {
                  const node = nodeStore.getNodeById(nodeId)
                  if (!node) return

                  const nodeInfo = nodeMap.get(nodeId)
                  if (!nodeInfo || !nodeInfo.parent) return

                  // 获取父节点信息
                  const parentInfo = nodeMap.get(nodeInfo.parent)
                  if (!parentInfo || !parentInfo.parent) return

                  // 获取祖父节点信息
                  const grandparentInfo = nodeMap.get(parentInfo.parent)
                  if (!grandparentInfo) return

                  // 获取父节点的所有兄弟节点（叔叔节点）
                  const uncleNodeIds = grandparentInfo.children.filter(id => id !== nodeInfo.parent)

                  // 获取所有表兄弟节点（叔叔的子节点）
                  const cousinNodes: any[] = []

                  uncleNodeIds.forEach(uncleId => {
                    const uncleInfo = nodeMap.get(uncleId)
                    if (uncleInfo) {
                      uncleInfo.children.forEach(cousinId => {
                        const cousinNode = nodeStore.getNodeById(cousinId)
                        if (cousinNode) cousinNodes.push(cousinNode)
                      })
                    }
                  })

                  // 检查是否与任何表兄弟节点重叠
                  const overlappingNodes = cousinNodes.filter(cousinNode =>
                    Math.abs(cousinNode.position.x - node.position.x) < 240 &&
                    Math.abs(cousinNode.position.y - node.position.y) < 90
                  )

                  // 如果有重叠，标记需要再次应用布局
                  if (overlappingNodes.length > 0) {
                    needsAnotherLayout = true
                  }
                })

                // 如果检测到重叠，再次应用布局
                if (needsAnotherLayout) {
                  canvasRef.value?.applyAutoLayout(true)
                }

                // 通知用户布局已完成
                console.log('自动布局已应用')
              }, 400)
            }, 400)
          }, 400)
        }, 400)
      }
    }


    // 控制是否自动创建初始节点
    const autoCreateInitialNode = ref(false) // 设置为false，不自动创建初始节点

    // 只有在需要时才初始化根节点
    if (autoCreateInitialNode.value) {
      initRootNode()
    }

    return {
      selectedNodeId,
      selectedEdgeId,
      modelName,
      showSelector,
      currentSourceNodeId,
      currentRelationType,
      showRelationSelector,
      relationSelectorPosition,
      currentEdgeId,
      currentEdgeType,
      showIndicatorSelector,
      showConnectorIndicatorSelector,
      closeSelector,
      createRelatedNode,
      createEmptyNode,
      updateEdgeRelation,
      togglePreviewMode,
      saveModel,
      handleNodeRemoved,
      applyAutoLayout,
      canvasRef
    }
  }
})
</script>

<style lang="scss" scoped>
.editor-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f7fa;

  .header-bar {
    height: 56px;
    background-color: #fff;
    border-bottom: 1px solid #e8e8e8;
    display: flex;
    align-items: center;
    padding: 0 16px;
    justify-content: space-between;

    .left-section {
      display: flex;
      align-items: center;

      .back-btn {
        display: flex;
        align-items: center;
        color: #666;
        margin-right: 16px;

        .icon {
          margin-right: 4px;
        }

        &:hover {
          color: #1890ff;
        }
      }

      .title {
        font-size: 16px;
        font-weight: 500;
        color: #333;
        margin-right: 8px;
      }

      .edit-icon {
        color: #999;
        cursor: pointer;

        &:hover {
          color: #1890ff;
        }
      }
    }

    .right-section {
      display: flex;
      align-items: center;

      button {
        margin-left: 8px;
      }
    }
  }

  .toolbar {
    height: 48px;
    background-color: #fff;
    border-bottom: 1px solid #e8e8e8;
    display: flex;
    align-items: center;
    padding: 0 16px;
    margin-bottom: 16px;

    .date-picker {
      display: flex;
      align-items: center;
      margin-right: 16px;

      .label {
        color: #666;
        margin-right: 8px;
      }

      .date-input {
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        padding: 4px 8px;
        width: 120px;
      }
    }

    .batch-config {
      margin-right: auto;
    }

    .layout-tools {
      display: flex;
      align-items: center;
      margin-right: 16px;

      button {
        display: flex;
        align-items: center;

        .icon {
          margin-right: 4px;
          font-size: 14px;
        }
      }
    }

    .search-tools {
      display: flex;

      .tool-btn {
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 8px;
        border-radius: 4px;

        &:hover {
          background-color: #f0f0f0;
        }
      }
    }
  }

  .main-content {
    display: flex;
    flex: 1;
    overflow: hidden;
    padding: 0 0 16px 16px;

    .canvas-container {
      flex: 1;
      position: relative;
      overflow: hidden;
      background-color: #fff;
      border-radius: 4px 0 0 4px;
      box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
    }

    .config-panel {
      width: 320px;
      background-color: #fff;
      display: flex;
      flex-direction: column;
      overflow: hidden;
      box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);

      .empty-state {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #999;
        height: 100%;
      }
    }
  }

  .modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;

    .modal-backdrop {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
    }

    .modal-content {
      position: relative;
      width: 500px;
      max-height: 80vh;
      background-color: #fff;
      border-radius: 4px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      display: flex;
      flex-direction: column;

      .modal-header {
        padding: 16px;
        border-bottom: 1px solid #f0f0f0;
        display: flex;
        align-items: center;
        justify-content: space-between;

        h3 {
          margin: 0;
          font-size: 16px;
          font-weight: 500;
        }

        .close-btn {
          background: none;
          border: none;
          font-size: 20px;
          color: #999;
          cursor: pointer;

          &:hover {
            color: #666;
          }
        }
      }

      .modal-body {
        flex: 1;
        overflow-y: auto;
        padding: 16px;
        max-height: 500px;
      }
    }
  }

  .relation-selector-popup {
    position: var(--relation-popup-position, absolute); /* 使用CSS变量控制定位方式 */
    z-index: 1000;
    filter: drop-shadow(0 2px 8px rgba(0, 0, 0, 0.15));

    /* 添加一个小三角形指向计算符号 */
    &::before {
      content: '';
      position: absolute;
      left: -8px;
      top: var(--relation-popup-triangle-top, 115px); /* 使用CSS变量控制三角形位置 */
      width: 0;
      height: 0;
      border-top: 8px solid transparent;
      border-bottom: 8px solid transparent;
      border-right: 8px solid white;
      z-index: 1; /* 确保三角形在弹窗内容之上 */
      filter: drop-shadow(-1px 0 1px rgba(0, 0, 0, 0.1)); /* 添加阴影效果，使三角形更明显 */
    }
  }
}

.btn-default {
  background-color: #fff;
  color: #666;
  padding: 5px 12px;
  border-radius: 4px;
  border: 1px solid #d9d9d9;

  &:hover {
    color: #40a9ff;
    border-color: #40a9ff;
  }
}

.btn-primary {
  background-color: #1890ff;
  color: white;
  padding: 5px 12px;
  border-radius: 4px;

  &:hover {
    background-color: #40a9ff;
  }
}
</style>