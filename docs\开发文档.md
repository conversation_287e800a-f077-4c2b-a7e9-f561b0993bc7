# 可视化配置系统开发文档

## 1. 系统概述

可视化配置系统是一个基于Vue 3的前端应用，用于创建和管理节点关系图，支持节点参数配置、节点连接计算关系、自动布局等功能。系统提供了直观的可视化界面，使用户能够轻松创建、编辑和管理复杂的节点关系网络，特别适用于业务指标分析、数据流程建模和决策支持系统。

## 2. 技术架构

### 2.1 前端框架
- **Vue 3.5.13**: 采用组合式API（Composition API）开发，提供更好的代码组织和类型推断
- **TypeScript**: 提供静态类型检查，增强代码可维护性
- **Vite 6.3.4**: 现代前端构建工具，提供快速的开发体验和高效的构建过程

### 2.2 状态管理
- **Pinia 3.0.2**: Vue官方推荐的状态管理库，替代Vuex，提供更简洁的API和更好的TypeScript支持

### 2.3 路由
- **Vue Router 4.5.1**: Vue官方路由管理器，支持历史模式导航

### 2.4 样式
- **SCSS/SASS 1.87.0**: CSS预处理器，提供变量、嵌套、混合等高级功能

### 2.5 工具库
- **UUID 11.1.0**: 用于生成唯一标识符，确保节点和连线的唯一性

## 3. 核心数据模型

### 3.1 节点（Node）

```typescript
interface Node {
  id: string;              // 节点唯一标识符
  type: string;            // 节点类型（kpi, input, output, process）
  position: {              // 节点位置
    x: number;
    y: number;
  };
  size?: {                 // 节点尺寸
    width: number;
    height: number;
  };
  data: {                  // 节点数据
    label: string;         // 节点标签
    value: string;         // 节点值
    change: string;        // 变化趋势
    period: string;        // 统计周期
    color?: string;        // 节点颜色
    borderStyle?: string;  // 边框样式
    borderWidth?: number;  // 边框宽度
    borderRadius?: number; // 边框圆角
    textColor?: string;    // 文本颜色
    backgroundColor?: string; // 背景颜色
    hasAttribution?: boolean; // 是否有归因分析
    attributionData?: AttributionItem[]; // 归因分析数据
    [key: string]: any;    // 其他自定义属性
  };
}
```

### 3.2 连线（Edge）

```typescript
interface Edge {
  id: string;              // 连线唯一标识符
  source: string;          // 源节点ID
  target: string;          // 目标节点ID
  label?: string;          // 连线标签（计算符号）
  type?: string;           // 连线类型（add, subtract, multiply, divide, related）
  color?: string;          // 连线颜色
  lineStyle?: string;      // 线条样式
  [key: string]: any;      // 其他自定义属性
}
```

### 3.3 归因项（AttributionItem）

```typescript
interface AttributionItem {
  id: string;              // 归因项唯一标识符
  name: string;            // 归因项名称
  value: number;           // 归因项值
  percentage: number;      // 归因百分比
  contribution: number;    // 贡献度
}
```

## 4. 核心模块

### 4.1 画布模块（Canvas）

画布模块是系统的核心，负责节点和连线的渲染、交互和布局。

#### 4.1.1 主要功能
- 节点渲染与交互
- 连线渲染与交互
- 画布缩放与平移
- 自动布局算法
- 拖拽操作处理
- 连线创建与管理
- 缩略图显示

#### 4.1.2 关键API

```typescript
// 选择节点
function selectNode(id: string): void

// 移动节点
function moveNode(id: string, position: Position): void

// 开始连接节点
function startConnecting(sourceId: string): void

// 完成节点连接
function finishConnecting(targetId: string): void

// 应用自动布局
function applyAutoLayout(force: boolean = false): void

// 缩放画布
function zoomCanvas(delta: number, center: Position): void

// 平移画布
function panCanvas(deltaX: number, deltaY: number): void
```

### 4.2 节点模块（Node）

节点模块负责单个节点的渲染、交互和数据管理。

#### 4.2.1 主要功能
- 节点渲染
- 节点拖拽
- 节点连接
- 节点样式配置
- 归因分析

#### 4.2.2 关键API

```typescript
// 获取所有节点
function getNodes(): Node[]

// 获取指定节点
function getNodeById(id: string): Node | null

// 添加节点
function addNode(node: Node): string

// 更新节点
function updateNode(id: string, updates: Partial<Node>): void

// 删除节点
function removeNode(id: string): void

// 选择节点
function selectNode(id: string | null): void
```

### 4.3 连线模块（Edge）

连线模块负责节点间连线的渲染、交互和数据管理。

#### 4.3.1 主要功能
- 连线渲染
- 连线样式配置
- 计算关系表示
- 连线交互

#### 4.3.2 关键API

```typescript
// 获取所有连线
function getEdges(): Edge[]

// 获取指定连线
function getEdgeById(id: string): Edge | null

// 添加连线
function addEdge(edge: Edge): string

// 更新连线
function updateEdge(id: string, updates: Partial<Edge>): void

// 删除连线
function removeEdge(id: string): void

// 选择连线
function selectEdge(id: string | null): void
```

### 4.4 配置面板模块（ConfigPanel）

配置面板模块负责节点和连线属性的配置和编辑。

#### 4.4.1 主要功能
- 节点基础属性配置
- 节点样式配置
- 节点数据配置
- 节点高级配置
- 连线属性配置
- 归因分析配置

#### 4.4.2 关键API

```typescript
// 更新节点标签
function updateNodeLabel(label: string): void

// 更新节点类型
function updateNodeType(type: string): void

// 更新节点颜色
function updateNodeColor(color: string): void

// 更新节点边框样式
function updateNodeBorderStyle(style: string): void

// 更新归因分析状态
function updateAttributionStatus(enabled: boolean): void

// 添加归因项
function addAttributionItem(): void

// 更新连线标签
function updateEdgeLabel(label: string): void

// 更新连线类型
function updateEdgeType(type: string): void
```

### 4.5 关系类型选择器模块（RelationTypeSelector）

关系类型选择器模块负责节点间关系类型的选择和管理。

#### 4.5.1 主要功能
- 关系类型选择
- 关系图标显示

#### 4.5.2 关键API

```typescript
// 选择关系类型
function selectRelation(relation: { id: string, name: string, icon: string }): void
```

### 4.6 归因分析弹窗模块（AttributionPopup）

归因分析弹窗模块负责显示节点的归因分析数据。

#### 4.6.1 主要功能
- 归因数据展示
- 贡献度可视化

#### 4.6.2 关键API

```typescript
// 显示归因分析弹窗
function showAttributionPopup(nodeId: string): void

// 关闭归因分析弹窗
function closeAttributionPopup(): void
```

### 4.7 画布缩略图模块（CanvasThumbnail）

画布缩略图模块负责显示画布的缩略视图，便于整体导航。

#### 4.7.1 主要功能
- 画布缩略视图显示
- 视口位置指示
- 快速导航

#### 4.7.2 关键API

```typescript
// 平移画布
function panCanvas(position: Position): void

// 获取节点颜色
function getNodeColor(node: Node): string

// 获取连线路径
function getEdgePath(edge: Edge): string
```

## 5. 自动布局算法

自动布局算法是系统的核心功能之一，确保节点在画布中的合理分布。

### 5.1 布局原则

1. 当节点只有一个子节点时，子节点与父节点水平中心对齐
2. 当节点有多个子节点时，子节点对称分布，与父节点形成局部对称效果
3. 新增节点时，确保不与现有节点重叠
4. 子节点与父节点之间保持固定的水平间距
5. 父节点与叔叔节点（同级节点）之间的垂直间距大于子节点之间的垂直间距

### 5.2 算法实现

```typescript
// 应用自动布局
function applyAutoLayout(force = false) {
  // 如果自动布局被禁用且不是强制执行，则直接返回
  if (!autoLayoutEnabled.value && !force) return

  // 强制执行时，重新启用自动布局
  if (force) {
    autoLayoutEnabled.value = true
  }

  // 构建节点关系树
  const { nodeMap, rootNodes } = buildNodeTree()

  // 全局协调布局
  coordinateGlobalLayout(nodeMap, rootNodes)
}

// 构建节点关系树
function buildNodeTree() {
  const nodeMap = new Map()

  // 初始化节点映射
  nodes.value.forEach(node => {
    nodeMap.set(node.id, {
      node,
      children: [],
      parent: null,
      depth: 0,
      siblingIndex: 0,
      hasSingleChild: false,
      totalSiblings: 0,
      parentGroup: '',
      siblingNodes: []
    })
  })

  // 构建父子关系
  edges.value.forEach(edge => {
    const sourceInfo = nodeMap.get(edge.source)
    const targetInfo = nodeMap.get(edge.target)

    if (sourceInfo && targetInfo) {
      sourceInfo.children.push(edge.target)
      targetInfo.parent = edge.source
    }
  })

  // 找出根节点
  const rootNodes = []
  nodeMap.forEach((info, nodeId) => {
    if (!info.parent) {
      rootNodes.push(nodeId)
    }
  })

  return { nodeMap, rootNodes }
}
```

## 6. 状态管理

系统使用Pinia进行状态管理，主要包括以下三个Store：

### 6.1 节点状态（NodeStore）

```typescript
// 节点状态
const nodeStore = defineStore('node', {
  state: () => ({
    nodes: [],
    selectedNodeId: null
  }),

  getters: {
    getNodeById: (state) => (id) => state.nodes.find(node => node.id === id) || null,
    selectedNode: (state) => state.nodes.find(node => node.id === state.selectedNodeId) || null
  },

  actions: {
    addNode(node) { /* ... */ },
    updateNode(id, updates) { /* ... */ },
    moveNode(id, position) { /* ... */ },
    removeNode(id) { /* ... */ },
    selectNode(id) { /* ... */ },
    clearNodes() { /* ... */ },
    addNodes(nodes) { /* ... */ }
  }
})
```

### 6.2 连线状态（EdgeStore）

```typescript
// 连线状态
const edgeStore = defineStore('edge', {
  state: () => ({
    edges: [],
    selectedEdgeId: null
  }),

  getters: {
    getEdgeById: (state) => (id) => state.edges.find(edge => edge.id === id) || null,
    getEdgesBySource: (state) => (sourceId) => state.edges.filter(edge => edge.source === sourceId),
    getEdgesByTarget: (state) => (targetId) => state.edges.filter(edge => edge.target === targetId),
    selectedEdge: (state) => state.edges.find(edge => edge.id === state.selectedEdgeId) || null
  },

  actions: {
    addEdge(edge) { /* ... */ },
    updateEdge(id, updates) { /* ... */ },
    removeEdge(id) { /* ... */ },
    removeEdgesConnectedToNode(nodeId) { /* ... */ },
    selectEdge(id) { /* ... */ },
    clearEdges() { /* ... */ },
    addEdges(edges) { /* ... */ }
  }
})
```

### 6.3 画布状态（CanvasStore）

```typescript
// 画布状态
const canvasStore = defineStore('canvas', {
  state: () => ({
    scale: 1,
    position: { x: 0, y: 0 },
    dimensions: { width: 5000, height: 5000 }
  }),

  actions: {
    setScale(scale) { /* ... */ },
    setPosition(position) { /* ... */ },
    setDimensions(dimensions) { /* ... */ },
    zoom(delta, center) { /* ... */ },
    pan(deltaX, deltaY) { /* ... */ },
    resetView() { /* ... */ }
  }
})
```

## 7. 系统功能与交互

### 7.1 编辑模式

编辑模式是系统的主要工作模式，提供完整的节点和连线编辑功能。

#### 7.1.1 节点操作
- **创建节点**：点击画布空白区域创建新节点
- **选择节点**：点击节点选中，显示配置面板
- **移动节点**：拖拽节点改变位置
- **删除节点**：在配置面板中点击删除按钮
- **配置节点**：在右侧配置面板中编辑节点属性

#### 7.1.2 连线操作
- **创建连线**：从源节点的出口拖拽到目标节点的入口
- **选择连线**：点击连线选中，显示配置面板
- **配置连线**：在右侧配置面板中编辑连线属性
- **删除连线**：在配置面板中点击删除按钮
- **修改计算关系**：点击连线上的计算符号，选择关系类型

#### 7.1.3 画布操作
- **缩放画布**：鼠标滚轮缩放
- **平移画布**：拖拽画布背景
- **自动布局**：点击工具栏中的自动布局按钮
- **查看缩略图**：右下角显示画布缩略图，可快速导航

### 7.2 预览模式

预览模式用于查看和分析已配置的节点关系图，不允许编辑操作。

#### 7.2.1 查看功能
- **浏览节点**：查看节点数据和关系
- **缩放平移**：调整视图
- **归因分析**：点击节点查看归因分析弹窗

## 8. 计算关系类型

系统支持多种节点间的计算关系类型：

### 8.1 相加关系（add）
- 符号：`+`
- 描述：目标节点值是源节点值的相加结果

### 8.2 相减关系（subtract）
- 符号：`-`
- 描述：目标节点值是源节点值的相减结果

### 8.3 相乘关系（multiply）
- 符号：`×`
- 描述：目标节点值是源节点值的相乘结果

### 8.4 相除关系（divide）
- 符号：`÷`
- 描述：目标节点值是源节点值的相除结果

### 8.5 相关关系（related）
- 符号：`C`
- 描述：目标节点与源节点存在相关性，但没有具体的计算关系

## 9. 归因分析

归因分析是系统的高级功能，用于分析指标的影响因素和贡献度。

### 9.1 归因数据结构

```typescript
interface AttributionItem {
  id: string;              // 归因项唯一标识符
  name: string;            // 归因项名称
  value: number;           // 归因项值
  percentage: number;      // 归因百分比
  contribution: number;    // 贡献度
}
```

### 9.2 归因分析配置

在节点配置面板的高级配置选项卡中，可以：
- 启用/禁用归因分析
- 添加归因项
- 配置归因项的名称、值、百分比和贡献度

### 9.3 归因分析展示

在预览模式下，点击启用了归因分析的节点，会弹出归因分析弹窗，显示：
- 归因项列表
- 每个归因项的值和百分比
- 贡献度可视化

## 10. 画布缩略图

画布缩略图是系统的辅助功能，提供画布的整体视图和快速导航能力。

### 10.1 主要功能
- 显示画布内容的缩略视图
- 指示当前视口位置
- 支持点击导航到画布的不同区域
- 支持拖拽视口指示器平移画布

### 10.2 实现原理

缩略图通过计算缩放比例，将画布内容按比例缩小显示：

```typescript
// 计算缩略图的缩放比例
const thumbnailScale = computed(() => {
  // 缩略图的尺寸
  const thumbnailWidth = 220
  const thumbnailHeight = 120

  // 计算缩放比例，使其与主画布保持相同的比例
  const scaleX = thumbnailWidth / props.containerSize.width
  const scaleY = thumbnailHeight / props.containerSize.height

  // 取较小的缩放比例，确保整个视口内容都能显示
  return Math.min(scaleX, scaleY) * 0.7 // 稍微缩小一点，留出边距
})
```

## 11. 安装与部署

### 11.1 环境要求
- Node.js 18.0.0 或更高版本
- pnpm 8.0.0 或更高版本（推荐）或 npm

### 11.2 安装依赖
```bash
# 使用pnpm（推荐）
pnpm install

# 或使用npm
npm install
```

### 11.3 开发模式运行
```bash
# 使用pnpm
pnpm start
# 或
pnpm dev

# 或使用npm
npm run dev
```

### 11.4 构建生产版本
```bash
# 使用pnpm
pnpm build

# 或使用npm
npm run build
```

### 11.5 部署方式

#### 11.5.1 传统Web服务器（Nginx/Apache）
1. 构建项目：`pnpm build`
2. 将`dist`目录中的文件上传到Web服务器
3. 配置服务器路由，将所有请求重定向到index.html

#### 11.5.2 Docker容器
1. 创建Dockerfile
2. 构建并运行Docker容器

## 12. 性能优化

系统实现了多项性能优化措施，确保在处理大量节点和连线时保持流畅的用户体验：

### 12.1 计算属性缓存
- 使用Vue的计算属性缓存复杂计算结果
- 只在依赖变化时重新计算

### 12.2 事件节流与防抖
- 对频繁触发的事件（如鼠标移动、滚轮缩放）应用节流和防抖技术
- 减少不必要的计算和渲染

### 12.3 布局算法优化
- 使用增量更新而非全量重新计算
- 只在必要时应用自动布局
- 拖拽节点时暂时禁用自动布局

## 13. 扩展与定制

系统设计考虑了扩展性，可以通过以下方式进行定制：

### 13.1 添加新的节点类型
1. 在类型定义中添加新的节点类型
2. 在Node组件中添加新类型的样式和行为
3. 在ConfigPanel中添加新类型的配置选项

### 13.2 添加新的关系类型
1. 在RelationTypeSelector中添加新的关系类型
2. 在Edge组件中添加新关系类型的渲染逻辑
3. 更新自动布局算法以支持新的关系类型

### 13.3 自定义样式
- 修改SCSS变量定制主题
- 添加新的颜色选项
- 自定义节点和连线的视觉效果