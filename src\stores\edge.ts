import { defineStore } from 'pinia'
import type { EdgeState, Edge } from '../types'
import { v4 as uuidv4 } from 'uuid'

export const useEdgeStore = defineStore('edge', {
  state: (): EdgeState => ({
    edges: [],
    selectedEdgeId: null
  }),
  
  getters: {
    getEdgeById: (state) => (id: string) => {
      return state.edges.find(edge => edge.id === id) || null
    },
    
    getEdgesBySource: (state) => (sourceId: string) => {
      return state.edges.filter(edge => edge.source === sourceId)
    },
    
    getEdgesByTarget: (state) => (targetId: string) => {
      return state.edges.filter(edge => edge.target === targetId)
    },
    
    selectedEdge: (state): Edge | null => {
      if (!state.selectedEdgeId) return null
      return state.edges.find(edge => edge.id === state.selectedEdgeId) || null
    }
  },
  
  actions: {
    // 添加连线
    addEdge(edge: Edge) {
      // 检查是否已存在相同的连线
      const existingEdge = this.edges.find(
        e => e.source === edge.source && e.target === edge.target
      )
      
      if (existingEdge) {
        return existingEdge.id
      }
      
      // 确保连线有唯一ID
      const newEdge = {
        ...edge,
        id: edge.id || uuidv4()
      }
      
      this.edges.push(newEdge)
      return newEdge.id
    },
    
    // 更新连线
    updateEdge(id: string, updates: Partial<Edge>) {
      const index = this.edges.findIndex(edge => edge.id === id)
      if (index !== -1) {
        this.edges[index] = { ...this.edges[index], ...updates }
      }
    },
    
    // 删除连线
    removeEdge(id: string) {
      this.edges = this.edges.filter(edge => edge.id !== id)
      if (this.selectedEdgeId === id) {
        this.selectedEdgeId = null
      }
    },
    
    // 删除与节点相关的所有连线
    removeEdgesConnectedToNode(nodeId: string) {
      this.edges = this.edges.filter(
        edge => edge.source !== nodeId && edge.target !== nodeId
      )
      
      if (this.selectedEdgeId && !this.getEdgeById(this.selectedEdgeId)) {
        this.selectedEdgeId = null
      }
    },
    
    // 选择连线
    selectEdge(id: string | null) {
      this.selectedEdgeId = id
    },
    
    // 清空所有连线
    clearEdges() {
      this.edges = []
      this.selectedEdgeId = null
    },
    
    // 批量添加连线
    addEdges(edges: Edge[]) {
      const newEdges = edges.map(edge => ({
        ...edge,
        id: edge.id || uuidv4()
      }))
      
      this.edges.push(...newEdges)
    }
  }
})