<template>
  <div>
    <!-- 空画布背景提示 -->
    <div
      v-if="!readonly && nodes.length === 0"
      class="empty-canvas-background-hint"
    >
      <div class="hint-text">右键点击画布任意位置也可添加节点</div>
    </div>

    <!-- 空画布节点提示 -->
    <div
      v-if="!readonly && nodes.length === 0"
      class="empty-canvas-node-prompt"
      @click="createEmptyNode"
      @contextmenu.prevent="onEmptyCanvasRightClick"
    >
      <div class="node-header">
        <div class="header-text">新建指标</div>
      </div>
      <div class="node-content">
        <div class="add-icon-container">
          <div class="add-icon">+</div>
        </div>
        <div class="prompt-text">点击此处添加节点</div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue'

export default defineComponent({
  name: 'CanvasEmptyState',
  props: {
    readonly: {
      type: Boolean,
      default: false
    },
    nodes: {
      type: Array,
      required: true
    }
  },
  setup(props, { emit }) {
    // Handle empty node creation
    const createEmptyNode = () => {
      emit('create-empty-node')
    }

    // Handle right-click on empty canvas
    const onEmptyCanvasRightClick = (event: MouseEvent) => {
      emit('empty-canvas-right-click', event)
    }

    return {
      createEmptyNode,
      onEmptyCanvasRightClick
    }
  }
})
</script>

<style lang="scss" scoped>
.empty-canvas-background-hint {
  position: absolute;
  bottom: 30px;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  z-index: 50;
  pointer-events: none;

  .hint-text {
    background-color: rgba(255, 255, 255, 0.9);
    padding: 8px 16px;
    border-radius: 20px;
    color: #666;
    font-size: 13px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    animation: fadeInUp 0.5s ease-out forwards;
    animation-delay: 1s;
    opacity: 0;
    transform: translateY(20px);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.empty-canvas-node-prompt {
  position: absolute;
  top: 50%;
  left: 30%; /* 将位置从50%（居中）调整为30%（偏左） */
  transform: translate(-50%, -50%);
  width: 240px;
  background-color: #fff;
  border-radius: 4px;
  border-left: 4px solid #1890ff;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  animation: float 3s ease-in-out infinite;
  z-index: 1000; /* 增加z-index确保在最上层 */
  pointer-events: auto !important; /* 确保可点击 */

  &:hover {
    box-shadow: 0 4px 16px rgba(24, 144, 255, 0.25);
    transform: translate(-50%, -50%) scale(1.02);
  }

  .node-header {
    padding: 10px 12px;
    border-bottom: 1px solid #f0f0f0;
    background-color: #fafafa;

    .header-text {
      font-size: 14px;
      color: #333;
      font-weight: 500;
    }
  }

  .node-content {
    padding: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;

    .add-icon-container {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      background-color: rgba(24, 144, 255, 0.1);
      display: flex;
      justify-content: center;
      align-items: center;
      margin-bottom: 16px;
      transition: all 0.3s ease;

      .add-icon {
        font-size: 36px;
        color: #1890ff;
        line-height: 1;
        font-weight: 300;
      }
    }

    .prompt-text {
      color: #666;
      font-size: 14px;
      text-align: center;
    }
  }
}

@keyframes float {
  0% {
    transform: translate(-50%, -50%) translateY(0px);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  }
  50% {
    transform: translate(-50%, -50%) translateY(-10px);
    box-shadow: 0 15px 20px rgba(0, 0, 0, 0.05);
  }
  100% {
    transform: translate(-50%, -50%) translateY(0px);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  }
}
</style>
