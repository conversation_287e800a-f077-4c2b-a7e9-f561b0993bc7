<template>
  <div class="config-panel">
    <div class="panel-header">节点属性</div>

    <div v-if="selectedNode" class="node-config">
      <!-- 选项卡导航 -->
      <div class="tabs-container">
        <div class="tabs">
          <div
            v-for="tab in tabs"
            :key="tab.id"
            :class="['tab', { active: activeTab === tab.id }]"
            @click="activeTab = tab.id"
          >
            {{ tab.name }}
          </div>
        </div>
      </div>

      <div class="tab-content-container">
        <!-- 基础属性选项卡 -->
        <div v-if="activeTab === 'basic'" class="tab-content">
          <div class="form-group">
            <label class="label">节点名称</label>
            <div class="input-wrapper">
              <input
                type="text"
                class="input"
                v-model="nodeLabel"
                @input="updateNodeLabel"
                placeholder="新节点"
              />
            </div>
          </div>

          <div class="form-group">
            <label class="label">指标类型</label>
            <div class="select-wrapper">
              <select class="select" v-model="nodeType" @change="updateNodeType">
                <option value="input">输入指标</option>
                <option value="output">输出指标</option>
                <option value="process">处理指标</option>
              </select>
              <div class="select-arrow">▼</div>
            </div>
          </div>

          <div class="form-group">
            <label class="label">指标选择</label>
            <div class="select-wrapper">
              <select class="select" v-model="selectedIndicator" @change="updateSelectedIndicator">
                <option value="">请选择指标</option>
                <option v-for="indicator in indicators" :key="indicator.id" :value="indicator.id">
                  {{ indicator.label }}
                </option>
              </select>
              <div class="select-arrow">▼</div>
            </div>
          </div>

          <!-- 指标下拉菜单 -->
          <div v-if="showIndicatorDropdown" class="indicator-dropdown">
            <div class="indicator-dropdown-header">
              <input
                type="text"
                class="indicator-search"
                v-model="indicatorSearchText"
                placeholder="搜索指标"
              />
            </div>
            <div class="indicator-dropdown-content">
              <div
                v-for="indicator in filteredIndicators"
                :key="indicator.id"
                :class="['indicator-item', { active: selectedIndicator === indicator.id }]"
                @click="selectIndicator(indicator)"
              >
                <div class="indicator-item-check" v-if="selectedIndicator === indicator.id">✓</div>
                <div class="indicator-item-name">{{ indicator.label }}</div>
              </div>
            </div>
          </div>

          <div class="form-group">
            <label class="label">归因分析</label>
            <div class="switch-group">
              <div class="switch">
                <input type="checkbox" id="hasAttribution" v-model="hasAttribution" @change="updateAttributionStatus" />
                <label for="hasAttribution" class="switch-label"></label>
              </div>
              <label for="hasAttribution" class="switch-text">启用归因分析</label>
            </div>
          </div>
        </div>

        <!-- 样式配置选项卡 -->
        <div v-if="activeTab === 'style'" class="tab-content">
          <div class="form-group">
            <label class="label">样式配置</label>
            <div class="color-picker">
              <div
                v-for="color in colorOptions"
                :key="color.value"
                :class="['color-option', { active: nodeColor === color.value }]"
                :style="{ backgroundColor: color.value }"
                @click="updateNodeColor(color.value)"
              >
                <div v-if="nodeColor === color.value" class="color-check">✓</div>
              </div>
            </div>
          </div>

          <div class="form-group">
            <label class="label">边框样式</label>
            <div class="select-wrapper">
              <select class="select" v-model="nodeBorderStyle" @change="updateNodeBorderStyle">
                <option value="solid">实线</option>
                <option value="dashed">虚线</option>
                <option value="dotted">点线</option>
                <option value="none">无边框</option>
              </select>
              <div class="select-arrow">▼</div>
            </div>
          </div>

          <div class="form-group">
            <label class="label">文本颜色</label>
            <div class="select-wrapper">
              <select class="select" v-model="nodeTextColor" @change="updateNodeTextColor">
                <option value="#333333">深色</option>
                <option value="#ffffff">浅色</option>
                <option value="#1890ff">蓝色</option>
                <option value="#52c41a">绿色</option>
                <option value="#fa8c16">橙色</option>
                <option value="#722ed1">紫色</option>
              </select>
              <div class="select-arrow">▼</div>
            </div>
          </div>

          <div class="form-group">
            <label class="label">背景颜色</label>
            <div class="color-picker">
              <div
                v-for="bgColor in backgroundColorOptions"
                :key="bgColor.value"
                :class="['color-option', { active: nodeBackgroundColor === bgColor.value }]"
                :style="{ backgroundColor: bgColor.value }"
                @click="updateNodeBackgroundColor(bgColor.value)"
              >
                <div v-if="nodeBackgroundColor === bgColor.value" class="color-check">✓</div>
              </div>
            </div>
          </div>

          <div class="form-group">
            <label class="label">边框宽度</label>
            <div class="input-wrapper">
              <input
                type="range"
                min="1"
                max="10"
                v-model.number="nodeBorderWidth"
                @change="updateNodeBorderWidth"
                class="slider"
              />
              <div class="slider-value">{{ nodeBorderWidth }}px</div>
            </div>
          </div>

          <div class="form-group">
            <label class="label">圆角大小</label>
            <div class="input-wrapper">
              <input
                type="range"
                min="0"
                max="20"
                v-model.number="nodeBorderRadius"
                @change="updateNodeBorderRadius"
                class="slider"
              />
              <div class="slider-value">{{ nodeBorderRadius }}px</div>
            </div>
          </div>

          <div class="form-group">
            <label class="label">字体大小</label>
            <div class="input-wrapper">
              <input
                type="range"
                min="12"
                max="32"
                v-model.number="nodeFontSize"
                @change="updateNodeFontSize"
                class="slider"
              />
              <div class="slider-value">{{ nodeFontSize }}px</div>
            </div>
          </div>

          <div class="form-group">
            <label class="label">字体粗细</label>
            <div class="select-wrapper">
              <select class="select" v-model="nodeFontWeight" @change="updateNodeFontWeight">
                <option value="normal">常规</option>
                <option value="500">中等</option>
                <option value="bold">粗体</option>
              </select>
              <div class="select-arrow">▼</div>
            </div>
          </div>

          <div class="form-group">
            <label class="label">阴影效果</label>
            <div class="select-wrapper">
              <select class="select" @change="updateNodeBoxShadow(($event.target as HTMLSelectElement).value)">
                <option value="0 2px 8px rgba(0, 0, 0, 0.08)">轻微</option>
                <option value="0 4px 12px rgba(0, 0, 0, 0.15)">中等</option>
                <option value="0 8px 24px rgba(0, 0, 0, 0.2)">强烈</option>
                <option value="none">无阴影</option>
              </select>
              <div class="select-arrow">▼</div>
            </div>
          </div>
        </div>

        <!-- 数据配置选项卡 -->
        <div v-if="activeTab === 'data'" class="tab-content">
          <!-- 数值编辑 -->
          <div class="form-group">
            <label class="label">数值</label>
            <div class="input-wrapper">
              <input
                type="text"
                class="input"
                v-model="nodeValue"
                @input="updateNodeValue"
                placeholder="输入数值"
              />
            </div>
          </div>

          <!-- 变化率编辑 -->
          <div class="form-group">
            <label class="label">变化率</label>
            <div class="input-wrapper">
              <input
                type="text"
                class="input"
                v-model="nodeChange"
                @input="updateNodeChange"
                placeholder="例如: +2.5%"
              />
            </div>
          </div>

          <!-- 对比周期编辑 -->
          <div class="form-group">
            <label class="label">对比周期</label>
            <div class="select-wrapper">
              <select class="select" v-model="nodePeriod" @change="updateNodePeriod">
                <option value="对比上周">对比上周</option>
                <option value="对比上月">对比上月</option>
                <option value="对比上季度">对比上季度</option>
                <option value="对比去年同期">对比去年同期</option>
                <option value="自定义">自定义</option>
              </select>
              <div class="select-arrow">▼</div>
            </div>
            <div v-if="nodePeriod === '自定义'" class="custom-period-input">
              <input
                type="text"
                class="input"
                v-model="nodeCustomPeriod"
                @input="updateNodeCustomPeriod"
                placeholder="输入自定义对比周期"
              />
            </div>
          </div>

          <!-- 数值格式 -->
          <div class="form-group">
            <label class="label">数值格式</label>
            <div class="select-wrapper">
              <select class="select" v-model="nodeNumberFormat" @change="updateNodeNumberFormat">
                <option value="default">默认格式 (1,234.56)</option>
                <option value="plain">无千分位 (1234.56)</option>
                <option value="integer">整数 (1,235)</option>
                <option value="decimal1">一位小数 (1,234.6)</option>
                <option value="decimal2">两位小数 (1,234.56)</option>
                <option value="decimal3">三位小数 (1,234.567)</option>
                <option value="percent">百分比 (123.45%)</option>
                <option value="currency">货币 (¥1,234.56)</option>
                <option value="custom">自定义格式</option>
              </select>
              <div class="select-arrow">▼</div>
            </div>
          </div>

          <!-- 自定义格式 -->
          <div v-if="nodeNumberFormat === 'custom'" class="form-group">
            <label class="label">自定义格式</label>
            <div class="input-wrapper">
              <input
                type="text"
                class="input"
                v-model="nodeCustomFormat"
                @input="updateNodeCustomFormat"
                placeholder="例如: #,##0.00"
              />
              <div class="format-help">
                <span class="help-icon">?</span>
                <div class="help-tooltip">
                  格式说明:<br>
                  # - 数字占位符<br>
                  0 - 零占位符<br>
                  , - 千分位分隔符<br>
                  . - 小数点<br>
                  % - 百分比<br>
                  ¥ - 货币符号
                </div>
              </div>
            </div>
          </div>

          <!-- 格式预览 -->
          <div class="form-group">
            <label class="label">格式预览</label>
            <div class="format-preview">
              <div class="format-example">
                <span class="format-value">{{ formatExample }}</span>
              </div>
            </div>
          </div>

          <!-- 数据来源 -->
          <div class="form-group">
            <label class="label">数据来源</label>
            <div class="select-wrapper">
              <select class="select" v-model="dataSource" @change="updateDataSource">
                <option value="manual">手动输入</option>
                <option value="api">API数据</option>
                <option value="formula">计算公式</option>
              </select>
              <div class="select-arrow">▼</div>
            </div>
          </div>

          <!-- 计算公式 -->
          <div v-if="dataSource === 'formula'" class="form-group">
            <label class="label">计算公式</label>
            <div class="input-wrapper">
              <textarea
                class="textarea"
                v-model="formula"
                @input="updateFormula"
                placeholder="输入计算公式，例如: [节点1] + [节点2] * 0.5"
              ></textarea>
            </div>
          </div>
        </div>

        <!-- 高级配置选项卡 -->
        <div v-if="activeTab === 'advanced'" class="tab-content">
          <div class="form-group">
            <label class="label">高级设置</label>
            <div class="select-wrapper">
              <select class="select">
                <option value="option1">选项1</option>
                <option value="option2">选项2</option>
                <option value="option3">选项3</option>
              </select>
              <div class="select-arrow">▼</div>
            </div>
          </div>
        </div>
      </div>

      <div class="form-actions">
        <button @click="removeNode" class="btn-danger">
          <i class="delete-icon"></i>
          删除节点
        </button>
      </div>
    </div>

    <div v-else-if="selectedEdge" class="edge-config">
      <!-- 选项卡导航 -->
      <div class="tabs-container">
        <div class="tabs">
          <div
            v-for="tab in edgeTabs"
            :key="tab.id"
            :class="['tab', { active: activeEdgeTab === tab.id }]"
            @click="activeEdgeTab = tab.id"
          >
            {{ tab.name }}
          </div>
        </div>
      </div>

      <div class="tab-content-container">
        <!-- 基础属性选项卡 -->
        <div v-if="activeEdgeTab === 'basic'" class="tab-content">
          <div class="form-group">
            <label class="label">关系类型</label>
            <div class="select-wrapper">
              <select class="select" v-model="edgeType" @change="updateEdgeType">
                <option value="default">默认关系</option>
                <option value="calculation">计算关系</option>
                <option value="reference">引用关系</option>
              </select>
              <div class="select-arrow">▼</div>
            </div>
          </div>

          <div class="form-group">
            <label class="label">标签</label>
            <div class="input-wrapper">
              <input
                type="text"
                class="input"
                v-model="edgeLabel"
                @input="updateEdgeLabel"
                placeholder="请输入标签"
              />
            </div>
          </div>
        </div>

        <!-- 样式配置选项卡 -->
        <div v-if="activeEdgeTab === 'style'" class="tab-content">
          <div class="form-group">
            <label class="label">线条颜色</label>
            <div class="color-picker">
              <div
                v-for="color in colorOptions"
                :key="color.value"
                :class="['color-option', { active: edgeColor === color.value }]"
                :style="{ backgroundColor: color.value }"
                @click="updateEdgeColor(color.value)"
              >
                <div v-if="edgeColor === color.value" class="color-check">✓</div>
              </div>
            </div>
          </div>

          <div class="form-group">
            <label class="label">线条样式</label>
            <div class="select-wrapper">
              <select class="select" v-model="edgeLineStyle">
                <option value="solid">实线</option>
                <option value="dashed">虚线</option>
                <option value="dotted">点线</option>
              </select>
              <div class="select-arrow">▼</div>
            </div>
          </div>
        </div>
      </div>

      <div class="form-actions">
        <button @click="removeEdge" class="btn-danger">
          <i class="delete-icon"></i>
          删除连线
        </button>
      </div>
    </div>

    <!-- 移除未选中状态的提示信息 -->
  </div>
</template>

<script lang="ts">
import { defineComponent, computed, ref, watch } from 'vue'
import { useNodeStore } from '../../stores/node'
import { useEdgeStore } from '../../stores/edge'

export default defineComponent({
  name: 'ConfigPanel',
  emits: ['node-removed'],
  setup(_, { emit }) {
    const nodeStore = useNodeStore()
    const edgeStore = useEdgeStore()

    // 获取选中的节点和连线
    const selectedNode = computed(() => nodeStore.selectedNode)
    const selectedEdge = computed(() => edgeStore.selectedEdge)



    // 选项卡状态
    const activeTab = ref('basic')
    const activeEdgeTab = ref('basic')

    // 选项卡定义
    const tabs = [
      { id: 'basic', name: '基础属性' },
      { id: 'style', name: '样式配置' },
      { id: 'data', name: '数据配置' },
      { id: 'advanced', name: '高级配置' }
    ]

    // 指标选择相关
    const selectedIndicator = ref('')
    const showIndicatorDropdown = ref(false)
    const indicatorSearchText = ref('')

    // 指标列表
    const indicators = [
      { id: 'sales', label: '零售价销售额', value: '49,194.473916', change: '+2.71%', period: '对比上周' },
      { id: 'online_sales', label: '线上销售额', value: '28,563.25', change: '+3.42%', period: '对比上周' },
      { id: 'offline_sales', label: '线下销售额', value: '15,642.75', change: '+1.2%', period: '对比上周' },
      { id: 'profit', label: '毛利', value: '12,345.67', change: '+1.5%', period: '对比上周' },
      { id: 'profit_rate', label: '毛利率', value: '25.1%', change: '-0.3%', period: '对比上周' },
      { id: 'net_profit', label: '净销售额', value: '42,123.45', change: '+2.1%', period: '对比上周' },
      { id: 'orders', label: '订单数', value: '1,234', change: '+5.2%', period: '对比上周' },
      { id: 'customers', label: '客户数', value: '856', change: '+3.1%', period: '对比上周' },
      { id: 'avg_order', label: '平均订单金额', value: '398.25', change: '-1.2%', period: '对比上周' },
      { id: 'conversion', label: '转化率', value: '3.5%', change: '+0.2%', period: '对比上周' },
      { id: 'retention', label: '留存率', value: '68.2%', change: '+1.5%', period: '对比上周' }
    ]

    // 过滤指标列表
    const filteredIndicators = computed(() => {
      if (!indicatorSearchText.value) return indicators
      return indicators.filter(indicator =>
        indicator.label.toLowerCase().includes(indicatorSearchText.value.toLowerCase())
      )
    })

    const edgeTabs = [
      { id: 'basic', name: '基础属性' },
      { id: 'style', name: '样式配置' }
    ]

    // 计算周期选项
    const calculationPeriods = [
      { value: 'day', label: '当日' },
      { value: 'week', label: '本周' },
      { value: 'month', label: '本月' },
      { value: 'quarter', label: '本季度' },
      { value: 'year', label: '本年度' }
    ]

    // 节点配置表单数据
    const nodeLabel = ref('')
    const nodeType = ref('')
    const nodePositionX = ref(0)
    const nodePositionY = ref(0)
    const calculationPeriod = ref('day')
    const compareIndicator = ref(false)
    const nodeColor = ref('#1890ff')
    const nodeBorderStyle = ref('solid')
    const nodeBorderWidth = ref(4)
    const nodeBorderRadius = ref(4)
    const nodeTextColor = ref('#333333')
    const nodeBackgroundColor = ref('white')
    const nodeFontSize = ref(20)
    const nodeFontWeight = ref('500')
    const nodeBoxShadow = ref('0 2px 8px rgba(0, 0, 0, 0.08)')

    // 数据配置相关
    const nodeValue = ref('')
    const nodeChange = ref('')
    const nodePeriod = ref('对比上周')
    const nodeCustomPeriod = ref('')
    const nodeNumberFormat = ref('default')
    const nodeCustomFormat = ref('#,##0.00')
    const dataSource = ref('manual')
    const formula = ref('')

    // 格式化示例
    const formatExample = computed(() => {
      const sampleValue = 123456.789;

      // 根据选择的格式返回示例
      switch (nodeNumberFormat.value) {
        case 'plain':
          return '123456.79';
        case 'integer':
          return '123,457';
        case 'decimal1':
          return '123,456.8';
        case 'decimal2':
          return '123,456.79';
        case 'decimal3':
          return '123,456.789';
        case 'percent':
          return '12,345.68%';
        case 'currency':
          return '¥123,456.79';
        case 'custom':
          // 简单模拟自定义格式
          try {
            let result = nodeCustomFormat.value;
            // 替换占位符
            result = result.replace(/#+/g, Math.floor(sampleValue).toString());
            // 处理小数部分
            if (result.includes('.')) {
              const decimalPart = (sampleValue % 1).toFixed(2).substring(2);
              result = result.replace(/\.0+/g, '.' + decimalPart);
            }
            return result;
          } catch (e) {
            return '格式错误';
          }
        default:
          return '123,456.79';
      }
    })

    // 归因分析相关数据
    const hasAttribution = ref(false)
    const attributionItems = ref<Array<{
      id: string;
      name: string;
      value: number;
      percentage: number;
      contribution: number;
    }>>([])

    // 生成唯一ID
    const generateId = () => {
      return Date.now().toString(36) + Math.random().toString(36).substr(2, 5)
    }

    // 连线配置表单数据
    const edgeLabel = ref('')
    const edgeType = ref('')
    const edgeColor = ref('#c1c7d0')
    const edgeLineStyle = ref('solid')

    // 颜色选项
    const colorOptions = [
      { name: '蓝色', value: '#1890ff' },
      { name: '绿色', value: '#52c41a' },
      { name: '橙色', value: '#fa8c16' },
      { name: '紫色', value: '#722ed1' },
      { name: '红色', value: '#f5222d' },
      { name: '灰色', value: '#8c8c8c' }
    ]

    // 背景颜色选项
    const backgroundColorOptions = [
      { name: '白色', value: 'white' },
      { name: '浅蓝', value: '#e6f7ff' },
      { name: '浅绿', value: '#f6ffed' },
      { name: '浅橙', value: '#fff7e6' },
      { name: '浅紫', value: '#f9f0ff' },
      { name: '浅红', value: '#fff1f0' },
      { name: '浅灰', value: '#f5f5f5' }
    ]

    // 监听选中节点变化，更新表单数据
    watch(selectedNode, (node) => {
      if (node) {
        // 基础属性
        nodeLabel.value = node.data.label || ''
        nodeType.value = node.type || 'kpi'
        nodePositionX.value = node.position.x
        nodePositionY.value = node.position.y
        calculationPeriod.value = node.data.calculationPeriod || 'day'
        compareIndicator.value = node.data.compareIndicator || false

        // 样式属性
        nodeColor.value = node.data.color || '#1890ff'
        nodeBorderStyle.value = node.data.borderStyle || 'solid'
        nodeBorderWidth.value = node.data.borderWidth || 4
        nodeBorderRadius.value = node.data.borderRadius || 4
        nodeTextColor.value = node.data.textColor || '#333333'
        nodeBackgroundColor.value = node.data.backgroundColor || 'white'
        nodeFontSize.value = node.data.fontSize || 20
        nodeFontWeight.value = node.data.fontWeight || '500'
        nodeBoxShadow.value = node.data.boxShadow || '0 2px 8px rgba(0, 0, 0, 0.08)'

        // 数据属性
        nodeValue.value = node.data.value || '0'
        nodeChange.value = node.data.change || '0%'
        nodePeriod.value = node.data.period || '对比上周'
        nodeCustomPeriod.value = node.data.customPeriod || ''
        nodeNumberFormat.value = node.data.numberFormat || 'default'
        nodeCustomFormat.value = node.data.customFormat || '#,##0.00'
        dataSource.value = node.data.dataSource || 'manual'
        formula.value = node.data.formula || ''

        // 其他属性
        selectedIndicator.value = node.data.indicatorId || ''

        // 更新归因分析数据
        hasAttribution.value = node.data.hasAttribution || false
        attributionItems.value = node.data.attributionData ? [...node.data.attributionData] : []
      }
    }, { immediate: true })

    // 指标选择相关方法
    const updateSelectedIndicator = () => {
      if (!selectedNode.value) return

      const indicator = indicators.find(ind => ind.id === selectedIndicator.value)
      if (!indicator) return

      // 更新节点数据
      nodeStore.updateNode(selectedNode.value.id, {
        data: {
          ...selectedNode.value.data,
          indicatorId: indicator.id,
          label: indicator.label,
          value: indicator.value,
          change: indicator.change,
          period: indicator.period
        }
      })

      // 更新节点标签
      nodeLabel.value = indicator.label
    }

    const selectIndicator = (indicator: any) => {
      selectedIndicator.value = indicator.id
      updateSelectedIndicator()
      showIndicatorDropdown.value = false
    }

    const toggleIndicatorDropdown = () => {
      showIndicatorDropdown.value = !showIndicatorDropdown.value
    }

    // 监听选中连线变化，更新表单数据
    watch(selectedEdge, (edge) => {
      if (edge) {
        edgeLabel.value = edge.label || ''
        edgeType.value = edge.type || 'default'
        edgeColor.value = edge.color || '#c1c7d0'
        edgeLineStyle.value = edge.lineStyle || 'solid'
      }
    }, { immediate: true })

    // 更新节点属性
    const updateNodeLabel = () => {
      if (!selectedNode.value) return

      nodeStore.updateNode(selectedNode.value.id, {
        data: { ...selectedNode.value.data, label: nodeLabel.value }
      })
    }

    const updateNodeType = () => {
      if (!selectedNode.value) return

      // 根据类型设置不同的颜色
      const colorMap: Record<string, string> = {
        kpi: '#1890ff',
        input: '#52c41a',
        output: '#fa8c16',
        process: '#722ed1'
      }

      nodeStore.updateNode(selectedNode.value.id, {
        type: nodeType.value,
        data: {
          ...selectedNode.value.data,
          color: colorMap[nodeType.value] || '#1890ff'
        }
      })

      // 更新颜色选择器
      nodeColor.value = colorMap[nodeType.value] || '#1890ff'
    }

    const updateNodePosition = () => {
      if (!selectedNode.value) return

      nodeStore.moveNode(selectedNode.value.id, {
        x: nodePositionX.value,
        y: nodePositionY.value
      })
    }

    const updateNodeColor = (color: string) => {
      if (!selectedNode.value) return

      nodeStore.updateNode(selectedNode.value.id, {
        data: { ...selectedNode.value.data, color }
      })

      nodeColor.value = color
    }

    const updateNodeBackgroundColor = (backgroundColor: string) => {
      if (!selectedNode.value) return

      nodeStore.updateNode(selectedNode.value.id, {
        data: { ...selectedNode.value.data, backgroundColor }
      })

      nodeBackgroundColor.value = backgroundColor
    }

    const updateNodeBorderStyle = () => {
      if (!selectedNode.value) return

      nodeStore.updateNode(selectedNode.value.id, {
        data: { ...selectedNode.value.data, borderStyle: nodeBorderStyle.value }
      })
    }

    const updateNodeTextColor = () => {
      if (!selectedNode.value) return

      nodeStore.updateNode(selectedNode.value.id, {
        data: { ...selectedNode.value.data, textColor: nodeTextColor.value }
      })
    }

    const updateNodeBorderWidth = () => {
      if (!selectedNode.value) return

      nodeStore.updateNode(selectedNode.value.id, {
        data: { ...selectedNode.value.data, borderWidth: nodeBorderWidth.value }
      })
    }

    const updateNodeBorderRadius = () => {
      if (!selectedNode.value) return

      nodeStore.updateNode(selectedNode.value.id, {
        data: { ...selectedNode.value.data, borderRadius: nodeBorderRadius.value }
      })
    }

    const updateNodeFontSize = () => {
      if (!selectedNode.value) return

      nodeStore.updateNode(selectedNode.value.id, {
        data: { ...selectedNode.value.data, fontSize: nodeFontSize.value }
      })
    }

    const updateNodeFontWeight = () => {
      if (!selectedNode.value) return

      nodeStore.updateNode(selectedNode.value.id, {
        data: { ...selectedNode.value.data, fontWeight: nodeFontWeight.value }
      })
    }

    const updateNodeBoxShadow = (shadow: string) => {
      if (!selectedNode.value) return

      nodeStore.updateNode(selectedNode.value.id, {
        data: { ...selectedNode.value.data, boxShadow: shadow }
      })

      nodeBoxShadow.value = shadow
    }

    const removeNode = () => {
      if (!selectedNode.value) return

      const nodeId = selectedNode.value.id

      // 发出节点删除事件，让Editor.vue处理递归删除
      emit('node-removed', nodeId)

      // 删除节点
      nodeStore.removeNode(nodeId)

      // 删除与该节点相关的所有连线
      edgeStore.removeEdgesConnectedToNode(nodeId)
    }

    // 更新连线属性
    const updateEdgeLabel = () => {
      if (!selectedEdge.value) return

      edgeStore.updateEdge(selectedEdge.value.id, {
        label: edgeLabel.value
      })
    }

    const updateEdgeType = () => {
      if (!selectedEdge.value) return

      edgeStore.updateEdge(selectedEdge.value.id, {
        type: edgeType.value
      })
    }

    const updateEdgeColor = (color: string) => {
      if (!selectedEdge.value) return

      edgeStore.updateEdge(selectedEdge.value.id, {
        color
      })

      edgeColor.value = color
    }

    const removeEdge = () => {
      if (!selectedEdge.value) return

      edgeStore.removeEdge(selectedEdge.value.id)
    }

    // 归因分析相关方法
    // 数据配置相关方法
    const updateNodeValue = () => {
      if (!selectedNode.value) return

      nodeStore.updateNode(selectedNode.value.id, {
        data: { ...selectedNode.value.data, value: nodeValue.value }
      })
    }

    const updateNodeChange = () => {
      if (!selectedNode.value) return

      nodeStore.updateNode(selectedNode.value.id, {
        data: { ...selectedNode.value.data, change: nodeChange.value }
      })
    }

    const updateNodePeriod = () => {
      if (!selectedNode.value) return

      // 如果选择了自定义，使用自定义周期值
      const periodValue = nodePeriod.value === '自定义' ? nodeCustomPeriod.value : nodePeriod.value

      nodeStore.updateNode(selectedNode.value.id, {
        data: {
          ...selectedNode.value.data,
          period: periodValue,
          customPeriod: nodeCustomPeriod.value
        }
      })
    }

    const updateNodeCustomPeriod = () => {
      if (!selectedNode.value) return

      nodeStore.updateNode(selectedNode.value.id, {
        data: {
          ...selectedNode.value.data,
          period: nodeCustomPeriod.value,
          customPeriod: nodeCustomPeriod.value
        }
      })
    }

    const updateNodeNumberFormat = () => {
      if (!selectedNode.value) return

      nodeStore.updateNode(selectedNode.value.id, {
        data: {
          ...selectedNode.value.data,
          numberFormat: nodeNumberFormat.value
        }
      })

      // 如果选择了自定义格式，应用自定义格式
      if (nodeNumberFormat.value === 'custom') {
        updateNodeCustomFormat()
      }
    }

    const updateNodeCustomFormat = () => {
      if (!selectedNode.value) return

      nodeStore.updateNode(selectedNode.value.id, {
        data: {
          ...selectedNode.value.data,
          customFormat: nodeCustomFormat.value
        }
      })
    }

    const updateDataSource = () => {
      if (!selectedNode.value) return

      nodeStore.updateNode(selectedNode.value.id, {
        data: { ...selectedNode.value.data, dataSource: dataSource.value }
      })
    }

    const updateFormula = () => {
      if (!selectedNode.value) return

      nodeStore.updateNode(selectedNode.value.id, {
        data: { ...selectedNode.value.data, formula: formula.value }
      })
    }

    // 归因分析相关方法
    const updateAttributionStatus = () => {
      if (!selectedNode.value) return

      nodeStore.updateNode(selectedNode.value.id, {
        data: {
          ...selectedNode.value.data,
          hasAttribution: hasAttribution.value,
          // 如果禁用归因分析，清空归因数据
          attributionData: hasAttribution.value ? selectedNode.value.data.attributionData : []
        }
      })
    }

    const addAttributionItem = () => {
      if (!selectedNode.value) return

      // 创建新的归因项
      const newItem = {
        id: generateId(),
        name: `归因项 ${attributionItems.value.length + 1}`,
        value: 0,
        percentage: 0,
        contribution: 0
      }

      // 添加到列表
      attributionItems.value.push(newItem)

      // 更新节点数据
      updateAttributionItems()
    }

    const removeAttributionItem = (id: string) => {
      if (!selectedNode.value) return

      // 从列表中移除
      attributionItems.value = attributionItems.value.filter(item => item.id !== id)

      // 更新节点数据
      updateAttributionItems()
    }

    const updateAttributionItems = () => {
      if (!selectedNode.value) return

      nodeStore.updateNode(selectedNode.value.id, {
        data: {
          ...selectedNode.value.data,
          attributionData: [...attributionItems.value]
        }
      })
    }

    return {
      selectedNode,
      selectedEdge,
      activeTab,
      activeEdgeTab,
      tabs,
      edgeTabs,
      calculationPeriods,
      nodeLabel,
      nodeType,
      nodePositionX,
      nodePositionY,
      calculationPeriod,
      formatExample,
      compareIndicator,
      nodeColor,
      nodeBorderStyle,
      nodeBorderWidth,
      nodeBorderRadius,
      nodeTextColor,
      nodeBackgroundColor,
      nodeFontSize,
      nodeFontWeight,
      nodeBoxShadow,
      // 数据配置相关
      nodeValue,
      nodeChange,
      nodePeriod,
      nodeCustomPeriod,
      nodeNumberFormat,
      nodeCustomFormat,
      dataSource,
      formula,
      updateNodeValue,
      updateNodeChange,
      updateNodePeriod,
      updateNodeCustomPeriod,
      updateNodeNumberFormat,
      updateNodeCustomFormat,
      updateDataSource,
      updateFormula,
      edgeLabel,
      edgeType,
      edgeColor,
      edgeLineStyle,
      colorOptions,
      backgroundColorOptions,
      // 指标选择相关
      selectedIndicator,
      showIndicatorDropdown,
      indicatorSearchText,
      indicators,
      filteredIndicators,
      updateSelectedIndicator,
      selectIndicator,
      toggleIndicatorDropdown,
      // 归因分析相关数据
      hasAttribution,
      attributionItems,
      updateAttributionStatus,
      addAttributionItem,
      removeAttributionItem,
      updateAttributionItems,
      // 其他方法
      updateNodeLabel,
      updateNodeType,
      updateNodePosition,
      updateNodeColor,
      updateNodeBackgroundColor,
      updateNodeBorderStyle,
      updateNodeBorderWidth,
      updateNodeBorderRadius,
      updateNodeTextColor,
      updateNodeFontSize,
      updateNodeFontWeight,
      updateNodeBoxShadow,
      removeNode,
      updateEdgeLabel,
      updateEdgeType,
      updateEdgeColor,
      removeEdge
    }
  }
})
</script>

<style lang="scss" scoped>
.config-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  position: relative;
  overflow: hidden; /* 防止整个面板出现滚动条 */

  .panel-header {
    padding: 16px;
    font-size: 16px;
    font-weight: 500;
    color: #333;
    border-bottom: 1px solid #f0f0f0;
  }

  .tabs-container {
    background-color: #f5f7fa;
    border-bottom: 1px solid #f0f0f0;
    margin-bottom: 0;
  }

  .tabs {
    display: flex;
    padding: 0;
    margin: 0;

    .tab {
      flex: 1;
      padding: 12px 0;
      text-align: center;
      cursor: pointer;
      font-size: 14px;
      color: #666;
      position: relative;
      transition: all 0.2s;

      &.active {
        color: #1890ff;
        font-weight: 500;
        background-color: #fff;

        &::after {
          content: '';
          position: absolute;
          bottom: 0;
          left: 0;
          right: 0;
          height: 2px;
          background-color: #1890ff;
        }
      }

      &:hover:not(.active) {
        color: #40a9ff;
      }
    }
  }

  .tab-content-container {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    padding: 0;
    display: flex;
    flex-direction: column;
    height: 0; /* 关键设置：让容器能够正确地伸缩并支持滚动 */
  }

  .tab-content {
    padding: 16px;
    padding-bottom: 80px; /* 增加更多底部间距，确保最后的元素不被遮挡 */
    background-color: #fff;
  }

  .form-group {
    margin-bottom: 20px;
    position: relative;

    &:last-child {
      margin-bottom: 0;
    }

    .label {
      display: block;
      margin-bottom: 8px;
      font-size: 14px;
      color: #333;
    }

    .input-wrapper, .select-wrapper, .textarea-wrapper {
      position: relative;
    }

    .input, .select, .textarea {
      width: 100%;
      padding: 8px 12px;
      border: 1px solid #e8e8e8;
      border-radius: 4px;
      font-size: 14px;
      transition: all 0.2s;
      background-color: #fff;

      &:hover {
        border-color: #40a9ff;
      }

      &:focus {
        outline: none;
        border-color: #1890ff;
      }
    }

    .indicator-dropdown {
      position: absolute;
      top: 100%;
      left: 0;
      right: 0;
      z-index: 10;
      background-color: #fff;
      border: 1px solid #e8e8e8;
      border-radius: 4px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      margin-top: 4px;
      max-height: 300px;
      display: flex;
      flex-direction: column;

      .indicator-dropdown-header {
        padding: 8px;
        border-bottom: 1px solid #f0f0f0;

        .indicator-search {
          width: 100%;
          padding: 6px 10px;
          border: 1px solid #e8e8e8;
          border-radius: 4px;
          font-size: 13px;

          &:focus {
            outline: none;
            border-color: #1890ff;
          }
        }
      }

      .indicator-dropdown-content {
        overflow-y: auto;
        max-height: 250px;

        .indicator-item {
          display: flex;
          align-items: center;
          padding: 8px 12px;
          cursor: pointer;
          transition: all 0.2s;

          &:hover {
            background-color: #f5f7fa;
          }

          &.active {
            background-color: #e6f7ff;
            color: #1890ff;
          }

          .indicator-item-check {
            margin-right: 8px;
            font-weight: bold;
          }

          .indicator-item-name {
            flex: 1;
          }
        }
      }
    }

    .select {
      appearance: none;
      padding-right: 30px;
    }

    .select-arrow {
      position: absolute;
      right: 12px;
      top: 50%;
      transform: translateY(-50%);
      color: #999;
      font-size: 10px;
      pointer-events: none;
    }

    .textarea {
      min-height: 80px;
      resize: vertical;
    }
  }

  .form-group-row {
    display: flex;
    gap: 12px;

    .form-group-col {
      flex: 1;
    }
  }

  // 开关样式
  .switch-group {
    display: flex;
    align-items: center;

    .switch {
      position: relative;
      display: inline-block;
      width: 40px;
      height: 20px;
      margin-right: 8px;

      input {
        opacity: 0;
        width: 0;
        height: 0;

        &:checked + .switch-label {
          background-color: #1890ff;

          &:before {
            transform: translateX(20px);
          }
        }
      }

      .switch-label {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #ddd;
        transition: .3s;
        border-radius: 20px;

        &:before {
          position: absolute;
          content: "";
          height: 16px;
          width: 16px;
          left: 2px;
          bottom: 2px;
          background-color: white;
          transition: .3s;
          border-radius: 50%;
        }
      }
    }

    .switch-text {
      font-size: 14px;
      color: #333;
      cursor: pointer;
    }
  }

  .color-picker {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 4px;

    .color-option {
      width: 24px;
      height: 24px;
      border-radius: 4px;
      cursor: pointer;
      border: 2px solid transparent;
      transition: all 0.2s;
      display: flex;
      align-items: center;
      justify-content: center;

      .color-check {
        color: white;
        font-weight: bold;
        font-size: 12px;
      }

      &.active {
        border-color: #1890ff;
      }

      &:hover:not(.active) {
        opacity: 0.8;
      }
    }
  }

  // 滑块样式
  .slider {
    -webkit-appearance: none;
    appearance: none;
    width: 100%;
    height: 6px;
    border-radius: 3px;
    background: #e8e8e8;
    outline: none;
    margin: 10px 0;

    &::-webkit-slider-thumb {
      -webkit-appearance: none;
      appearance: none;
      width: 16px;
      height: 16px;
      border-radius: 50%;
      background: #1890ff;
      cursor: pointer;
      transition: all 0.2s;

      &:hover {
        transform: scale(1.1);
        background: #40a9ff;
      }
    }

    &::-moz-range-thumb {
      width: 16px;
      height: 16px;
      border-radius: 50%;
      background: #1890ff;
      cursor: pointer;
      transition: all 0.2s;
      border: none;

      &:hover {
        transform: scale(1.1);
        background: #40a9ff;
      }
    }
  }

  .slider-value {
    text-align: center;
    font-size: 12px;
    color: #666;
    margin-top: 4px;
  }

  .format-preview {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background-color: #fafafa;
    border-radius: 4px;
    border: 1px solid #f0f0f0;

    .format-example {
      .format-value {
        color: #333;
        font-weight: 500;
      }
    }
  }

  .custom-period-input {
    margin-top: 8px;
  }

  .format-help {
    position: relative;
    display: inline-block;
    margin-left: 8px;

    .help-icon {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      width: 16px;
      height: 16px;
      border-radius: 50%;
      background-color: #1890ff;
      color: white;
      font-size: 12px;
      cursor: pointer;
    }

    .help-tooltip {
      position: absolute;
      bottom: 24px;
      right: -8px;
      width: 200px;
      background-color: #fff;
      border-radius: 4px;
      padding: 12px;
      box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08);
      font-size: 12px;
      line-height: 1.5;
      color: #666;
      z-index: 10;
      display: none;

      &:before {
        content: '';
        position: absolute;
        bottom: -6px;
        right: 12px;
        width: 12px;
        height: 12px;
        background-color: #fff;
        transform: rotate(45deg);
        box-shadow: 3px 3px 6px -3px rgba(0, 0, 0, 0.12);
      }
    }

    &:hover .help-tooltip {
      display: block;
    }
  }

  .node-config,
  .edge-config {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
  }

  .form-actions {
    padding: 16px;
    border-top: 1px solid #f0f0f0;
    margin-top: auto;
    background-color: #fff;
    z-index: 1; /* 确保操作按钮始终在顶层 */
  }

  /* 移除 no-selection 相关样式 */
}

.btn-link {
  background: none;
  border: none;
  color: #1890ff;
  cursor: pointer;
  padding: 0;
  font-size: 13px;

  &:hover {
    color: #40a9ff;
  }
}

.btn-danger {
  width: 100%;
  padding: 8px 0;
  background-color: #f56c6c;
  border: none;
  border-radius: 4px;
  color: white;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;

  .delete-icon {
    display: inline-block;
    width: 16px;
    height: 16px;
    margin-right: 6px;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z'/%3E%3C/svg%3E");
    background-size: contain;
    background-repeat: no-repeat;
  }

  &:hover {
    background-color: #f78989;
  }

  &:active {
    background-color: #e64242;
  }
}
</style>