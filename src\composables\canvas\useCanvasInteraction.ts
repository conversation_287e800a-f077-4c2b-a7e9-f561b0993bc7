import { ref, computed, onMounted, type Ref } from 'vue'
import { useCanvasStore } from '../../stores/canvas'
import type { Position, Size } from '../../types'

export function useCanvasInteraction(canvasRef: Ref<HTMLElement | null>) {
  const canvasStore = useCanvasStore()

  // Canvas state
  const canvasScale = computed(() => canvasStore.scale)
  const canvasPosition = computed(() => canvasStore.position)
  const canvasDimensions = computed(() => canvasStore.dimensions)
  
  // Container size
  const containerSize = ref<Size>({ width: 0, height: 0 })
  
  // Canvas drag state
  const isDraggingCanvas = ref(false)
  const lastMousePosition = ref<Position>({ x: 0, y: 0 })
  
  // Canvas animation optimization
  let canvasAnimationFrameId: number | null = null
  let lastCanvasMouseEvent: MouseEvent | null = null
  let lastCanvasUpdateTime = 0
  const CANVAS_THROTTLE_THRESHOLD = 1000 / 120 // 120fps

  // Initialize container size
  onMounted(() => {
    if (canvasRef.value) {
      updateContainerSize()
      window.addEventListener('resize', updateContainerSize)
    }
  })

  // Update container size
  const updateContainerSize = () => {
    if (canvasRef.value) {
      containerSize.value = {
        width: canvasRef.value.clientWidth,
        height: canvasRef.value.clientHeight
      }
    }
  }

  // Start canvas drag
  const startCanvasDrag = (event: MouseEvent) => {
    isDraggingCanvas.value = true
    lastMousePosition.value = { x: event.clientX, y: event.clientY }
    
    // Reset optimization variables
    lastCanvasMouseEvent = null
    lastCanvasUpdateTime = 0
    
    // Add grab cursor
    document.body.style.cursor = 'grabbing'
  }

  // Update canvas position with throttling
  const updateCanvasPosition = () => {
    canvasAnimationFrameId = null
    
    if (!lastCanvasMouseEvent || !isDraggingCanvas.value) return
    
    // Calculate mouse movement
    const deltaX = lastCanvasMouseEvent.clientX - lastMousePosition.value.x
    const deltaY = lastCanvasMouseEvent.clientY - lastMousePosition.value.y
    
    // Pan the canvas
    canvasStore.pan(deltaX, deltaY)
    
    // Update last mouse position
    lastMousePosition.value = {
      x: lastCanvasMouseEvent.clientX,
      y: lastCanvasMouseEvent.clientY
    }
    
    // Continue animation frame if dragging
    if (lastCanvasMouseEvent && isDraggingCanvas.value) {
      canvasAnimationFrameId = requestAnimationFrame(updateCanvasPosition)
    }
  }

  // Handle canvas drag movement
  const dragCanvas = (event: MouseEvent) => {
    if (!isDraggingCanvas.value) return
    
    // Store the latest mouse event
    lastCanvasMouseEvent = event
    
    // Use requestAnimationFrame for smooth panning
    if (canvasAnimationFrameId === null) {
      canvasAnimationFrameId = requestAnimationFrame(updateCanvasPosition)
    }
    
    // Apply immediate visual feedback between frames
    const currentTime = performance.now()
    if (currentTime - lastCanvasUpdateTime > CANVAS_THROTTLE_THRESHOLD) {
      const deltaX = event.clientX - lastMousePosition.value.x
      const deltaY = event.clientY - lastMousePosition.value.y
      
      canvasStore.pan(deltaX, deltaY)
      
      lastMousePosition.value = { x: event.clientX, y: event.clientY }
      lastCanvasUpdateTime = currentTime
    }
  }

  // End canvas drag
  const endCanvasDrag = () => {
    if (isDraggingCanvas.value) {
      isDraggingCanvas.value = false
      
      // Cancel any pending animation frames
      if (canvasAnimationFrameId !== null) {
        cancelAnimationFrame(canvasAnimationFrameId)
        canvasAnimationFrameId = null
      }
      
      // Reset variables
      lastCanvasMouseEvent = null
      lastCanvasUpdateTime = 0
      
      // Reset cursor
      document.body.style.cursor = ''
    }
  }

  // Handle zoom
  const handleZoom = (event: WheelEvent) => {
    event.preventDefault()
    
    if (!canvasRef.value) return
    
    // Get mouse position relative to canvas
    const canvasRect = canvasRef.value.getBoundingClientRect()
    const center = {
      x: event.clientX - canvasRect.left,
      y: event.clientY - canvasRect.top
    }
    
    // Zoom the canvas
    canvasStore.zoom(-event.deltaY, center)
  }

  // Add function to center the canvas on a position
  const centerOnPosition = (position: Position) => {
    if (!canvasRef.value) return
    
    // Calculate the center of the container
    const containerWidth = canvasRef.value.clientWidth
    const containerHeight = canvasRef.value.clientHeight
    
    // Calculate new canvas position to center on the given position
    const newX = containerWidth / 2 - position.x * canvasScale.value
    const newY = containerHeight / 2 - position.y * canvasScale.value
    
    // Update canvas position
    canvasStore.setPosition({ x: newX, y: newY })
  }

  return {
    canvasScale,
    canvasPosition,
    canvasDimensions,
    containerSize,
    isDraggingCanvas,
    startCanvasDrag,
    dragCanvas,
    endCanvasDrag,
    handleZoom,
    updateContainerSize,
    centerOnPosition
  }
}
