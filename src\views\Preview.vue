<template>
  <div class="preview-container">
    <div class="toolbar">
      <button @click="backToEditor">返回编辑</button>
    </div>
    <div class="canvas-container">
      <Canvas
        :readonly="true"
        @show-attribution="handleShowAttribution"
      />
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue'
import { useRouter } from 'vue-router'
import { useNodeStore } from '../stores/node'
import Canvas from '../components/Canvas/Canvas.vue'

export default defineComponent({
  name: 'Preview',
  components: {
    Canvas
  },
  setup() {
    const router = useRouter()
    const nodeStore = useNodeStore()

    const backToEditor = () => {
      router.push('/editor')
    }

    // 处理显示归因分析
    const handleShowAttribution = (nodeId: string) => {
      console.log('显示归因分析', nodeId)
      // 获取节点信息
      const node = nodeStore.getNodeById(nodeId)
      if (node && node.data.hasAttribution) {
        // 这里可以添加额外的逻辑，比如记录用户查看归因分析的行为等
        console.log('节点归因数据:', node.data.attributionData)
      }
    }

    return {
      backToEditor,
      handleShowAttribution
    }
  }
})
</script>

<style lang="scss" scoped>
.preview-container {
  display: flex;
  flex-direction: column;
  height: 100vh;

  .toolbar {
    height: 50px;
    padding: 0 20px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #ddd;

    button {
      padding: 5px 10px;
      background-color: #2196F3;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;

      &:hover {
        background-color: #0b7dda;
      }
    }
  }

  .canvas-container {
    flex: 1;
    position: relative;
    overflow: hidden;
  }
}
</style>