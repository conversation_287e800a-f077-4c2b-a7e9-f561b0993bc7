<template>
  <div
    ref="canvasRef"
    class="canvas"
    @mousedown="onMouseDown"
    @mousemove="onMouseMove"
    @mouseup="onMouseUp"
    @mouseleave="onMouseUp"
    @wheel="handleZoom"
    @contextmenu="onCanvasContextMenu"
  >
    <!-- 空画布状态 -->
    <CanvasEmptyState
      :readonly="readonly"
      :nodes="nodes"
      @create-empty-node="createEmptyNode"
      @empty-canvas-right-click="onEmptyCanvasRightClick"
    />

    <!-- 画布内容 -->
    <CanvasContent
      :canvas-position="canvasPosition"
      :canvas-scale="canvasScale"
      :canvas-dimensions="canvasDimensions"
      :nodes="nodes"
      :edges="edges"
      :selected-node-id="selectedNodeId"
      :selected-edge-id="selectedEdgeId"
      :is-connecting="isConnecting"
      :readonly="readonly"
      :dragged-node-id="draggedNodeId"
      :temp-edge-path="tempEdgePath"
      @select-node="selectNode"
      @move-node="moveNode"
      @select-edge="selectEdge"
      @connect-start="startConnecting"
      @connect-end="finishConnecting"
      @add-relation="handleAddRelation"
      @show-attribution="handleShowAttribution"
      @drag-start="handleDragStart"
      @drag-end="handleDragEnd"
      @copy-node="handleCopyNode"
      @delete-node="handleDeleteNode"
      @symbol-click="onSymbolClick"
      @connector-click="onConnectorClick"
      @get-temp-edge-path="getTempEdgePath"
    />

    <!-- 实时缩略图 -->
    <CanvasThumbnail
      v-if="!readonly && showThumbnail"
      :nodes="nodesWithDragState"
      :edges="edges"
      :canvas-position="canvasPosition"
      :canvas-scale="canvasScale"
      :canvas-dimensions="canvasDimensions"
      :container-size="containerSize"
      :selected-node-id="selectedNodeId"
    />

    <!-- 缩略图切换按钮 -->
    <CanvasThumbnailControl
      :readonly="readonly"
      :show-thumbnail="showThumbnail"
      @toggle-thumbnail="toggleThumbnail"
    />

    <!-- 空画布右键菜单 -->
    <EmptyCanvasContextMenu
      :visible="showEmptyCanvasMenu"
      :position="emptyCanvasMenuPosition"
      @add-node="handleAddNodeFromMenu"
      @paste="handlePasteFromMenu"
      @auto-layout="handleAutoLayoutFromMenu"
    />
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed, watch, nextTick } from 'vue'
import CanvasContent from './components/CanvasContent.vue'
import CanvasEmptyState from './components/CanvasEmptyState.vue'
import CanvasThumbnailControl from './components/CanvasThumbnailControl.vue'
import CanvasThumbnail from '../CanvasThumbnail/CanvasThumbnail.vue'
import EmptyCanvasContextMenu from '../ContextMenu/EmptyCanvasContextMenu.vue'

// Composables
import { useCanvasInteraction } from '../../composables/canvas/useCanvasInteraction'
import { useNodeOperations } from '../../composables/canvas/useNodeOperations'
import { useEdgeOperations } from '../../composables/canvas/useEdgeOperations'
import { useNodeTree } from '../../composables/canvas/useNodeTree'
import { useCanvasEvents } from '../../composables/canvas/useCanvasEvents'
import { useLayoutCalculation } from '../../composables/canvas/useLayoutCalculation'

// Stores
import { useCanvasStore } from '../../stores/canvas'

export default defineComponent({
  name: 'Canvas',
  components: {
    CanvasContent,
    CanvasEmptyState,
    CanvasThumbnailControl,
    CanvasThumbnail,
    EmptyCanvasContextMenu
  },
  props: {
    readonly: {
      type: Boolean,
      default: false
    }
  },
  emits: ['add-relation', 'create-node', 'connector-click', 'node-removed', 'show-attribution', 'auto-layout', 'close-relation-selector', 'create-empty-node'],
  setup(props, { emit }) {
    // Canvas ref
    const canvasRef = ref<HTMLElement | null>(null)
    
    // Thumbnail state
    const showThumbnail = ref(true)
    
    // Canvas store
    const canvasStore = useCanvasStore()
    
    // Canvas interaction
    const {
      canvasScale,
      canvasPosition,
      canvasDimensions,
      containerSize,
      isDraggingCanvas,
      startCanvasDrag,
      dragCanvas,
      endCanvasDrag,
      handleZoom,
      centerOnPosition
    } = useCanvasInteraction(canvasRef)
    
    // Node operations
    const {
      nodes,
      selectedNodeId,
      draggedNodeId,
      nodesWithDragState,
      autoLayoutEnabled,
      getNodeById,
      selectNode,
      moveNode,
      createEmptyNode: createNode,
      handleDragStart,
      handleDragEnd,
      handleCopyNode,
      handleDeleteNode,
      handleAddRelation,
      handleShowAttribution
    } = useNodeOperations(props.readonly, emit)
    
    // Edge operations
    const {
      edges,
      selectedEdgeId,
      isConnecting,
      connectingSourceId,
      connectingPosition,
      selectEdge,
      startConnecting,
      finishConnecting,
      updateConnectingPosition,
      getTempEdgePath,
      onSymbolClick,
      onConnectorClick
    } = useEdgeOperations(canvasRef, canvasPosition, canvasScale, props.readonly, emit)
    
    // Node tree
    const {
      buildNodeTree,
      optimizeNodeSpacing
    } = useNodeTree(nodes, edges, getNodeById)
    
    // Layout calculation
    const {
      applyAutoLayout,
      forceAdjustParentUncleSpacing
    } = useLayoutCalculation(buildNodeTree, getNodeById)
    
    // Canvas events
    const {
      showEmptyCanvasMenu,
      emptyCanvasMenuPosition,
      rightClickPosition,
      onCanvasContextMenu,
      onEmptyCanvasRightClick,
      closeEmptyCanvasMenu,
      handleAddNodeFromMenu,
      handlePasteFromMenu,
      handleAutoLayoutFromMenu: handleAutoLayout
    } = useCanvasEvents(canvasRef, canvasPosition, canvasScale, props.readonly, emit)
    
    // Mouse event handlers
    const onMouseDown = (event: MouseEvent) => {
      // Get the target element
      const target = event.target as HTMLElement
      
      // Check if clicked directly on the canvas or a background element
      // We need to exclude node-related elements and only allow background elements
      const isNode = target.closest('.node') !== null
      const isEdge = target.closest('.edge') !== null
      const isContextMenu = target.closest('.context-menu') !== null
      
      // Allow canvas dragging only if we clicked on the background
      if (!isNode && !isEdge && !isContextMenu) {
        // Send close relation selector event
        if (!props.readonly) {
          emit('close-relation-selector')
        }
        
        startCanvasDrag(event)
        
        // Prevent default
        event.preventDefault()
      }
    }
    
    const onMouseMove = (event: MouseEvent) => {
      // Handle canvas dragging
      if (isDraggingCanvas.value) {
        dragCanvas(event)
      }
      
      // Handle connecting nodes
      if (!props.readonly && isConnecting.value) {
        updateConnectingPosition(event)
      }
    }
    
    const onMouseUp = () => {
      endCanvasDrag()
    }
    
    // Toggle thumbnail visibility
    const toggleThumbnail = () => {
      showThumbnail.value = !showThumbnail.value
    }
    
    // Create empty node with proper position
    const createEmptyNode = () => {
      if (props.readonly) return

      console.log('创建新节点', nodes.value.length) // 添加日志帮助调试

      // 定义节点位置
      let position: { x: number; y: number }

      if (nodes.value.length === 0) {
        // 第一个节点 - 偏向左侧，为后续子节点预留空间
        // 参考自动布局中的ROOT_X和ROOT_Y常量
        position = {
          x: 200, // 偏向左侧，原来是400
          y: 300
        }

        // 重置画布位置和缩放，确保节点可见
        canvasStore.setPosition({ x: 0, y: 0 })
        canvasStore.setScale(1.0)
      } else {
        // 后续节点 - 使用当前视图中心计算位置
        const containerWidth = containerSize.value.width || 800
        const containerHeight = containerSize.value.height || 600

        position = {
          x: (containerWidth / 2 - canvasPosition.value.x) / canvasScale.value,
          y: (containerHeight / 2 - canvasPosition.value.y) / canvasScale.value
        }
      }

      // 发送创建节点事件
      emit('create-empty-node', position)

      // 关闭菜单（如果打开）
      if (showEmptyCanvasMenu.value) {
        closeEmptyCanvasMenu()
      }

      // 延迟应用布局，减少闪动效果
      // 使用nextTick确保DOM更新后再应用布局
      nextTick(() => {
        // 应用自动布局，确保新节点遵循布局原则
        // 使用与手动自动布局相同的逻辑
        applyAutoLayout(true)

        // 居中到新节点位置（延迟更短，减少闪动）
        setTimeout(() => {
          centerOnPosition(position)
        }, 100)
      })
    }
    
    // Handle auto layout from menu
    const handleAutoLayoutFromMenu = () => {
      applyAutoLayout(true)
      closeEmptyCanvasMenu()
    }
    
    // Compute the temp edge path
    const tempEdgePath = computed(() => getTempEdgePath())
    
    // Watch for node deletions
    watch(() => nodes.value.length, (newCount, oldCount) => {
      if (newCount < oldCount) {
        // Find the deleted node ID
        const oldNodeIds = new Set(nodes.value.map(node => node.id))
        const deletedNodeId = Array.from(oldNodeIds).find(id => !oldNodeIds.has(id))
        
        if (deletedNodeId) {
          // Notify parent
          emit('node-removed', deletedNodeId)
        }
      }
    })
    
    // Expose methods for external use
    const publicForceAdjustParentUncleSpacing = (parentId: string, childCount: number) => {
      console.log('外部调用强制调整父叔节点间距:', parentId, childCount)
      
      // Build tree
      const { nodeMap } = buildNodeTree()
      
      // Call internal method
      return forceAdjustParentUncleSpacing(nodeMap, parentId, childCount)
    }
    
    return {
      // Refs
      canvasRef,
      
      // Canvas state
      canvasScale,
      canvasPosition,
      canvasDimensions,
      containerSize,
      
      // Node state
      nodes,
      nodesWithDragState,
      selectedNodeId,
      draggedNodeId,
      
      // Edge state
      edges,
      selectedEdgeId,
      isConnecting,
      
      // UI state
      showThumbnail,
      showEmptyCanvasMenu,
      emptyCanvasMenuPosition,
      
      // Event handlers
      onMouseDown,
      onMouseMove,
      onMouseUp,
      handleZoom,
      onCanvasContextMenu,
      
      // Node operations
      getNodeById,
      selectNode,
      moveNode,
      handleDragStart,
      handleDragEnd,
      handleCopyNode,
      handleDeleteNode,
      
      // Edge operations
      selectEdge,
      startConnecting,
      finishConnecting,
      onSymbolClick,
      onConnectorClick,
      getTempEdgePath,
      tempEdgePath,
      
      // Relation handling
      handleAddRelation,
      handleShowAttribution,
      
      // Empty canvas handling
      createEmptyNode,
      onEmptyCanvasRightClick,
      closeEmptyCanvasMenu,
      handleAddNodeFromMenu,
      handlePasteFromMenu,
      handleAutoLayoutFromMenu,
      
      // Layout operations
      applyAutoLayout,
      optimizeNodeSpacing,
      
      // UI operations
      toggleThumbnail,
      
      // Public methods
      forceAdjustParentUncleSpacing: publicForceAdjustParentUncleSpacing
    }
  }
})
</script>

<style lang="scss" scoped>
.canvas {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
  background-color: #fff;
  background-image:
    linear-gradient(rgba(0, 0, 0, 0.05) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 0, 0, 0.05) 1px, transparent 1px);
  background-size: 20px 20px;
}
</style>
