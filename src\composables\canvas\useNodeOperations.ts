import { ref, computed } from 'vue'
import { useNodeStore } from '../../stores/node'
import { useEdgeStore } from '../../stores/edge'
import type { Position } from '../../types'

export function useNodeOperations(readonly = false, emit: Function) {
  const nodeStore = useNodeStore()
  const edgeStore = useEdgeStore()
  
  // Node state
  const nodes = computed(() => nodeStore.nodes)
  const selectedNodeId = computed(() => nodeStore.selectedNodeId)
  const getNodeById = (id: string) => nodeStore.getNodeById(id)
  
  // Dragged node state
  const draggedNodeId = ref<string | null>(null)
  
  // Auto layout flag
  const autoLayoutEnabled = ref(true)
  
  // Nodes with drag state for visual feedback
  const nodesWithDragState = computed(() => {
    return nodes.value.map(node => ({
      ...node,
      isDragging: node.id === draggedNodeId.value
    }))
  })
  
  // Node layout constants - 与自动布局保持一致
  const NODE_HEIGHT = 90
  const NODE_WIDTH = 240
  const SYMBOL_WIDTH = 30
  const MIN_HORIZONTAL_GAP = 380 // 增加到与自动布局一致的间距
  const CONNECTOR_LENGTH = 60
  
  // Helper function to get all descendant nodes (children, grandchildren, etc.)
  const getDescendantNodes = (nodeId: string): any[] => {
    const descendants: any[] = []
    const edgeList = edgeStore.edges
    
    // Find direct children
    const childEdges = edgeList.filter(edge => edge.source === nodeId)
    const childIds = childEdges.map(edge => edge.target)
    
    // Get direct child nodes
    const childNodes = childIds.map(id => getNodeById(id)).filter(Boolean)
    
    // Add direct children to descendants
    descendants.push(...childNodes)
    
    // Recursively get descendants of each child
    childNodes.forEach(childNode => {
      descendants.push(...getDescendantNodes(childNode.id))
    })
    
    return descendants
  }
  
  // Select a node
  const selectNode = (id: string | null) => {
    if (readonly) return

    nodeStore.selectNode(id)
    edgeStore.selectEdge(null)
  }
  
  // Move a node
  const moveNode = (id: string, position: Position) => {
    if (readonly) return
    
    // Disable auto layout when user manually moves nodes
    autoLayoutEnabled.value = false
    
    // Track the dragged node
    draggedNodeId.value = id
    
    // Get the current node
    const currentNode = getNodeById(id)
    if (!currentNode) return
    
    // Calculate the delta movement
    const deltaX = position.x - currentNode.position.x
    const deltaY = position.y - currentNode.position.y
    
    // Update node position
    nodeStore.moveNode(id, position)
    
    // Only move children when a node is dragged by the user (not during auto-layout)
    if (draggedNodeId.value === id) {
      // Get all direct and indirect child nodes
      const childNodes = getDescendantNodes(id)
      
      // Move all child nodes by the same delta
      childNodes.forEach(childNode => {
        nodeStore.moveNode(childNode.id, {
          x: childNode.position.x + deltaX,
          y: childNode.position.y + deltaY
        })
      })
    }
  }
  
  // Create an empty node
  const createEmptyNode = (position: Position) => {
    if (readonly) return
    
    // Send create event to parent
    emit('create-empty-node', position)
  }
  
  // Handle node drag start
  const handleDragStart = (nodeId: string) => {
    draggedNodeId.value = nodeId
    autoLayoutEnabled.value = false
  }
  
  // Handle node drag end
  const handleDragEnd = (nodeId: string) => {
    // Reset dragged node ID
    draggedNodeId.value = null
    
    // Keep auto layout disabled
    autoLayoutEnabled.value = false
    
    // Get the node
    const node = getNodeById(nodeId)
    if (!node) return
    
    // Additional logic for adjusting child nodes can be added here
  }
  
  // Copy a node
  const handleCopyNode = (nodeId: string) => {
    if (readonly) return
    
    // Get the source node
    const sourceNode = getNodeById(nodeId)
    if (!sourceNode) return
    
    // Create a new node with copied data
    const newNode = {
      id: '',
      type: sourceNode.type,
      position: {
        x: sourceNode.position.x + 20, // Offset slightly
        y: sourceNode.position.y + 20
      },
      size: sourceNode.size,
      data: { ...sourceNode.data, label: sourceNode.data.label + ' (复制)' }
    }
    
    // Add the new node
    const newNodeId = nodeStore.addNode(newNode)
    
    // Select the new node
    selectNode(newNodeId)
  }
  
  // Delete a node
  const handleDeleteNode = (nodeId: string) => {
    if (readonly) return
    
    // Remove the node
    nodeStore.removeNode(nodeId)
    
    // Remove related edges
    edgeStore.removeEdgesConnectedToNode(nodeId)
    
    // Deselect
    nodeStore.selectNode(null)
    
    // Notify parent component
    emit('node-removed', nodeId)
  }
  
  // Handle relation
  const handleAddRelation = (nodeId: string, relation: any) => {
    if (readonly) return
    
    // Send event to parent
    emit('add-relation', nodeId, relation)
  }
  
  // Handle attribution
  const handleShowAttribution = (nodeId: string) => {
    emit('show-attribution', nodeId)
  }
  
  return {
    nodes,
    selectedNodeId,
    draggedNodeId,
    nodesWithDragState,
    autoLayoutEnabled,
    getNodeById,
    selectNode,
    moveNode,
    createEmptyNode,
    handleDragStart,
    handleDragEnd,
    handleCopyNode,
    handleDeleteNode,
    handleAddRelation,
    handleShowAttribution,
    NODE_HEIGHT,
    NODE_WIDTH,
    SYMBOL_WIDTH,
    MIN_HORIZONTAL_GAP,
    CONNECTOR_LENGTH
  }
}
