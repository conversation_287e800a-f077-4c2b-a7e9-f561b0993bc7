<template>
  <div
    class="canvas-thumbnail"
  >
    <!-- 缩略图内容 - 显示当前视口的内容 -->
    <div
      class="thumbnail-content"
      :style="thumbnailContentStyle"
    >
      <!-- 渲染所有连线 -->
      <svg class="thumbnail-edges" width="100%" height="100%">
        <path
          v-for="edge in edges"
          :key="edge.id"
          class="thumbnail-edge"
          :d="getEdgePath(edge)"
          :stroke="edge.color || '#1890ff'"
          stroke-width="2"
          fill="none"
        />
      </svg>

      <!-- 渲染所有节点 -->
      <div
        v-for="node in nodes"
        :key="node.id"
        class="thumbnail-node"
        :class="{
          'node-selected': node.id === $props.selectedNodeId,
          'node-dragging': false // Removed isDragging property as it doesn't exist in Node type
        }"
        :style="{
          left: `${node.position.x}px`,
          top: `${node.position.y}px`,
          width: `${node.size?.width || 240}px`,
          height: `${node.size?.height || 90}px`,
          backgroundColor: getNodeColor(node),
          borderLeftColor: getNodeBorderColor(node),
          opacity: 1,
          transition: 'transform 0.1s ease' // Removed isDragging check as it doesn't exist in Node type
        }"
      >
        <!-- 节点内容 -->
        <div class="thumbnail-node-header">{{ node.data.label }}</div>
        <div class="thumbnail-node-value">{{ node.data.value }}</div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, computed, ref } from 'vue'
import type { PropType } from 'vue'
import type { Node, Edge, Position, Size } from '../../types'

export default defineComponent({
  name: 'CanvasThumbnail',
  props: {
    nodes: {
      type: Array as PropType<Node[]>,
      required: true
    },
    edges: {
      type: Array as PropType<Edge[]>,
      required: true
    },
    canvasPosition: {
      type: Object as PropType<Position>,
      required: true
    },
    canvasScale: {
      type: Number,
      required: true
    },
    canvasDimensions: {
      type: Object as PropType<Size>,
      required: true
    },
    containerSize: {
      type: Object as PropType<Size>,
      required: true
    },
    selectedNodeId: {
      type: String,
      default: null
    }
  },
  emits: ['pan'],
  setup(props, { emit }) {
    // 缩略图状态
    const isDraggingIndicator = ref(false)
    const isDraggingThumbnail = ref(false)
    const lastMousePosition = ref<Position>({ x: 0, y: 0 })

    // 计算缩略图的缩放比例 - 基于容器大小和缩略图大小计算
    const thumbnailScale = computed(() => {
      // 缩略图的尺寸
      const thumbnailWidth = 220
      const thumbnailHeight = 120

      // 计算缩放比例，使其与主画布保持相同的比例
      const scaleX = thumbnailWidth / props.containerSize.width
      const scaleY = thumbnailHeight / props.containerSize.height

      // 取较小的缩放比例，确保整个视口内容都能显示
      return Math.min(scaleX, scaleY) * 0.7 // 稍微缩小一点，留出边距
    })

    // 计算缩略图内容的位置和样式
    const thumbnailContentStyle = computed(() => {
      // 计算缩略图内容的位置，使其显示当前视口的内容
      const scale = thumbnailScale.value

      // 计算平移量，使内容居中显示
      const translateX = props.canvasPosition.x / props.canvasScale
      const translateY = props.canvasPosition.y / props.canvasScale

      return {
        transform: `translate(-50%, -50%) scale(${scale}) translate(${translateX}px, ${translateY}px)`,
        width: `${props.containerSize.width}px`,
        height: `${props.containerSize.height}px`
      }
    })

    // 获取连线路径
    const getEdgePath = (edge: Edge) => {
      // 获取源节点和目标节点
      const sourceNode = props.nodes.find(node => node.id === edge.source)
      const targetNode = props.nodes.find(node => node.id === edge.target)

      if (!sourceNode || !targetNode) return ''

      // 计算连线的起点和终点
      const sourceX = sourceNode.position.x + (sourceNode.size?.width || 240)
      const sourceY = sourceNode.position.y + (sourceNode.size?.height || 90) / 2
      const targetX = targetNode.position.x
      const targetY = targetNode.position.y + (targetNode.size?.height || 90) / 2

      // 计算控制点 - 使用贝塞尔曲线创建平滑的连线
      const dx = Math.abs(targetX - sourceX)
      const offsetX = dx * 0.4

      // 创建贝塞尔曲线路径
      const controlPoint1X = sourceX + offsetX
      const controlPoint1Y = sourceY
      const controlPoint2X = targetX - offsetX
      const controlPoint2Y = targetY

      return `M ${sourceX} ${sourceY} C ${controlPoint1X} ${controlPoint1Y}, ${controlPoint2X} ${controlPoint2Y}, ${targetX} ${targetY}`
    }

    // 视口指示器拖拽事件
    const onIndicatorMouseDown = (event: MouseEvent) => {
      isDraggingIndicator.value = true
      lastMousePosition.value = { x: event.clientX, y: event.clientY }
      event.stopPropagation()
    }

    // 缩略图点击事件
    const onThumbnailMouseDown = (event: MouseEvent) => {
      if (isDraggingIndicator.value) return

      isDraggingThumbnail.value = true

      // 计算点击位置对应的画布位置
      const thumbnailRect = (event.currentTarget as HTMLElement).getBoundingClientRect()
      const clickX = (event.clientX - thumbnailRect.left) / thumbnailScale.value
      const clickY = (event.clientY - thumbnailRect.top) / thumbnailScale.value

      // 计算新的画布位置，使点击位置居中
      const newX = -clickX * props.canvasScale + props.containerSize.width / 2
      const newY = -clickY * props.canvasScale + props.containerSize.height / 2

      // 发送平移事件
      emit('pan', { x: newX, y: newY })
    }

    // 鼠标移动事件
    const onThumbnailMouseMove = (event: MouseEvent) => {
      if (!isDraggingIndicator.value && !isDraggingThumbnail.value) return

      // 计算鼠标移动的距离
      const deltaX = event.clientX - lastMousePosition.value.x
      const deltaY = event.clientY - lastMousePosition.value.y

      // 更新最后的鼠标位置
      lastMousePosition.value = { x: event.clientX, y: event.clientY }

      if (isDraggingIndicator.value) {
        // 如果是拖动视口指示器，直接平移画布
        const panDeltaX = -deltaX / thumbnailScale.value * props.canvasScale
        const panDeltaY = -deltaY / thumbnailScale.value * props.canvasScale

        // 发送平移事件
        emit('pan', {
          x: props.canvasPosition.x + panDeltaX,
          y: props.canvasPosition.y + panDeltaY
        })
      } else if (isDraggingThumbnail.value) {
        // 如果是拖动缩略图，计算新的画布位置
        const thumbnailRect = (event.currentTarget as HTMLElement).getBoundingClientRect()
        const clickX = (event.clientX - thumbnailRect.left) / thumbnailScale.value
        const clickY = (event.clientY - thumbnailRect.top) / thumbnailScale.value

        // 计算新的画布位置，使点击位置居中
        const newX = -clickX * props.canvasScale + props.containerSize.width / 2
        const newY = -clickY * props.canvasScale + props.containerSize.height / 2

        // 发送平移事件
        emit('pan', { x: newX, y: newY })
      }
    }

    // 鼠标释放事件
    const onThumbnailMouseUp = () => {
      isDraggingIndicator.value = false
      isDraggingThumbnail.value = false
    }

    // 获取节点颜色
    const getNodeColor = (node: Node) => {
      // 如果节点有自定义背景色，使用自定义背景色
      if (node.data.backgroundColor) {
        return node.data.backgroundColor;
      }

      // 根据节点类型设置不同的背景色
      if (node.type === 'kpi') {
        return '#e6f7ff';
      } else if (node.type === 'input') {
        return '#f0f5ff';
      } else if (node.id === props.selectedNodeId) {
        return '#f0f5ff'; // 选中节点的颜色
      }

      // 默认背景色
      return 'white';
    }

    // 获取节点边框颜色
    const getNodeBorderColor = (node: Node) => {
      if (node.data.color) {
        return node.data.color;
      }

      if (node.id === props.selectedNodeId) {
        return '#1890ff';
      }

      return '#1890ff';
    }

    return {
      thumbnailScale,
      thumbnailContentStyle,
      getEdgePath,
      getNodeColor,
      getNodeBorderColor
    }
  }
})
</script>

<style lang="scss" scoped>
.canvas-thumbnail {
  position: absolute;
  bottom: 20px;
  right: 20px;
  width: 220px;
  height: 120px;
  background-color: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 100;
  display: flex;
  justify-content: center;
  align-items: center;

  .thumbnail-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform-origin: center center;
    background-color: transparent;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .thumbnail-edges {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    overflow: visible;
    z-index: 0;
  }

  .thumbnail-node {
    position: absolute;
    border-radius: 2px;
    transform-origin: top left;
    z-index: 1;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border-left: 4px solid;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    background-color: white;

    .thumbnail-node-header {
      font-size: 6px;
      padding: 2px 3px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      border-bottom: 1px solid rgba(0, 0, 0, 0.05);
      color: #333;
      font-weight: 500;
    }

    .thumbnail-node-value {
      font-size: 8px;
      font-weight: bold;
      padding: 2px 3px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      color: #333;
    }

    &.node-selected {
      box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.2), 0 1px 4px rgba(0, 0, 0, 0.1);
    }

    &.node-dragging {
      z-index: 2;
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
    }
  }

  .thumbnail-edge {
    fill: none;
    stroke-width: 1.5;
    pointer-events: none;
    stroke-linecap: round;
    stroke-linejoin: round;
    opacity: 0.9;
  }


}
</style>
