import { computed, ref } from 'vue'
import { useNodeStore } from '../../stores/node'

// Layout constants - 优化间距以匹配自动布局效果
const FIXED_HORIZONTAL_SPACING = 280 // 水平间距 - 从380减少到280
const VERTICAL_SPACING = 180
const PARENT_UNCLE_VERTICAL_SPACING = 280
const ROOT_X = 200 // 与Canvas.vue中第一个节点位置保持一致
const ROOT_Y = 300 // 与Canvas.vue中第一个节点位置保持一致
const HORIZONTAL_SPACING = FIXED_HORIZONTAL_SPACING
const PARENT_CHILD_SPACING = FIXED_HORIZONTAL_SPACING // 父子节点间距 - 从380减少到280
const SIBLING_VERTICAL_SPACING = 120 // 兄弟节点垂直间距 - 从220减少到120
const COUSIN_VERTICAL_SPACING = 100 // 从150减少到100
const MIN_NODE_DISTANCE = 50 // 增加最小节点距离，防止重叠
const MAX_ITERATIONS = 5
const MIN_VERTICAL_GAP = 80 // 从120减少到80
const VERTICAL_GAP = 100 // 从160减少到100
const TERMINAL_NODE_SPACING = 120 // 终端节点间距 - 从200减少到120
const CHILD_NODE_SPACING = 120 // 子节点间距 - 从200减少到120
const FUTURE_NODE_SPACE_FACTOR = 1.2
const PARENT_UNCLE_SPACING_INCREMENT = 80 // 增加父叔节点间距增量
const BASE_PARENT_UNCLE_SPACING = 200 // 增加基础父叔节点间距
const PROPAGATION_FACTOR = 1.2
const SYMMETRY_PRIORITY_FACTOR = 1.0
const MIN_PARENT_UNCLE_GAP = 200 // 增加最小父叔节点间距
const SIBLING_SPACING_MULTIPLIER = 1.0
const FORCE_PARENT_UNCLE_SPACING = true
const GLOBAL_NODE_OVERLAP_CHECK = true
const OVERLAP_SAFETY_MARGIN = 120 // 增加重叠安全边距
const ANCESTOR_PROPAGATION_DEPTH = 5
const EXPONENTIAL_BASE = 3.0
const FIXED_SPACING_PER_CHILD = 1500

export function useLayoutCalculation(buildNodeTree: Function, getNodeById: Function) {
  const nodeStore = useNodeStore()
  
  // Auto layout enabled flag
  const autoLayoutEnabled = ref(true)
  
  // Apply auto layout to the nodes
  const applyAutoLayout = (forceLayout = false) => {
    // Skip if auto layout is disabled and not forced
    if (!autoLayoutEnabled.value && !forceLayout) return
    
    // Build node relationship tree
    const { nodeMap, rootNodes } = buildNodeTree()
    
    // If no root nodes, can't layout
    if (rootNodes.length === 0) return
    
    // Sort root nodes by ID to ensure consistent layout
    rootNodes.sort()
    
    // Layout root nodes
    let currentY = ROOT_Y
    
    // First pass: calculate required space and vertical positions
    rootNodes.forEach(rootId => {
      const rootInfo = nodeMap.get(rootId)
      if (!rootInfo) return
      
      // Position root node
      nodeStore.moveNode(rootId, {
        x: ROOT_X,
        y: currentY
      })
      
      // Calculate vertical space needed by this subtree
      const requiredSpace = rootInfo.requiredSpace || 0
      
      // Update current Y for next root node
      currentY += Math.max(requiredSpace, VERTICAL_SPACING) + VERTICAL_GAP
    })
    
    // Second pass: recursively layout children
    const layoutChildren = (nodeId, depth = 0) => {
      const nodeInfo = nodeMap.get(nodeId)
      if (!nodeInfo || nodeInfo.children.length === 0) return
      
      const node = nodeInfo.node
      const childX = node.position.x + (node.size?.width || 240) + PARENT_CHILD_SPACING
      
      // Sort children by ID for consistent layout
      const sortedChildren = [...nodeInfo.children].sort()
      
      // 特殊处理只有一个子节点的情况 - 完美水平中心对齐
      if (sortedChildren.length === 1) {
        const childId = sortedChildren[0]
        const childInfo = nodeMap.get(childId)
        if (!childInfo) return
        
        // 计算父节点中心Y坐标
        const parentCenterY = node.position.y + (node.size?.height || 90) / 2
        // 计算子节点应该的Y坐标，使其中心与父节点中心对齐
        const childY = parentCenterY - (childInfo.node.size?.height || 90) / 2
        
        const newPosition = {
          x: childX,
          y: childY // 精确水平中心对齐
        }
        
        // 更新子节点位置
        nodeStore.moveNode(childId, newPosition)
        
        // 在节点信息中标记为有单个子节点
        nodeInfo.hasSingleChild = true
        childInfo.node.position = newPosition
        
        // 递归布局该子节点的所有子节点
        layoutChildren(childId, depth + 1)
        return
      }
      
      // For multiple children, ensure symmetrical layout
      // Calculate total vertical space required by children
      let totalChildrenSpace = 0
      sortedChildren.forEach(childId => {
        const childInfo = nodeMap.get(childId)
        if (!childInfo) return
        
        // Use node height or calculated required space
        totalChildrenSpace += childInfo.requiredSpace || 0
      })
      
      // Calculate spacing between children
      const spacing = nodeInfo.isTerminalNode ? TERMINAL_NODE_SPACING : CHILD_NODE_SPACING
      
      // Add spacing between children
      totalChildrenSpace += (sortedChildren.length - 1) * spacing
      
      // 计算父节点中心Y坐标
      const parentCenterY = node.position.y + (node.size?.height || 90) / 2
      
      // Start position for first child - center align the entire child group with parent center
      let currentChildY = parentCenterY - (totalChildrenSpace / 2)
      
      // Position each child
      sortedChildren.forEach((childId, index) => {
        const childInfo = nodeMap.get(childId)
        if (!childInfo) return
        
        // Store sibling information for future reference
        childInfo.siblingIndex = index
        childInfo.totalSiblings = sortedChildren.length
        childInfo.siblingNodes = sortedChildren.filter(id => id !== childId)
        
        // Get child's required space
        const childSpace = childInfo.requiredSpace || 0
        
        // Center child vertically in its allocated space
        const childY = currentChildY + (childSpace / 2) - ((childInfo.node.size?.height || 0) / 2)
        
        // Move child node
        nodeStore.moveNode(childId, {
          x: childX,
          y: childY
        })
        
        // Update current Y position for next child
        currentChildY += childSpace + spacing
        
        // Recursively layout this child's children
        layoutChildren(childId, depth + 1)
      })
    }
    
    // Layout children of all root nodes
    rootNodes.forEach(rootId => {
      layoutChildren(rootId)
    })
    
    // Adjust spacing between parent and uncle nodes
    const adjustParentUncleSpacing = () => {
      // Detect nodes with new children and adjust spacing
      let changes = false
      
      nodeMap.forEach((nodeInfo, nodeId) => {
        if (nodeInfo.hasNewChildren) {
          changes = forceAdjustParentUncleSpacing(nodeMap, nodeId, nodeInfo.childrenCount) || changes
        }
      })
      
      return changes
    }
    
    // Execute parent-uncle spacing adjustment
    let iterationCount = 0
    let changes = true
    
    while (changes && iterationCount < MAX_ITERATIONS) {
      changes = adjustParentUncleSpacing()
      iterationCount++
    }
    
    // Final step: Check for any remaining node overlaps
    if (GLOBAL_NODE_OVERLAP_CHECK) {
      resolveGlobalOverlaps(nodeMap)
    }
    
    // Enable auto layout for future changes
    autoLayoutEnabled.value = true
  }
  
  // Force adjust spacing between parent and uncle nodes
  const forceAdjustParentUncleSpacing = (nodeMap: Map<string, any>, parentId: string, childCount: number) => {
    const parentInfo = nodeMap.get(parentId)
    if (!parentInfo || !parentInfo.parent) return false
    
    // Get grandparent
    const grandparentId = parentInfo.parent
    const grandparentInfo = nodeMap.get(grandparentId)
    if (!grandparentInfo) return false
    
    // Get parent's siblings (uncles)
    const uncles = grandparentInfo.children.filter(id => id !== parentId)
    if (uncles.length === 0) return false
    
    // Calculate needed space based on child count
    let neededSpace
    
    // Progressive spacing calculation
    if (childCount <= 2) {
      // For 1-2 children, use base spacing
      neededSpace = BASE_PARENT_UNCLE_SPACING
    } else {
      // For 3+ children, increase spacing exponentially
      neededSpace = BASE_PARENT_UNCLE_SPACING + 
                    PARENT_UNCLE_SPACING_INCREMENT * Math.pow(childCount - 2, EXPONENTIAL_BASE)
    }
    
    // Always ensure minimum spacing
    neededSpace = Math.max(neededSpace, MIN_PARENT_UNCLE_GAP)
    
    let changes = false
    
    // Adjust each uncle's position
    uncles.forEach(uncleId => {
      const uncleInfo = nodeMap.get(uncleId)
      if (!uncleInfo) return
      
      const parentNode = parentInfo.node
      const uncleNode = uncleInfo.node
      
      // Calculate current vertical distance
      const currentDistance = Math.abs(parentNode.position.y - uncleNode.position.y)
      
      // If spacing is insufficient
      if (currentDistance < neededSpace) {
        // Direction to move the uncle (up or down)
        const direction = parentNode.position.y > uncleNode.position.y ? -1 : 1
        
        // Calculate new position
        const newY = uncleNode.position.y + direction * (neededSpace - currentDistance)
        
        // Move uncle node
        nodeStore.moveNode(uncleId, {
          x: uncleNode.position.x,
          y: newY
        })
        
        // Update node position in the map
        uncleInfo.node.position.y = newY
        
        changes = true
        
        // Propagate the adjustment to the uncle's children
        propagateVerticalAdjustment(nodeMap, uncleId, direction * (neededSpace - currentDistance))
      }
    })
    
    // Reset the new children flag
    parentInfo.hasNewChildren = false
    
    return changes
  }
  
  // Propagate vertical adjustment to children
  const propagateVerticalAdjustment = (nodeMap: Map<string, any>, nodeId: string, deltaY: number, depth = 0) => {
    if (depth > ANCESTOR_PROPAGATION_DEPTH) return
    
    const nodeInfo = nodeMap.get(nodeId)
    if (!nodeInfo) return
    
    // Adjust all children
    nodeInfo.children.forEach(childId => {
      const childInfo = nodeMap.get(childId)
      if (!childInfo) return
      
      // Move child node
      const childNode = childInfo.node
      const newY = childNode.position.y + deltaY
      
      nodeStore.moveNode(childId, {
        x: childNode.position.x,
        y: newY
      })
      
      // Update node position in the map
      childInfo.node.position.y = newY
      
      // Recursively propagate to this child's children
      propagateVerticalAdjustment(nodeMap, childId, deltaY, depth + 1)
    })
  }
  
  // Resolve global node overlaps
  const resolveGlobalOverlaps = (nodeMap: Map<string, any>) => {
    // Get all nodes
    const nodes = Array.from(nodeMap.values()).map((info: any) => info.node)
    
    // Multiple iterations to resolve all overlaps
    for (let i = 0; i < MAX_ITERATIONS; i++) {
      let overlapsResolved = 0
      
      // Check each pair of nodes for overlap
      for (let j = 0; j < nodes.length; j++) {
        for (let k = j + 1; k < nodes.length; k++) {
          const nodeA = nodes[j]
          const nodeB = nodes[k]
          
          // Skip if nodes are related (parent-child)
          const nodeAInfo = nodeMap.get(nodeA.id)
          const nodeBInfo = nodeMap.get(nodeB.id)
          
          if (!nodeAInfo || !nodeBInfo) continue
          
          // Skip if direct parent-child relationship
          if (nodeAInfo.children.includes(nodeB.id) || nodeBInfo.children.includes(nodeA.id)) {
            continue
          }
          
          // Check for overlap
          if (nodesOverlap(nodeA, nodeB, OVERLAP_SAFETY_MARGIN)) {
            // Resolve overlap by moving the nodes apart
            if (resolveNodeOverlap(nodeA, nodeB, nodeMap)) {
              overlapsResolved++
            }
          }
        }
      }
      
      // If no overlaps were resolved in this iteration, we're done
      if (overlapsResolved === 0) break
    }
  }
  
  // Check if two nodes overlap
  const nodesOverlap = (nodeA: any, nodeB: any, margin = 0) => {
    const a = {
      left: nodeA.position.x - margin,
      right: nodeA.position.x + (nodeA.size?.width || 240) + margin,
      top: nodeA.position.y - margin,
      bottom: nodeA.position.y + (nodeA.size?.height || 90) + margin
    }
    
    const b = {
      left: nodeB.position.x - margin,
      right: nodeB.position.x + (nodeB.size?.width || 240) + margin,
      top: nodeB.position.y - margin,
      bottom: nodeB.position.y + (nodeB.size?.height || 90) + margin
    }
    
    return !(a.right < b.left || a.left > b.right || a.bottom < b.top || a.top > b.bottom)
  }
  
  // Resolve overlap between two nodes
  const resolveNodeOverlap = (nodeA: any, nodeB: any, nodeMap: Map<string, any>) => {
    // Calculate overlap dimensions
    const overlapX = Math.min(
      nodeA.position.x + (nodeA.size?.width || 240) - nodeB.position.x,
      nodeB.position.x + (nodeB.size?.width || 240) - nodeA.position.x
    )
    
    const overlapY = Math.min(
      nodeA.position.y + (nodeA.size?.height || 90) - nodeB.position.y,
      nodeB.position.y + (nodeB.size?.height || 90) - nodeA.position.y
    )
    
    // No overlap
    if (overlapX <= 0 || overlapY <= 0) return false
    
    // Determine which direction requires less movement
    if (overlapX < overlapY) {
      // Move horizontally
      const moveLeft = nodeA.position.x < nodeB.position.x
      
      if (moveLeft) {
        // Move nodeB right
        const newX = nodeB.position.x + overlapX + MIN_NODE_DISTANCE
        nodeStore.moveNode(nodeB.id, { x: newX, y: nodeB.position.y })
        nodeB.position.x = newX
      } else {
        // Move nodeA right
        const newX = nodeA.position.x + overlapX + MIN_NODE_DISTANCE
        nodeStore.moveNode(nodeA.id, { x: newX, y: nodeA.position.y })
        nodeA.position.x = newX
      }
    } else {
      // Move vertically
      const moveUp = nodeA.position.y < nodeB.position.y
      
      if (moveUp) {
        // Move nodeB down
        const newY = nodeB.position.y + overlapY + MIN_NODE_DISTANCE
        nodeStore.moveNode(nodeB.id, { x: nodeB.position.x, y: newY })
        nodeB.position.y = newY
      } else {
        // Move nodeA down
        const newY = nodeA.position.y + overlapY + MIN_NODE_DISTANCE
        nodeStore.moveNode(nodeA.id, { x: nodeA.position.x, y: newY })
        nodeA.position.y = newY
      }
    }
    
    return true
  }
  
  return {
    autoLayoutEnabled,
    applyAutoLayout,
    forceAdjustParentUncleSpacing
  }
}
