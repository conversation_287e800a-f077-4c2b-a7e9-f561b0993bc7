<template>
  <svg class="edge" :class="{ selected, highlighted, readonly }" :data-edge-id="edge.id">
    <!-- Calculation Symbol Group -->
    <g
      class="relation-symbol"
      :transform="`translate(${symbolPosition.x - 15}, ${symbolPosition.y - 15})`"
      @click="onSymbolClick"
    >
      <rect
        width="30"
        height="30"
        rx="4"
        fill="white"
        :stroke="getSymbolStrokeColor()"
        :stroke-width="selected || highlighted ? 1.5 : 1"
      />
      <text
        x="15"
        y="15"
        text-anchor="middle"
        dominant-baseline="middle"
        font-size="16"
        font-weight="bold"
        :fill="getSymbolTextColor()"
      >
        {{ edge.label || 'C' }}
      </text>
    </g>

    <!-- 第1部分：从父节点到计算符号的实线（带箭头） -->
    <path
      :d="getLeftSolidPath()"
      :stroke="getArrowStrokeColor()"
      :stroke-width="selected || highlighted ? 2 : 1.5"
      fill="none"
      class="arrow-path left-solid-part"
      marker-end="url(#arrowhead-solid)"
    />

    <!-- 第3部分：从计算符号右侧出发的虚线 -->
    <g class="right-dashed-group" @click.stop="onRightDashedClick">
      <!-- 正常状态的虚线 -->
      <path
        v-show="!isHoveringRightDashed"
        :d="getRightDashedPath()"
        :stroke="getArrowStrokeColor()"
        :stroke-width="selected || highlighted ? 2 : 1.5"
        fill="none"
        :stroke-dasharray="highlighted ? '5,3' : '3,3'"
        class="arrow-path right-dashed-part"
        :marker-end="getRightDashedMarker()"
      />
      <!-- Hover状态的粗实线箭头 -->
      <path
        v-show="isHoveringRightDashed"
        :d="getRightDashedPath()"
        :stroke="getArrowStrokeColor()"
        stroke-width="3"
        fill="none"
        class="arrow-path right-dashed-part-hover"
        marker-end="url(#arrowhead-hover)"
      />
      <!-- 交互区域 -->
      <path
        :d="getRightDashedPath()"
        stroke="transparent"
        stroke-width="12"
        fill="none"
        class="right-dashed-hitbox"
        @mouseenter="isHoveringRightDashed = true"
        @mouseleave="isHoveringRightDashed = false"
      />
    </g>

    <!-- 第4部分：从虚线末尾到目标节点的连接线（仅在有目标节点时显示） -->
    <path
      v-if="!edge.isTemporary && targetNode"
      :d="getTargetConnectionPath()"
      :stroke="getArrowStrokeColor()"
      :stroke-width="selected || highlighted ? 2 : 1.5"
      fill="none"
      :stroke-dasharray="highlighted ? '5,3' : '3,3'"
      class="arrow-path target-connection-part"
      :marker-end="getArrowMarker()"
    />

    <!-- Arrowhead Definitions -->
    <defs>
      <!-- Default Arrowhead -->
      <marker
        id="arrowhead-default"
        viewBox="0 0 10 10"
        refX="8"
        refY="5"
        markerWidth="6"
        markerHeight="6"
        orient="auto-start-reverse"
      >
        <path d="M 0 0 L 10 5 L 0 10 z" fill="#c1c7d0" />
      </marker>
      <!-- Active/Hover Arrowhead -->
      <marker
        id="arrowhead-active"
        viewBox="0 0 10 10"
        refX="8"
        refY="5"
        markerWidth="8"
        markerHeight="8"
        orient="auto-start-reverse"
      >
        <path d="M 0 0 L 10 5 L 0 10 z" fill="#1890ff" />
      </marker>
      <!-- Highlighted Arrowhead -->
      <marker
        id="arrowhead-highlighted"
        viewBox="0 0 10 10"
        refX="8"
        refY="5"
        markerWidth="8"
        markerHeight="8"
        orient="auto-start-reverse"
      >
        <path d="M 0 0 L 10 5 L 0 10 z" fill="#52c41a" />
      </marker>

      <!-- Solid Arrowhead for first part -->
      <marker
        id="arrowhead-solid"
        viewBox="0 0 10 10"
        refX="8"
        refY="5"
        markerWidth="6"
        markerHeight="6"
        orient="auto-start-reverse"
      >
        <path d="M 0 0 L 10 5 L 0 10 z" :fill="getArrowStrokeColor()" />
      </marker>

      <!-- Hover Arrowhead for third part -->
      <marker
        id="arrowhead-hover"
        viewBox="0 0 10 10"
        refX="8"
        refY="5"
        markerWidth="8"
        markerHeight="8"
        orient="auto-start-reverse"
      >
        <path d="M 0 0 L 10 5 L 0 10 z" :fill="getArrowStrokeColor()" />
      </marker>
    </defs>
  </svg>
</template>

<script lang="ts">
import { defineComponent, computed, ref } from 'vue'
import type { Edge, Node, Position } from '../../types'

export default defineComponent({
  name: 'Edge',
  props: {
    edge: {
      type: Object as () => Edge,
      required: true
    },
    sourceNode: {
      type: Object as () => Node | null,
      required: true
    },
    targetNode: {
      type: Object as () => Node | null,
      required: true
    },
    selected: {
      type: Boolean,
      default: false
    },
    readonly: {
      type: Boolean,
      default: false
    },
    highlighted: {
      type: Boolean,
      default: false
    }
  },
  emits: ['select', 'symbol-click', 'connector-click'],
  setup(props, { emit }) {
    const isHoveringRightDashed = ref(false)

    // Calculate connection points
    const sourcePoint = computed(() => {
      if (!props.sourceNode) return { x: 0, y: 0 }
      return {
        x: props.sourceNode.position.x + (props.sourceNode.size?.width || 240),
        y: props.sourceNode.position.y + (props.sourceNode.size?.height || 90) / 2
      }
    })

    const targetPoint = computed(() => {
      if (!props.targetNode) return { x: 0, y: 0 }
      return {
        x: props.targetNode.position.x,
        y: props.targetNode.position.y + (props.targetNode.size?.height || 90) / 2
      }
    })

    // Calculate symbol position - 调整源节点到计算符号的距离
    const symbolPosition = computed(() => {
      if (!props.sourceNode) return { x: 0, y: 0 }
      return {
        x: sourcePoint.value.x + 100, // 进一步增加第一段连线长度，从80增加到100
        y: sourcePoint.value.y
      }
    })

    // Calculate right dashed start point - 第3部分虚线的起点（计算符号右侧）
    const rightDashedStartPoint = computed(() => {
      if (!props.sourceNode) return { x: 0, y: 0 }
      return {
        x: symbolPosition.value.x + 15, // 降低第二段间距，从20改回15
        y: symbolPosition.value.y
      }
    })

    // Calculate right dashed end point - 第3部分虚线的终点（固定长度）
    const rightDashedEndPoint = computed(() => {
      if (!props.sourceNode) return { x: 0, y: 0 }
      return {
        x: rightDashedStartPoint.value.x + 50, // 进一步降低第三段连线长度，从60px改为50px
        y: rightDashedStartPoint.value.y
      }
    })



    // 第1部分：从父节点到计算符号的实线
    const getLeftSolidPath = () => {
      if (!props.sourceNode) return ''
      const sourceStart = sourcePoint.value
      const symbolStart = { x: symbolPosition.value.x - 20, y: symbolPosition.value.y } // 增加间隙，从-15改为-20
      return `M ${sourceStart.x} ${sourceStart.y} L ${symbolStart.x} ${symbolStart.y}`
    }

    // 第3部分：从计算符号右侧出发的虚线（带箭头）
    const getRightDashedPath = () => {
      if (!props.sourceNode) return ''
      const start = rightDashedStartPoint.value
      const end = rightDashedEndPoint.value
      return `M ${start.x} ${start.y} L ${end.x} ${end.y}`
    }

    // 第4部分：从虚线箭头到目标节点的连接线
    const getTargetConnectionPath = () => {
      if (!props.targetNode || props.edge.isTemporary) return ''

      const start = rightDashedEndPoint.value
      const end = targetPoint.value

      // 检查目标节点是否在虚线起点左侧
      if (end.x <= start.x) {
        // 如果目标节点在虚线起点左侧，使用曲线连接
        const midY = (start.y + end.y) / 2
        return `M ${start.x} ${start.y} Q ${start.x + 30} ${start.y}, ${start.x + 30} ${midY} T ${end.x} ${end.y}`
      }

      // 正常情况：目标节点在虚线起点右侧
      const dx = Math.abs(end.x - start.x)
      const dy = end.y - start.y

      // 检查是否在同一水平线上（允许小误差）
      const sameLevel = Math.abs(dy) < 5

      if (sameLevel) {
        // 同一水平线，使用直线
        return `M ${start.x} ${start.y} L ${end.x} ${end.y}`
      }

      // 不在同一水平线上，使用简单的贝塞尔曲线
      const offsetX = Math.min(dx * 0.3, 50)
      const controlPoint1 = {
        x: start.x + offsetX,
        y: start.y
      }
      const controlPoint2 = {
        x: end.x - offsetX,
        y: end.y
      }

      return `M ${start.x} ${start.y} C ${controlPoint1.x} ${controlPoint1.y}, ${controlPoint2.x} ${controlPoint2.y}, ${end.x} ${end.y}`
    }

    // 获取第3部分虚线的箭头标记（根据是否有目标节点决定是否显示箭头）
    const getRightDashedMarker = () => {
      // 如果有目标节点，不显示箭头（因为第4部分会有箭头）
      if (!props.edge.isTemporary && props.targetNode) {
        return 'none'
      }
      // 临时连线显示箭头
      if (props.highlighted) return 'url(#arrowhead-highlighted)'
      if (props.selected || isHoveringRightDashed.value) return 'url(#arrowhead-active)'
      return 'url(#arrowhead-default)'
    }

    // Emit event when calculation symbol is clicked
    const onSymbolClick = (event: MouseEvent) => {
      // 如果是只读模式，不处理点击事件
      if (props.readonly) return

      // 阻止事件冒泡
      event.stopPropagation()

      // 传递边的ID、计算符号的精确位置和原始DOM事件
      emit('symbol-click', props.edge.id, {
        x: symbolPosition.value.x,
        y: symbolPosition.value.y,
        domEvent: event,
        symbolSize: 30 // 符号的大小，用于精确定位弹窗
      })
    }

    // 第3部分虚线点击事件（创建子节点）
    const onRightDashedClick = () => {
      // 如果是只读模式，不处理点击事件
      if (props.readonly) return

      // 无论是临时连线还是完整连线，都触发创建子节点事件
      emit('select', props.edge.id)
      const position: Position = {
        x: rightDashedEndPoint.value.x,
        y: rightDashedEndPoint.value.y
      }
      emit('connector-click', props.edge.id, position)
    }

    // 获取符号边框颜色
    const getSymbolStrokeColor = () => {
      if (props.highlighted) return '#52c41a' // 高亮状态使用绿色
      if (props.selected) return '#1890ff' // 选中状态使用蓝色
      return '#e8e8e8' // 默认状态使用浅灰色
    }

    // 获取符号文本颜色
    const getSymbolTextColor = () => {
      if (props.highlighted) return '#52c41a' // 高亮状态使用绿色
      if (props.selected) return '#1890ff' // 选中状态使用蓝色
      return '#666' // 默认状态使用深灰色
    }

    // 获取箭头线条颜色
    const getArrowStrokeColor = () => {
      if (props.highlighted) return '#52c41a' // 高亮状态使用绿色
      if (props.selected || isHoveringRightDashed.value) return '#1890ff' // 选中或悬停状态使用蓝色
      return '#c1c7d0' // 默认状态使用浅灰色
    }

    // 获取第4部分的箭头标记
    const getArrowMarker = () => {
      if (props.highlighted) return 'url(#arrowhead-highlighted)' // 高亮状态使用绿色箭头
      if (props.selected || isHoveringRightDashed.value) return 'url(#arrowhead-active)' // 选中或悬停状态使用蓝色箭头
      return 'url(#arrowhead-default)' // 默认状态使用灰色箭头
    }

    return {
      symbolPosition,
      rightDashedStartPoint,
      rightDashedEndPoint,
      isHoveringRightDashed,
      getLeftSolidPath,
      getRightDashedPath,
      getTargetConnectionPath,
      getRightDashedMarker,
      getSymbolStrokeColor,
      getSymbolTextColor,
      getArrowStrokeColor,
      getArrowMarker,
      onSymbolClick,
      onRightDashedClick
    }
  }
})
</script>

<style lang="scss" scoped>
.edge {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;

  .relation-symbol {
    pointer-events: all;
    cursor: pointer;
    transition: transform 0.1s ease-out;

    rect {
      transition: stroke 0.2s ease, fill 0.2s ease;
    }
    text {
      transition: fill 0.2s ease;
      user-select: none;
    }
  }

  .right-dashed-group {
    pointer-events: none; // Group itself doesn't capture events
  }

  .arrow-path {
    transition: stroke 0.2s ease;
  }

  .right-dashed-hitbox {
    pointer-events: all; // Hitbox captures events for the right dashed group
    cursor: pointer;
  }

  &.selected {
    z-index: 10;

    .arrow-path { // Highlight path when edge is selected
       stroke: #1890ff;
    }

    .relation-symbol {
      filter: drop-shadow(0 0 3px rgba(24, 144, 255, 0.3));
    }
  }

  &.highlighted {
    z-index: 15; // 高于选中状态

    .arrow-path {
      stroke: #52c41a;
      animation: dash 1s linear infinite;
    }

    .relation-symbol {
      filter: drop-shadow(0 0 3px rgba(82, 196, 26, 0.3));
      animation: pulse 1.5s ease-in-out infinite;
    }
  }

  @keyframes dash {
    to {
      stroke-dashoffset: -16;
    }
  }

  @keyframes pulse {
    0% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.1);
    }
    100% {
      transform: scale(1);
    }
  }
}
</style>